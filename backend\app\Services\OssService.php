<?php

namespace App\Services;

use OSS\OssClient;
use OSS\Core\OssException;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;

class OssService
{
    private $ossClient;
    private $bucket;
    private $cdnUrl;

    public function __construct()
    {
        $accessKeyId = config('filesystems.disks.oss.access_key_id');
        $accessKeySecret = config('filesystems.disks.oss.access_key_secret');
        $endpoint = config('filesystems.disks.oss.endpoint');
        $this->bucket = config('filesystems.disks.oss.bucket');
        $this->cdnUrl = config('filesystems.disks.oss.cdn_url');

        try {
            $this->ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);
        } catch (OssException $e) {
            Log::error('OSS客户端初始化失败: ' . $e->getMessage());
            throw new \Exception('OSS服务初始化失败');
        }
    }

    /**
     * 上传文件到OSS
     *
     * @param UploadedFile $file 上传的文件
     * @param string $directory 存储目录
     * @param string|null $filename 自定义文件名
     * @return array
     */
    public function uploadFile(UploadedFile $file, string $directory = 'uploads', ?string $filename = null): array
    {
        try {
            // 生成文件名
            if (!$filename) {
                $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
            }

            // 构建OSS对象键
            $objectKey = trim($directory, '/') . '/' . $filename;

            // 上传文件
            $result = $this->ossClient->uploadFile($this->bucket, $objectKey, $file->getPathname());

            // 构建访问URL
            $url = $this->cdnUrl ? rtrim($this->cdnUrl, '/') . '/' . $objectKey : $result['info']['url'];

            Log::info('文件上传到OSS成功', [
                'object_key' => $objectKey,
                'url' => $url,
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize()
            ]);

            return [
                'success' => true,
                'data' => [
                    'object_key' => $objectKey,
                    'url' => $url,
                    'original_name' => $file->getClientOriginalName(),
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType()
                ]
            ];

        } catch (OssException $e) {
            Log::error('OSS上传失败: ' . $e->getMessage(), [
                'error_code' => $e->getErrorCode(),
                'request_id' => $e->getRequestId()
            ]);

            return [
                'success' => false,
                'message' => 'OSS上传失败: ' . $e->getMessage()
            ];
        } catch (\Exception $e) {
            Log::error('文件上传异常: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => '文件上传失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 删除OSS文件
     *
     * @param string $objectKey OSS对象键
     * @return bool
     */
    public function deleteFile(string $objectKey): bool
    {
        try {
            $this->ossClient->deleteObject($this->bucket, $objectKey);
            Log::info('OSS文件删除成功: ' . $objectKey);
            return true;
        } catch (OssException $e) {
            Log::error('OSS文件删除失败: ' . $e->getMessage(), [
                'object_key' => $objectKey,
                'error_code' => $e->getErrorCode()
            ]);
            return false;
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param string $objectKey OSS对象键
     * @return bool
     */
    public function fileExists(string $objectKey): bool
    {
        try {
            return $this->ossClient->doesObjectExist($this->bucket, $objectKey);
        } catch (OssException $e) {
            Log::error('检查OSS文件存在性失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取文件访问URL
     *
     * @param string $objectKey OSS对象键
     * @return string
     */
    public function getFileUrl(string $objectKey): string
    {
        if ($this->cdnUrl) {
            return rtrim($this->cdnUrl, '/') . '/' . ltrim($objectKey, '/');
        }

        try {
            return $this->ossClient->getObjectUrl($this->bucket, $objectKey);
        } catch (OssException $e) {
            Log::error('获取OSS文件URL失败: ' . $e->getMessage());
            return '';
        }
    }
}
