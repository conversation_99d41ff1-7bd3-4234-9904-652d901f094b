<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Submission extends Model
{
    protected $fillable = [
        'user_id',
        'title',
        'abstract',
        'authors',
        'corresponding_author',
        'corresponding_email',
        'corresponding_phone',
        'institution',
        'department',
        'keywords',
        'submission_type',
        'research_field',
        'meeting_theme',
        'funding_info',
        'file_path',
        'original_filename',
        'file_size',
        'status',
        'review_comments',
        'submitted_at',
        'reviewed_at'
    ];

    protected $casts = [
        'authors' => 'array',
        'keywords' => 'array',
        'submitted_at' => 'datetime',
        'reviewed_at' => 'datetime',
    ];

    /**
     * 投稿状态常量
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_SUBMITTED = 'submitted';
    const STATUS_UNDER_REVIEW = 'under_review';
    const STATUS_ACCEPTED = 'accepted';
    const STATUS_REJECTED = 'rejected';
    const STATUS_REVISION_REQUIRED = 'revision_required';

    /**
     * 投稿类型常量
     */
    const TYPE_ORAL = 'oral';
    const TYPE_POSTER = 'poster';

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取状态标签
     */
    public function getStatusLabelAttribute(): string
    {
        $labels = [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_SUBMITTED => '已提交',
            self::STATUS_UNDER_REVIEW => '审稿中',
            self::STATUS_ACCEPTED => '已接收',
            self::STATUS_REJECTED => '已拒绝',
            self::STATUS_REVISION_REQUIRED => '需要修改',
        ];

        return $labels[$this->status] ?? '未知';
    }

    /**
     * 获取投稿类型标签
     */
    public function getSubmissionTypeLabelAttribute(): string
    {
        $labels = [
            self::TYPE_ORAL => '口头报告',
            self::TYPE_POSTER => '壁报展示',
        ];

        return $labels[$this->submission_type] ?? '未知';
    }

    /**
     * 提交投稿
     */
    public function submit(): void
    {
        $this->update([
            'status' => self::STATUS_SUBMITTED,
            'submitted_at' => now(),
        ]);
    }

    /**
     * 检查是否可以编辑
     */
    public function canEdit(): bool
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_REVISION_REQUIRED]);
    }

    /**
     * 检查是否已提交
     */
    public function isSubmitted(): bool
    {
        return !in_array($this->status, [self::STATUS_DRAFT]);
    }

    /**
     * 获取文件URL
     */
    public function getFileUrlAttribute(): ?string
    {
        if (!$this->file_path) {
            return null;
        }

        return asset('storage/' . $this->file_path);
    }
}
