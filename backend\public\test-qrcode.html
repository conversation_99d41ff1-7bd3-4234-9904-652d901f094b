<!DOCTYPE html>
<html>
<head>
    <title>测试微信二维码</title>
    <meta charset="utf-8">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            text-align: center;
        }
        .qrcode-container {
            margin: 20px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            max-width: 400px;
        }
        .qrcode {
            max-width: 200px;
            height: auto;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .info {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
            text-align: left;
        }
        .button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
        }
        .button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>微信二维码测试页面</h1>
    
    <div class="qrcode-container">
        <h3>二维码显示测试</h3>
        <div id="qrcode-display">
            <p>点击下方按钮获取二维码</p>
        </div>
        
        <button class="button" onclick="getQrCode()">获取二维码</button>
        <button class="button" onclick="testMockLogin()">测试模拟登录</button>
        <button class="button" onclick="testError('ip_whitelist')">测试IP白名单错误</button>
        <button class="button" onclick="testError('auth_error')">测试认证错误</button>
        
        <div id="info-display" class="info" style="display: none;">
            <h4>二维码信息：</h4>
            <div id="qr-info"></div>
        </div>
    </div>

    <script>
        let currentSceneStr = '';

        async function getQrCode() {
            try {
                const response = await fetch('/api/wechat/qrcode');
                const data = await response.json();
                
                if (data.success) {
                    // 显示二维码
                    const qrcodeDisplay = document.getElementById('qrcode-display');
                    qrcodeDisplay.innerHTML = `
                        <img src="${data.data.qrcode_base64}" alt="微信二维码" class="qrcode" />
                        <p>${data.data.note}</p>
                    `;
                    
                    // 显示信息
                    const infoDisplay = document.getElementById('info-display');
                    const qrInfo = document.getElementById('qr-info');
                    qrInfo.innerHTML = `
                        <p><strong>场景值:</strong> ${data.data.scene_str}</p>
                        <p><strong>类型:</strong> ${data.data.type}</p>
                        <p><strong>有效期:</strong> ${data.data.expires_in} 秒</p>
                        ${data.data.ip_whitelist_error ? '<p style="color: red;"><strong>IP白名单错误:</strong> 需要配置服务器IP</p>' : ''}
                        ${data.data.server_ip ? '<p><strong>服务器IP:</strong> ' + data.data.server_ip + '</p>' : ''}
                    `;
                    infoDisplay.style.display = 'block';
                    
                    currentSceneStr = data.data.scene_str;
                } else {
                    alert('获取二维码失败: ' + data.message);
                }
            } catch (error) {
                alert('请求失败: ' + error.message);
            }
        }

        async function testMockLogin() {
            if (!currentSceneStr) {
                alert('请先获取二维码');
                return;
            }

            try {
                const response = await fetch('/api/wechat/mock-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        scene_str: currentSceneStr
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    alert('模拟登录成功！\n用户: ' + data.data.user.name + '\nToken: ' + data.data.token.substring(0, 20) + '...');
                } else {
                    alert('模拟登录失败: ' + data.message);
                }
            } catch (error) {
                alert('请求失败: ' + error.message);
            }
        }

        async function testError(errorType) {
            try {
                const response = await fetch(`/api/wechat/test-error?type=${errorType}`);
                const data = await response.json();

                if (!data.success) {
                    // 显示错误信息
                    const qrcodeDisplay = document.getElementById('qrcode-display');
                    qrcodeDisplay.innerHTML = `
                        <div style="color: red; padding: 20px; border: 1px solid red; border-radius: 4px;">
                            <h4>错误测试: ${errorType}</h4>
                            <p><strong>错误信息:</strong> ${data.message}</p>
                            <p><strong>错误码:</strong> ${data.error_code || 'N/A'}</p>
                            <p><strong>错误类型:</strong> ${data.error_type}</p>
                        </div>
                    `;

                    // 显示建议
                    const infoDisplay = document.getElementById('info-display');
                    const qrInfo = document.getElementById('qr-info');
                    qrInfo.innerHTML = `
                        <h4>解决建议:</h4>
                        <ol>
                            ${data.suggestions.map(suggestion => '<li>' + suggestion + '</li>').join('')}
                        </ol>
                    `;
                    infoDisplay.style.display = 'block';
                } else {
                    alert('测试失败: 应该返回错误但返回了成功');
                }
            } catch (error) {
                alert('测试请求失败: ' + error.message);
            }
        }

        // 页面加载时自动获取二维码
        window.onload = function() {
            getQrCode();
        };
    </script>
</body>
</html>
