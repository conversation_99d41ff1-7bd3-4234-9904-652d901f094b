<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('material_downloads', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade')->comment('用户ID');
            $table->foreignId('material_id')->constrained('meeting_materials')->onDelete('cascade')->comment('资料ID');
            $table->string('ip_address')->nullable()->comment('下载IP地址');
            $table->string('user_agent')->nullable()->comment('用户代理');
            $table->timestamps();

            $table->index(['user_id', 'created_at']);
            $table->index(['material_id', 'created_at']);
            $table->unique(['user_id', 'material_id', 'created_at'], 'unique_user_material_download');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('material_downloads');
    }
};
