<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Announcement extends Model
{
    use SoftDeletes;

    /**
     * 指定表名
     */
    protected $table = 'dy_announcements_info';

    /**
     * 可批量赋值的字段
     */
    protected $fillable = [
        'title',
        'content',
        'user_id',
        'attachments',
    ];

    /**
     * 字段类型转换
     */
    protected $casts = [
        'attachments' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 关联发布者用户
     */
    public function publisher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 获取格式化的发布时间
     */
    public function getFormattedCreatedAtAttribute(): string
    {
        return $this->created_at ? $this->created_at->format('Y年m月d日 H:i') : '未知';
    }

    /**
     * 获取格式化的更新时间
     */
    public function getFormattedUpdatedAtAttribute(): string
    {
        return $this->updated_at ? $this->updated_at->format('Y年m月d日 H:i') : '未知';
    }

    /**
     * 获取附件数量
     */
    public function getAttachmentCountAttribute(): int
    {
        return $this->attachments ? count($this->attachments) : 0;
    }

    /**
     * 检查是否有附件
     */
    public function hasAttachments(): bool
    {
        return $this->attachments && count($this->attachments) > 0;
    }

    /**
     * 获取内容摘要
     */
    public function getContentSummary(int $length = 100): string
    {
        if (!$this->content) {
            return '暂无内容';
        }

        $content = strip_tags($this->content);
        return mb_strlen($content) > $length ? mb_substr($content, 0, $length) . '...' : $content;
    }
}
