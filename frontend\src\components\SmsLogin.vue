<template>
  <div class="sms-login-container">
    <el-form
      ref="smsFormRef"
      :model="smsForm"
      :rules="smsRules"
      label-width="0"
      size="large"
    >
      <el-form-item prop="phone">
        <div class="input-wrapper">
          <el-icon class="input-icon"><Phone /></el-icon>
          <el-input
            v-model="smsForm.phone"
            placeholder="请输入手机号"
            class="custom-input"
            maxlength="11"
            @input="onPhoneInput"
          />
        </div>
      </el-form-item>

      <el-form-item prop="code">
        <div class="code-input-container">
          <div class="input-wrapper code-input">
            <el-icon class="input-icon"><Message /></el-icon>
            <el-input
              v-model="smsForm.code"
              placeholder="请输入验证码"
              class="custom-input"
              maxlength="6"
            />
          </div>
          <el-button
            :disabled="!canSendCode || sendingCode"
            :loading="sendingCode"
            @click="sendVerificationCode"
            class="code-btn"
          >
            {{ codeButtonText }}
          </el-button>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          :loading="logging"
          @click="handleSmsLogin"
          class="login-btn"
          size="large"
        >
          {{ logging ? '登录中...' : '短信登录' }}
        </el-button>
      </el-form-item>
    </el-form>

    <div class="login-tips">
      <p>• 未注册的手机号将自动创建账号并登录</p>
      <p>• 已注册的手机号直接登录</p>
      <p>• 验证码有效期5分钟</p>
      <p>• 1分钟内只能发送一次验证码</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Phone, Message } from '@element-plus/icons-vue'
import { sendSmsCode, smsLogin } from '@/api/sms'

const emit = defineEmits(['login-success'])

// 表单数据
const smsForm = reactive({
  phone: '',
  code: ''
})

// 表单验证规则
const smsRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码必须是6位数字', trigger: 'blur' }
  ]
}

// 状态管理
const smsFormRef = ref()
const sendingCode = ref(false)
const logging = ref(false)
const countdown = ref(0)
let countdownTimer = null

// 计算属性
const canSendCode = computed(() => {
  return /^1[3-9]\d{9}$/.test(smsForm.phone) && countdown.value === 0
})

const codeButtonText = computed(() => {
  if (sendingCode.value) return '发送中...'
  if (countdown.value > 0) return `${countdown.value}s后重试`
  return '获取验证码'
})

// 手机号输入处理
const onPhoneInput = (value) => {
  // 只允许输入数字
  smsForm.phone = value.replace(/\D/g, '')
}

// 发送验证码
const sendVerificationCode = async () => {
  if (!canSendCode.value) return

  try {
    sendingCode.value = true

    const response = await sendSmsCode({
      phone: smsForm.phone,
      type: 'login'
    })

    if (response.success) {
      ElMessage.success('验证码发送成功')

      // 开发环境显示验证码
      if (response.data.dev_code) {
        ElMessage.info(`开发环境验证码: ${response.data.dev_code}`)
        smsForm.code = response.data.dev_code
      }

      // 开始倒计时
      startCountdown(60)
    } else {
      ElMessage.error(response.message)

      // 如果是频率限制，显示等待时间
      if (response.wait_seconds) {
        startCountdown(response.wait_seconds)
      }
    }
  } catch (error) {
    console.error('发送验证码失败:', error)

    // 处理API错误响应
    if (error.response && error.response.data) {
      ElMessage.error(error.response.data.message || '发送验证码失败')

      // 如果是频率限制，显示等待时间
      if (error.response.data.wait_seconds) {
        startCountdown(error.response.data.wait_seconds)
      }
    } else {
      ElMessage.error('发送验证码失败，请检查网络连接')
    }
  } finally {
    sendingCode.value = false
  }
}

// 开始倒计时
const startCountdown = (seconds) => {
  countdown.value = seconds
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }, 1000)
}

// 短信登录
const handleSmsLogin = async () => {
  try {
    // 表单验证
    const valid = await smsFormRef.value.validate()
    if (!valid) return

    logging.value = true

    const response = await smsLogin({
      phone: smsForm.phone,
      code: smsForm.code
    })

    if (response.success) {
      ElMessage.success('登录成功')

      // 触发登录成功事件
      emit('login-success', {
        user: response.data.user,
        token: response.data.token
      })
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    console.error('短信登录失败:', error)

    // 处理API错误响应
    if (error.response && error.response.data) {
      ElMessage.error(error.response.data.message || '登录失败')
    } else {
      ElMessage.error('登录失败，请检查网络连接')
    }
  } finally {
    logging.value = false
  }
}

// 清理定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style scoped>
.sms-login-container {
  width: 100%;
}

/* 输入框样式 */
.input-wrapper {
  position: relative;
  margin-bottom: 20px;
}

.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  z-index: 3;
}

.custom-input :deep(.el-input__wrapper) {
  padding-left: 45px;
  border-radius: 12px;
  border: 2px solid #e4e7ed;
  box-shadow: none;
  transition: all 0.3s ease;
}

.custom-input :deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

.custom-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 验证码输入容器 */
.code-input-container {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  width: 100%;
}

.code-input {
  flex: 1;
  margin-bottom: 0;
}

.code-btn {
  height: 50px;
  padding: 0 20px;
  border-radius: 12px;
  border: 2px solid #667eea;
  background: white;
  color: #667eea;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.code-btn:hover:not(:disabled) {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
}

.code-btn:disabled {
  border-color: #e4e7ed;
  color: #c0c4cc;
  background: #f5f7fa;
  cursor: not-allowed;
  transform: none;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* 提示信息 */
.login-tips {
  margin-top: 20px;
  padding: 15px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  border-left: 4px solid #667eea;
}

.login-tips p {
  margin: 5px 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.login-tips p:first-child {
  margin-top: 0;
}

.login-tips p:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .code-input-container {
    flex-direction: column;
    gap: 15px;
  }

  .code-input {
    width: 100%;
  }

  .code-btn {
    width: 100%;
    height: 45px;
  }

  .login-btn {
    height: 45px;
    font-size: 15px;
  }
}
</style>
