# 微信支付配置指南

## 1. 获取微信支付商户信息

### 1.1 申请微信支付商户号
1. 登录微信支付商户平台：https://pay.weixin.qq.com/
2. 完成商户入驻流程
3. 获取以下信息：
   - 商户号（mch_id）
   - 应用ID（app_id）
   - APIv3密钥

### 1.2 下载商户证书
1. 在商户平台下载API证书
2. 获取以下文件：
   - `apiclient_cert.pem` - 商户API证书
   - `apiclient_key.pem` - 商户API私钥
   - 证书序列号

## 2. 配置证书文件

### 2.1 上传证书文件
将下载的证书文件放置到以下目录：
```
backend/storage/cert/
├── apiclient_cert.pem
└── apiclient_key.pem
```

### 2.2 设置文件权限
```bash
chmod 600 storage/cert/apiclient_key.pem
chmod 644 storage/cert/apiclient_cert.pem
```

## 3. 更新环境配置

### 3.1 修改 .env 文件
```env
# 微信支付配置
WECHAT_PAY_APPID=你的应用ID
WECHAT_PAY_MCH_ID=你的商户号
WECHAT_PAY_SERIAL_NO=你的证书序列号
WECHAT_PAY_PRIVATE_KEY_PATH=cert/apiclient_key.pem
WECHAT_PAY_CERTIFICATE_PATH=cert/apiclient_cert.pem
WECHAT_PAY_NOTIFY_URL=https://yourdomain.com/api/payments/wechat/notify
```

### 3.2 配置APIv3密钥
在 `.env` 文件中添加：
```env
WECHAT_PAY_API_V3_KEY=你的APIv3密钥
```

## 4. 配置回调URL

### 4.1 在微信商户平台配置
1. 登录微信支付商户平台
2. 进入"产品中心" -> "开发配置"
3. 设置支付回调URL：`https://yourdomain.com/api/payments/wechat/notify`

### 4.2 确保回调URL可访问
- 必须是HTTPS协议
- 必须是公网可访问的域名
- 响应时间不超过5秒

## 5. 测试支付功能

### 5.1 测试环境
```bash
# 测试微信支付配置
curl -X GET "http://localhost:8000/api/test-wechat-pay"
```

### 5.2 创建测试订单
```bash
# 创建Native支付订单
curl -X POST "http://localhost:8000/api/payments/create" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "meeting_registration_id": 1,
    "payment_method": "wechat_native"
  }'
```

## 6. 生产环境部署

### 6.1 安全配置
- 确保证书文件权限正确
- 使用HTTPS协议
- 配置防火墙规则
- 定期更新证书

### 6.2 监控和日志
- 监控支付成功率
- 记录支付异常日志
- 设置告警机制

## 7. 常见问题

### 7.1 证书相关
- **问题**：证书路径错误
- **解决**：检查证书文件是否存在，路径是否正确

### 7.2 签名验证失败
- **问题**：回调签名验证失败
- **解决**：检查证书配置，确保使用正确的平台证书

### 7.3 回调超时
- **问题**：微信回调超时
- **解决**：优化回调处理逻辑，确保5秒内响应

## 8. 支持的支付方式

### 8.1 Native支付（扫码支付）
- 适用于PC端和移动端
- 用户扫描二维码完成支付
- 支付方式：`wechat_native`

### 8.2 JSAPI支付（公众号支付）
- 适用于微信公众号内
- 需要用户openid
- 支付方式：`wechat_jsapi`

## 9. 开发注意事项

1. **金额单位**：API接受元为单位，系统自动转换为分
2. **订单号**：确保商户订单号唯一性
3. **超时处理**：设置合理的订单超时时间
4. **重复支付**：防止重复创建相同订单
5. **异常处理**：完善的错误处理和日志记录

## 10. 相关文档

- [微信支付官方文档](https://pay.weixin.qq.com/wiki/doc/apiv3/index.shtml)
- [wechatpay/wechatpay SDK文档](https://github.com/wechatpay-im/wechatpay-php)
- [Laravel集成指南](https://laravel.com/docs)
