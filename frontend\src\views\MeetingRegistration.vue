<template>
  <div class="meeting-registration">
    <!-- 会议注册步骤 -->
    <el-steps :active="currentStep" finish-status="success" align-center>
      <el-step title="报名会议" />
      <el-step title="注册提示" />
      <el-step title="开票信息" />
    </el-steps>

    <!-- 步骤1: 报名会议 -->
    <div v-if="currentStep === 0" class="step-content">
      <el-card>
        <template #header>
          <div class="step-header">
            <el-icon color="#409eff"><Calendar /></el-icon>
            <span>报名会议</span>
          </div>
        </template>

        <!-- 已支付用户提示 -->
        <div v-if="isAlreadyPaid" class="already-paid-notice">
          <el-alert
            title="您已成功注册该会议"
            type="success"
            :closable="false"
            show-icon
          >
            <template #default>
              <div class="paid-info">
                <p><strong>注册类型：</strong>{{ getRegistrationTypeLabel(userRegistration?.registration_type) }}</p>
                <p><strong>支付金额：</strong>¥{{ userRegistration?.fee_amount }}</p>
                <p><strong>注册时间：</strong>{{ formatDate(userRegistration?.registered_at) }}</p>
                <p><strong>支付状态：</strong><el-tag type="success">已支付</el-tag></p>
              </div>
              <div class="paid-actions">
                <el-button type="primary" @click="goToProfile">查看我的注册</el-button>
                <el-button @click="goToDashboard">返回首页</el-button>
              </div>
            </template>
          </el-alert>
        </div>

        <!-- 待支付用户提示 -->
        <div v-if="userRegistration && userRegistration.payment_status === 'pending'" class="pending-notice">
          <el-alert
            title="您有待支付的注册记录"
            type="warning"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>您可以修改注册类型，费用将根据新类型重新计算。</p>
              <p><strong>当前注册类型：</strong>{{ getRegistrationTypeLabel(userRegistration.registration_type) }}</p>
              <p><strong>当前费用：</strong>¥{{ userRegistration.fee_amount }}</p>
            </template>
          </el-alert>
        </div>

        <!-- 未支付用户的注册选项 -->
        <div v-if="!isAlreadyPaid" class="meeting-options">
          <el-card
            class="meeting-option"
            :class="{ 'selected': selectedMeeting === 'expert' }"
            @click="selectMeeting('expert')"
          >
            <div class="option-content">
              <h3>正式代表</h3>
              <div class="price">
                CNY {{ currentMeetingData ? (meetingStore.calculateFee(currentMeetingData, 'expert') || 0).toFixed(2) : '0.00' }}
              </div>
              <div class="price-note" v-if="currentMeetingData">
                <span v-if="isEarlyBird" class="early-bird">9月30日前</span>
                <span v-else class="normal-price">10月1日后</span>
              </div>
              <el-icon v-if="selectedMeeting === 'expert'" class="check-icon"><Check /></el-icon>
            </div>
          </el-card>

          <el-card
            class="meeting-option"
            :class="{ 'selected': selectedMeeting === 'student' }"
            @click="selectMeeting('student')"
          >
            <div class="option-content">
              <h3>学生代表</h3>
              <div class="price">
                CNY {{ currentMeetingData ? (meetingStore.calculateFee(currentMeetingData, 'student') || 0).toFixed(2) : '0.00' }}
              </div>
              <div class="price-note" v-if="currentMeetingData">
                <span v-if="isEarlyBird" class="early-bird">9月30日前</span>
                <span v-else class="normal-price">10月1日后</span>
              </div>
              <el-icon v-if="selectedMeeting === 'student'" class="check-icon"><Check /></el-icon>
            </div>
          </el-card>

          <el-card
            class="meeting-option"
            :class="{ 'selected': selectedMeeting === 'corporate' }"
            @click="selectMeeting('corporate')"
          >
            <div class="option-content">
              <h3>企业代表</h3>
              <div class="price">
                CNY {{ currentMeetingData ? (meetingStore.calculateFee(currentMeetingData, 'corporate') || 0).toFixed(2) : '0.00' }}
              </div>
              <div class="price-note" v-if="currentMeetingData">
                <span v-if="isEarlyBird" class="early-bird">9月30日前</span>
                <span v-else class="normal-price">10月1日后</span>
              </div>
              <el-icon v-if="selectedMeeting === 'corporate'" class="check-icon"><Check /></el-icon>
            </div>
          </el-card>
        </div>

        <div class="note">
          <el-icon><InfoFilled /></el-icon>
          <span>请选择上述方案中的1个方案，方可进行缴费</span>
        </div>

        <div class="step-actions">
          <el-button
            type="primary"
            :disabled="!selectedMeeting"
            @click="nextStep"
          >
            下一步
          </el-button>
        </div><!-- 结束未支付用户的注册选项 -->
    
      </el-card>
    </div>

    <!-- 步骤2: 注册提示 (仅未支付用户可见) -->
    <div v-if="currentStep === 1 && !isAlreadyPaid" class="step-content">
      <el-card>
        <template #header>
          <div class="step-header">
            <el-icon color="#409eff"><Document /></el-icon>
            <span>注册提示</span>
            <el-tag
              :type="userStore.isPaidUser ? 'success' : 'warning'"
              effect="dark"
              class="user-status-tag"
            >
              {{ userStore.userTypeLabel }}
            </el-tag>
          </div>
        </template>

        <!-- 用户状态提示 -->
        <div class="user-status-notice" v-if="!userStore.isPaidUser">
          <el-alert
            title="首次付费用户提示"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>您当前是免费用户，完成本次会议注册并支付后，您将自动升级为付费用户，享受以下权益：</p>
              <ul>
                <li>✅ 优先获得会议通知和资料</li>
                <li>✅ 参与会议互动和讨论</li>
                <li>✅ 获得会议录像和资料下载权限</li>
                <li>✅ 享受后续会议优惠价格</li>
              </ul>
            </template>
          </el-alert>
        </div>

        <div class="paid-user-notice" v-else>
          <el-alert
            title="付费用户专享"
            type="success"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>欢迎回来！作为{{ userStore.userLevelLabel }}，您已享受以下权益：</p>
              <ul>
                <li>✅ 已参与 {{ userStore.user?.paid_meetings_count || 0 }} 场付费会议</li>
                <li>✅ 累计贡献 ¥{{ userStore.user?.total_paid_amount || '0.00' }}</li>
                <li>✅ 享受会员专属服务和优惠</li>
              </ul>
            </template>
          </el-alert>
        </div>

        <!-- 注册费用表格 -->
        <div class="registration-table">
          <table class="fee-table">
            <thead>
              <tr>
                <th rowspan="2">会员人员身份</th>
                <th colspan="2">注册费</th>
              </tr>
              <tr>
                <th>9月30日（含）前</th>
                <th>10月1日及以后</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="currentMeetingData">
                <td>正式代表</td>
                <td>{{ currentMeetingData.expert_fee_early }}元/人</td>
                <td>{{ currentMeetingData.expert_fee_normal }}元/人</td>
              </tr>
              <tr v-if="currentMeetingData">
                <td>学生代表</td>
                <td>{{ currentMeetingData.student_fee_early }}元/人</td>
                <td>{{ currentMeetingData.student_fee_normal }}元/人</td>
              </tr>
              <tr v-if="currentMeetingData">
                <td>企业代表</td>
                <td>{{ currentMeetingData.corporate_fee_early }}元/人</td>
                <td>{{ currentMeetingData.corporate_fee_normal }}元/人</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 注册说明 -->
        <div class="registration-info" v-if="false">
          <h4>注册说明：</h4>
          <p>1. 会议注册费包含：会议资料包、会议期间茶歇费用。</p>
          <p>2. 会议费用需在注册时缴纳（含发票开具），会议现场不接受现金。</p>
          <p>3. 本次会议提供午餐及茶歇服务，住宿费用自理，会议组委会可协助预订酒店。</p>
          <p>4. 会议注册费一经缴纳不予退还，如有特殊情况请提前联系会议组委会。</p>
          <p>5. 会议期间：线上不受地域限制，线下会场有限，注册请尽早。</p>
          <p>6. 正式报名注册：网站开放 13617175558</p>
          <p>7. 本次会议费用为人民币，支付方式：支付宝/微信。</p>
          <p>8. 最终解释权归主办方所有。</p>
        </div>

        <!-- 联系信息 -->
        <div class="contact-info"  v-if="false">
          <h4>联系方式：</h4>
          <p>开票方：天津威海会议文化传播有限公司</p>
          <p>开户行：中国建设银行天津滨海新区中新生态城支行</p>
          <p>开户账号：13570214615</p>
          <p>联系电话：16473100968</p>
          <p>传真号码同电话本次会议名称，具体开票信息请主办方联系。</p>
        </div>

        <div class="important-note"  v-if="false">
          <p><strong>附件：</strong></p>
          <p style="color: #e6a23c;"><strong>高血压学术会议：最新医学研究支持</strong></p>
        </div>

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" @click="nextStep">下一步：填写开票信息</el-button>
        </div>
      </el-card>
    </div>

    <!-- 步骤3: 开票信息 (仅未支付用户可见) -->
    <div v-if="currentStep === 2 && !isAlreadyPaid" class="step-content">
      <el-card>
        <template #header>
          <div class="step-header">
            <el-icon color="#409eff"><Document /></el-icon>
            <span>开票信息</span>
          </div>
        </template>

        <!-- 开票信息表单 -->
        <div class="invoice-form">
          <el-form :model="invoiceForm" :rules="invoiceRules" ref="invoiceFormRef" label-width="120px">
            <!-- 发票类型 -->
            <el-form-item label="发票类型" prop="invoice_type">
              <el-radio-group v-model="invoiceForm.invoice_type" @change="handleInvoiceTypeChange">
                <el-radio value="personal">个人</el-radio>
                <el-radio value="company">企业</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 发票抬头 -->
            <el-form-item label="发票抬头" prop="invoice_title">
              <el-input v-model="invoiceForm.invoice_title" placeholder="请输入发票抬头" />
            </el-form-item>

            <!-- 企业信息（仅企业发票显示） -->
            <template v-if="invoiceForm.invoice_type === 'company'">
              <el-form-item label="纳税人识别号" prop="tax_number">
                <el-input v-model="invoiceForm.tax_number" placeholder="请输入纳税人识别号" />
              </el-form-item>

              <el-form-item label="公司电话" prop="company_phone">
                <el-input v-model="invoiceForm.company_phone" placeholder="请输入公司电话" />
              </el-form-item>

              <el-form-item label="开户银行" prop="bank_name">
                <el-input v-model="invoiceForm.bank_name" placeholder="请输入开户银行" />
              </el-form-item>

              <el-form-item label="银行账号" prop="bank_account">
                <el-input v-model="invoiceForm.bank_account" placeholder="请输入银行账号" />
              </el-form-item>
            </template>

            <!-- 联系信息 -->
            <el-divider content-position="left">联系信息</el-divider>

            <el-form-item label="联系人" prop="contact_name">
              <el-input v-model="invoiceForm.contact_name" placeholder="请输入联系人姓名" />
            </el-form-item>

            <el-form-item label="联系电话" prop="contact_phone">
              <el-input v-model="invoiceForm.contact_phone" placeholder="请输入联系电话" />
            </el-form-item>

            <el-form-item label="联系邮箱" prop="contact_email">
              <el-input v-model="invoiceForm.contact_email" placeholder="请输入联系邮箱（可选）" />
            </el-form-item>

            <!-- 保存为默认 -->
            <el-form-item>
              <el-checkbox v-model="invoiceForm.is_default">保存为默认开票信息</el-checkbox>
            </el-form-item>
          </el-form>

          <!-- 历史开票信息选择 -->
          <div v-if="invoiceInfoList.length > 0" class="invoice-history">
            <el-divider content-position="left">选择历史开票信息</el-divider>
            <el-radio-group v-model="selectedInvoiceId" @change="handleSelectInvoice">
              <div v-for="invoice in invoiceInfoList" :key="invoice.id" class="invoice-item">
                <el-radio :value="invoice.id">
                  <div class="invoice-info">
                    <div class="invoice-title">
                      {{ invoice.invoice_title }}
                      <el-tag v-if="invoice.is_default" type="success" size="small">默认</el-tag>
                      <el-tag :type="invoice.invoice_type === 'company' ? 'warning' : 'info'" size="small">
                        {{ invoice.invoice_type === 'company' ? '企业' : '个人' }}
                      </el-tag>
                    </div>
                    <div class="invoice-details">
                      <span>联系电话：{{ invoice.contact_phone }}</span>
                      <span v-if="invoice.contact_email">邮箱：{{ invoice.contact_email }}</span>
                    </div>
                  </div>
                </el-radio>
              </div>
            </el-radio-group>
          </div>
        </div>

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" :loading="loading" @click="confirmRegistration">确认缴费</el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Calendar, Document, Check, InfoFilled } from '@element-plus/icons-vue'
import { useMeetingStore } from '@/stores/meeting'
import { useUserStore } from '@/stores/user'
import { getInvoiceInfoList, createInvoiceInfo } from '@/api/invoice'

const router = useRouter()
const meetingStore = useMeetingStore()
const userStore = useUserStore()

const currentStep = ref(0)
const selectedMeeting = ref('')
const currentMeetingData = ref(null)
const loading = ref(false)
const userRegistration = ref(null)
const isAlreadyPaid = ref(false)

// 开票信息相关
const invoiceFormRef = ref(null)
const invoiceInfoList = ref([])
const selectedInvoiceId = ref(null)
const invoiceForm = reactive({
  invoice_type: 'personal',
  invoice_title: '',
  tax_number: '',
  company_phone: '',
  bank_name: '',
  bank_account: '',
  contact_name: '',
  contact_phone: '',
  contact_email: '',
  is_default: false
})

// 开票信息验证规则
const invoiceRules = {
  invoice_type: [
    { required: true, message: '请选择发票类型', trigger: 'change' }
  ],
  invoice_title: [
    { required: true, message: '请输入发票抬头', trigger: 'blur' }
  ],
  tax_number: [
    { required: true, message: '请输入纳税人识别号', trigger: 'blur' }
  ],
  contact_phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  contact_email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 计算属性
const selectedMeetingPrice = computed(() => {
  if (!currentMeetingData.value || !selectedMeeting.value) return 0
  return meetingStore.calculateFee(currentMeetingData.value, selectedMeeting.value)
})

const isEarlyBird = computed(() => {
  if (!currentMeetingData.value) return false
  return new Date() < new Date(currentMeetingData.value.early_deadline)
})

// 初始化数据
onMounted(async () => {
  await loadMeetingData()
  await loadInvoiceInfoList()
})

const loadMeetingData = async () => {
  loading.value = true
  try {
    // 获取会议列表
    const result = await meetingStore.fetchMeetings()
    if (result.success && meetingStore.meetings.length > 0) {
      // 使用第一个会议作为当前会议
      currentMeetingData.value = meetingStore.meetings[0]

      // 检查用户是否已经支付过该会议
      await checkUserRegistrationStatus()
    }
  } catch (error) {
    ElMessage.error('加载会议数据失败')
  } finally {
    loading.value = false
  }
}

// 检查用户注册状态
const checkUserRegistrationStatus = async () => {
  if (!currentMeetingData.value) return

  try {
    // 获取用户的注册记录
    await meetingStore.fetchMyRegistrations()

    // 查找当前会议的注册记录
    const registration = meetingStore.myRegistrations.find(
      reg => reg.meeting_id === currentMeetingData.value.id
    )

    if (registration) {
      userRegistration.value = registration
      // 只有已支付的用户才不能修改注册类型
      isAlreadyPaid.value = registration.payment_status === 'paid'

      if (isAlreadyPaid.value) {
        console.log('用户已支付该会议，不显示注册选项')
      } else if (registration.payment_status === 'pending') {
        // 如果是待支付状态，预选之前的注册类型
        selectedMeeting.value = registration.registration_type
        console.log('用户有待支付记录，预选注册类型:', registration.registration_type)
      }
    }
  } catch (error) {
    console.error('检查用户注册状态失败:', error)
  }
}

const selectMeeting = (type) => {
  selectedMeeting.value = type
}

// 导航方法
const goToProfile = () => {
  router.push('/profile')
}

const goToDashboard = () => {
  router.push('/')
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取注册类型标签
const getRegistrationTypeLabel = (type) => {
  const labels = {
    'expert': '正式代表',
    'student': '学生代表',
    'corporate': '企业代表'
  }
  return labels[type] || type
}

const nextStep = () => {
  // 已支付用户不能进入下一步
  if (isAlreadyPaid.value) {
    ElMessage.warning('您已成功注册该会议，无需重复注册')
    return
  }

  if (currentStep.value === 0 && selectedMeeting.value) {
    currentStep.value = 1
  } else if (currentStep.value === 1) {
    currentStep.value = 2
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 加载开票信息列表
const loadInvoiceInfoList = async () => {
  try {
    const response = await getInvoiceInfoList()
    if (response.success) {
      invoiceInfoList.value = response.data

      // 如果有默认开票信息，自动选中
      const defaultInvoice = response.data.find(item => item.is_default)
      if (defaultInvoice) {
        selectedInvoiceId.value = defaultInvoice.id
        handleSelectInvoice(defaultInvoice.id)
      }
    }
  } catch (error) {
    console.error('加载开票信息失败:', error)
  }
}

// 发票类型改变处理
const handleInvoiceTypeChange = (type) => {
  if (type === 'personal') {
    // 个人发票清空企业信息
    invoiceForm.tax_number = ''
    invoiceForm.company_phone = ''
    invoiceForm.bank_name = ''
    invoiceForm.bank_account = ''
  }
}

// 选择历史开票信息
const handleSelectInvoice = (invoiceId) => {
  const invoice = invoiceInfoList.value.find(item => item.id === invoiceId)
  if (invoice) {
    Object.assign(invoiceForm, {
      invoice_type: invoice.invoice_type,
      invoice_title: invoice.invoice_title,
      tax_number: invoice.tax_number || '',
      company_phone: invoice.company_phone || '',
      bank_name: invoice.bank_name || '',
      bank_account: invoice.bank_account || '',
      contact_name: invoice.contact_name || '',
      contact_phone: invoice.contact_phone,
      contact_email: invoice.contact_email || '',
      is_default: invoice.is_default
    })
  }
}

// 保存开票信息
const saveInvoiceInfo = async () => {
  try {
    // 清理数据，个人发票时清空企业字段
    const cleanData = { ...invoiceForm }
    if (cleanData.invoice_type === 'personal') {
      cleanData.tax_number = null
      cleanData.company_phone = null
      cleanData.bank_name = null
      cleanData.bank_account = null
    }

    const response = await createInvoiceInfo(cleanData)
    if (response.success) {
      ElMessage.success('开票信息保存成功')
      await loadInvoiceInfoList()
      return true
    }
  } catch (error) {
    console.error('保存开票信息失败:', error)
    ElMessage.error('保存开票信息失败')
    return false
  }
}

// 检查用户个人信息是否完整
const checkUserProfile = async () => {
  // 第一次检查
  if (userStore.hasCompleteProfile) {
    return { success: true }
  }

  // 第一次检查失败，刷新用户信息后再次检查
  //ElMessage.info('正在验证个人信息...')

  try {
    const result = await userStore.getCurrentUser()
    if (!result.success) {
      return {
        success: false,
        message: '获取用户信息失败，请稍后重试'
      }
    }

    // 刷新后再次检查
    if (userStore.hasCompleteProfile) {
      return { success: true }
    }

    // 仍然不完整，返回缺失字段信息
    const missingFields = userStore.getMissingProfileFields
    return {
      success: false,
      message: `请先完善个人信息：${missingFields.join('、')}`,
      missingFields
    }
  } catch (error) {
    console.error('检查用户信息失败:', error)
    return {
      success: false,
      message: '验证个人信息时发生错误，请稍后重试'
    }
  }
}

const confirmRegistration = async () => {
  if (!currentMeetingData.value || !selectedMeeting.value) {
    ElMessage.error('请选择会议类型')
    return
  }

  // 检查个人信息是否完整
  const profileCheck = await checkUserProfile()
  if (!profileCheck.success) {
    ElMessage.warning(profileCheck.message)
    router.push('/profile')
    return
  }

  // 验证开票信息
  if (!invoiceFormRef.value) {
    ElMessage.error('开票信息表单未初始化')
    return
  }

  try {
    await invoiceFormRef.value.validate()
  } catch (error) {
    ElMessage.error('请完善开票信息')
    return
  }

  loading.value = true
  try {
    // 如果没有选择历史开票信息，先保存当前开票信息
    if (!selectedInvoiceId.value) {
      const saveResult = await saveInvoiceInfo()
      if (!saveResult) {
        loading.value = false
        return
      }
    }

    const registrationData = {
      registration_type: selectedMeeting.value,
      notes: `会议注册 - ${getRegistrationTypeLabel(selectedMeeting.value)}`
    }

    const result = await meetingStore.registerMeeting(
      currentMeetingData.value.id,
      registrationData
    )

    if (result.success) {
      // 判断是新注册还是更新注册
      const isUpdate = userRegistration.value && userRegistration.value.payment_status === 'pending'
      const message = isUpdate ? '注册信息已更新，请完成支付' : '注册信息已提交，请完成支付以确认注册'

      ElMessage.success(message)

      // 跳转到支付页面，携带开票信息ID
      router.push({
        name: 'PaymentProcess',
        query: {
          registrationId: result.data.id,
          meetingId: currentMeetingData.value.id,
          invoiceId: selectedInvoiceId.value || invoiceInfoList.value.find(item => item.is_default)?.id
        }
      })
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('注册失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.meeting-registration {
  padding: 20px;
  min-height: calc(100vh - 60px);
  background-color: #f5f7fa;
}

.step-content {
  margin-top: 30px;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  justify-content: space-between;
}

.user-status-tag {
  margin-left: auto;
}

.user-status-notice,
.paid-user-notice {
  margin-bottom: 20px;
}

.user-status-notice ul,
.paid-user-notice ul {
  margin: 10px 0 0 20px;
  padding: 0;
}

.user-status-notice li,
.paid-user-notice li {
  margin: 5px 0;
  color: #606266;
}

.already-paid-notice {
  margin-bottom: 20px;
}

.pending-notice {
  margin-bottom: 20px;
}

.pending-notice .el-alert__content p {
  margin: 5px 0;
}

.paid-info {
  margin: 15px 0;
}

.paid-info p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

.paid-info strong {
  color: #303133;
  font-weight: 600;
}

.paid-actions {
  margin-top: 20px;
  display: flex;
  gap: 12px;
}

/* 开票信息样式 */
.invoice-form {
  margin-bottom: 20px;
}

.invoice-history {
  margin-top: 20px;
}

.invoice-item {
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s;
}

.invoice-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.invoice-info {
  margin-left: 24px;
}

.invoice-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.invoice-details {
  font-size: 12px;
  color: #909399;
  display: flex;
  gap: 16px;
}

.meeting-options {
  display: flex;
  gap: 20px;
  margin: 20px 0;
}

.meeting-option {
  flex: 1;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.meeting-option:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.meeting-option.selected {
  border: 2px solid #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.option-content {
  text-align: center;
  padding: 20px;
}

.option-content h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.price {
  font-size: 20px;
  font-weight: bold;
  color: #e6a23c;
}

.check-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  color: #67c23a;
  font-size: 20px;
}

.price-note {
  font-size: 12px;
  margin-top: 5px;
}

.early-bird {
  color: #e6a23c;
  background: #fdf6ec;
  padding: 2px 6px;
  border-radius: 4px;
}

.normal-price {
  color: #909399;
}

.note {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  margin: 20px 0;
}

.step-actions {
  text-align: center;
  margin-top: 30px;
}

.registration-table {
  margin: 20px 0;
}

.fee-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #dcdfe6;
}

.fee-table th,
.fee-table td {
  border: 1px solid #dcdfe6;
  padding: 12px;
  text-align: center;
}

.fee-table th {
  background-color: #f5f7fa;
  font-weight: bold;
  color: #303133;
}

.fee-table td {
  color: #606266;
}

.registration-info,
.contact-info {
  margin: 20px 0;
}

.registration-info h4,
.contact-info h4 {
  color: #303133;
  margin-bottom: 10px;
}

.registration-info p,
.contact-info p {
  margin: 5px 0;
  color: #606266;
  line-height: 1.6;
}

.important-note {
  margin: 20px 0;
  padding: 15px;
  background-color: #fdf6ec;
  border-left: 4px solid #e6a23c;
}

.important-note p {
  margin: 5px 0;
  color: #606266;
}
</style>
