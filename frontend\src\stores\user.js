import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as authApi from '@/api/auth'
import * as userApi from '@/api/user'

export const useUserStore = defineStore('user', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || '')

  const isLoggedIn = computed(() => !!token.value)

  // 用户付费状态相关计算属性
  const isPaidUser = computed(() => user.value?.is_paid_user || false)
  const userLevel = computed(() => {
    if (!user.value?.is_paid_user) return 'free'

    const meetingsCount = user.value.paid_meetings_count || 0
    if (meetingsCount >= 5) return 'vip'
    if (meetingsCount >= 2) return 'premium'
    return 'paid'
  })

  const userLevelLabel = computed(() => {
    const level = userLevel.value
    const labels = {
      'free': '免费用户',
      'paid': '付费用户',
      'premium': '高级用户',
      'vip': 'VIP用户'
    }
    return labels[level] || '普通用户'
  })

  const userTypeLabel = computed(() => {
    return isPaidUser.value ? '付费用户' : '免费用户'
  })

  // 检查个人信息是否完整
  const hasCompleteProfile = computed(() => {
    if (!user.value) return false
    return !!(user.value.real_name &&
              user.value.gender &&
              user.value.organization &&
              user.value.job_title &&
              user.value.email)
  })

  // 获取缺失的个人信息字段
  const getMissingProfileFields = computed(() => {
    if (!user.value) return []

    const missing = []
    if (!user.value.real_name) missing.push('真实姓名')
    if (!user.value.gender) missing.push('性别')
    if (!user.value.organization) missing.push('工作单位')
    if (!user.value.job_title) missing.push('职称')
    if (!user.value.email) missing.push('邮箱')

    return missing
  })

  const login = async (credentials) => {
    try {
      const response = await authApi.login(credentials)
      user.value = response.user
      token.value = response.token
      localStorage.setItem('token', response.token)
      return { success: true }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  const logout = async () => {
      user.value = null
      token.value = ''
      localStorage.removeItem('token')

    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出API调用失败:', error)
    } finally {
  
    }
  }

  const register = async (userData) => {
    try {
      const response = await authApi.register(userData)
      user.value = response.user
      token.value = response.token
      localStorage.setItem('token', response.token)
      return { success: true }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  const getCurrentUser = async () => {
    try {
      const response = await authApi.getCurrentUser()
      user.value = response.user
      return { success: true }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  const updateProfile = async (profileData) => {
    try {
      const response = await userApi.updateUserProfile(profileData)
      user.value = response.user
      return { success: true, message: response.message }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  const changePassword = async (passwordData) => {
    try {
      const response = await userApi.changePassword(passwordData)
      return { success: true, message: response.message }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  const bindPhone = async (bindData) => {
    try {
      const response = await userApi.bindPhone(bindData)
      user.value = response.user
      return { success: true, message: response.message }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  const sendSmsCode = async (phone) => {
    try {
      const response = await userApi.sendSmsCode(phone)
      return { success: true, message: response.message }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  const bindWechat = async () => {
    try {
      const response = await userApi.bindWechat()
      user.value = response.user
      return { success: true, message: response.message }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  return {
    user,
    token,
    isLoggedIn,
    isPaidUser,
    userLevel,
    userLevelLabel,
    userTypeLabel,
    hasCompleteProfile,
    getMissingProfileFields,
    login,
    logout,
    register,
    getCurrentUser,
    updateProfile,
    changePassword,
    bindPhone,
    sendSmsCode,
    bindWechat
  }
})
