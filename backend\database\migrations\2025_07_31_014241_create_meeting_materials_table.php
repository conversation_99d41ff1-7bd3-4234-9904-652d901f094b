<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meeting_materials', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('资料标题');
            $table->text('description')->nullable()->comment('资料描述');
            $table->string('category')->default('general')->comment('资料分类');
            $table->string('file_path')->comment('文件路径');
            $table->string('original_filename')->comment('原始文件名');
            $table->bigInteger('file_size')->comment('文件大小(字节)');
            $table->string('file_type')->comment('文件类型');
            $table->integer('download_count')->default(0)->comment('下载次数');
            $table->boolean('is_public')->default(false)->comment('是否公开(false=仅付费用户)');
            $table->foreignId('meeting_id')->nullable()->constrained()->onDelete('set null')->comment('关联会议ID');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamps();

            $table->index(['category', 'is_active']);
            $table->index(['meeting_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meeting_materials');
    }
};
