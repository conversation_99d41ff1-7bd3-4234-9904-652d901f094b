<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $announcement->title ?? '公告详情' }} - 第九届光谱成像研讨会</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --dark-blue-1: #0a1f3d;
            --dark-blue-2: #1a3b6d;
            --light-blue: #a8d0ff;
            --icon-bg: rgba(168, 208, 255, 0.15);
        }
        body {
           	background: url(/images/bg.jpg);
            color: white;
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .header-section {
            padding: 3rem 0 1.5rem;
            text-align: center;
            flex: 0 0 auto;
        }
        .header-section h1 {
            font-weight: 700;
            margin-bottom: 0.8rem;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .header-section .subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            letter-spacing: 0.5px;
        }
        .announcement-meta {
            background: rgba(0,0,0,0.2);
            padding: 1rem;
            margin: 1rem auto;
            border-radius: 10px;
            max-width: 800px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        .meta-item {
            display: flex;
            align-items: center;
            margin: 0.5rem 0;
        }
        .meta-item i {
            margin-right: 0.5rem;
            color: var(--light-blue);
        }
        .content-section {
            flex: 1 0 auto;
            padding: 2rem 0;
        }
        .announcement-content {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(0,0,0,0.3);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .content-text {
            line-height: 1.8;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }
        .content-text img {
            max-width: 100%;
        }
        .content-text p {
            margin-bottom: 1rem;
        }
        .attachments-section {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255,255,255,0.2);
        }
        .attachments-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--light-blue);
        }
        .attachment-item {
            background: rgba(168, 208, 255, 0.1);
            border: 1px solid rgba(168, 208, 255, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }
        .attachment-item:hover {
            background: rgba(168, 208, 255, 0.2);
            transform: translateY(-2px);
        }
        .attachment-info {
            display: flex;
            align-items: center;
        }
        .attachment-info i {
            font-size: 1.5rem;
            margin-right: 1rem;
            color: var(--light-blue);
        }
        .attachment-details h6 {
            margin: 0;
            font-weight: 600;
        }
        .attachment-details small {
            opacity: 0.8;
        }
        .download-btn {
            background: var(--light-blue);
            color: var(--dark-blue-1);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .download-btn:hover {
            background: white;
            transform: scale(1.05);
        }
        .publisher-info {
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: center;
        }
        .publisher-info h5 {
            color: var(--light-blue);
            margin-bottom: 0.5rem;
        }
        .back-button {
            position: fixed;
            top: 2rem;
            left: 2rem;
            background: rgba(168, 208, 255, 0.2);
            border: 1px solid rgba(168, 208, 255, 0.5);
            color: white;
            padding: 0.8rem 1.2rem;
            border-radius: 50px;
            text-decoration: none;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .back-button:hover {
            background: rgba(168, 208, 255, 0.3);
            color: white;
            transform: translateY(-2px);
        }
        .footer-section {
            flex: 0 0 auto;
            padding: 1.5rem 0;
            text-align: center;
            color: rgba(255,255,255,0.7);
            font-size: 0.9rem;
            border-top: 1px solid rgba(255,255,255,0.1);
        }
        @media (max-width: 767px) {
            .announcement-meta {
                flex-direction: column;
                text-align: center;
            }
            .announcement-content {
                margin: 0 1rem;
                padding: 1.5rem;
            }
            .back-button {
                top: 1rem;
                left: 1rem;
                padding: 0.6rem 1rem;
            }
            .attachment-item {
                flex-direction: column;
                text-align: center;
            }
            .attachment-info {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 返回按钮 -->
    <a href="javascript:history.back()" class="back-button">
        <i class="fas fa-arrow-left"></i> 返回
    </a>

    <!-- 头部区域 -->
    <section class="header-section">
        <div class="container">
            <h1>{{ $announcement->title ?? '公告标题' }}</h1>
            <p class="subtitle">公告详情</p>
            <div class="announcement-meta">
                <div class="meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>发布时间：{{ $announcement->created_at ? $announcement->created_at->format('Y年m月d日 H:i') : '未知' }}</span>
                </div>
                @if($announcement->updated_at && $announcement->updated_at != $announcement->created_at)
                <div class="meta-item">
                    <i class="fas fa-edit"></i>
                    <span>更新时间：{{ $announcement->updated_at->format('Y年m月d日 H:i') }}</span>
                </div>
                @endif
                @if(isset($publisher))
                <div class="meta-item">
                    <i class="fas fa-user"></i>
                    <span>发布者：{{ $publisher->username ?? $publisher->name ?? '系统管理员' }}</span>
                </div>
                @endif
            </div>
        </div>
    </section>

    <!-- 内容区域 -->
    <section class="content-section">
        <div class="container">
            <div class="announcement-content">
                <!-- 公告内容 -->
                <div class="content-text">
                    @if($announcement->content)
                        {!! $announcement->content !!}
                    @else
                        <p class="text-muted">暂无内容</p>
                    @endif
                </div>

                <!-- 附件区域 -->
                @if($announcement->attachments && count(json_decode($announcement->attachments, true)) > 0)
                <div class="attachments-section">
                    <div class="attachments-title">
                        <i class="fas fa-paperclip"></i> 相关附件
                    </div>
                    @foreach(json_decode($announcement->attachments, true) as $attachment)
                    <div class="attachment-item">
                        <div class="attachment-info">
                            <i class="fas fa-file-alt"></i>
                            <div class="attachment-details">
                                <h6>{{ $attachment['name'] ?? '附件文件' }}</h6>
                                <small>
                                    @if(isset($attachment['size']))
                                        大小：{{ number_format($attachment['size'] / 1024, 2) }} KB
                                    @endif
                                    @if(isset($attachment['type']))
                                        | 类型：{{ $attachment['type'] }}
                                    @endif
                                </small>
                            </div>
                        </div>
                        @if(isset($attachment['url']) || isset($attachment['path']))
                        <button class="download-btn" onclick="downloadAttachment('{{ $attachment['url'] ?? $attachment['path'] }}', '{{ $attachment['name'] ?? 'attachment' }}')">
                            <i class="fas fa-download"></i> 下载
                        </button>
                        @endif
                    </div>
                    @endforeach
                </div>
                @endif

                <!-- 发布者信息 -->
                @if(isset($publisher))
                <div class="publisher-info">
                    <h5>发布者信息</h5>
                    <p>{{ $publisher->username ?? $publisher->name ?? '系统管理员' }}</p>
                    @if(isset($publisher->email))
                    <small><i class="fas fa-envelope"></i> {{ $publisher->email }}</small>
                    @endif
                </div>
                @endif
            </div>
        </div>
    </section>

    <!-- 底部区域 -->
    <section class="footer-section">
        <div class="container">
            <p>&copy; 2025 第九届光谱成像研讨会组委会 版权所有</p>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 附件下载功能
        function downloadAttachment(url, filename) {
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 返回按钮功能增强
        document.querySelector('.back-button').addEventListener('click', function(e) {
            e.preventDefault();
            if (document.referrer) {
                history.back();
            } else {
                window.location.href = '/';
            }
        });
    </script>
</body>
</html>
