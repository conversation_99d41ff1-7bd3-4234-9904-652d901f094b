<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_info', function (Blueprint $table) {
            $table->string('contact_name')->nullable()->after('bank_account')->comment('联系人姓名');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_info', function (Blueprint $table) {
            $table->dropColumn('contact_name');
        });
    }
};
