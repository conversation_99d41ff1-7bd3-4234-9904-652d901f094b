<template>
  <div class="users">
    <h1>用户管理</h1>
    
    <el-table :data="users" style="width: 100%; margin-top: 20px;">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="username" label="用户名" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.row)">
            编辑
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const users = ref([
  { id: 1, username: 'admin', email: '<EMAIL>', createTime: '2024-01-01' },
  { id: 2, username: 'user1', email: '<EMAIL>', createTime: '2024-01-02' },
  { id: 3, username: 'user2', email: '<EMAIL>', createTime: '2024-01-03' }
])

const handleEdit = (row) => {
  ElMessage.info(`编辑用户: ${row.username}`)
}

const handleDelete = (row) => {
  ElMessage.warning(`删除用户: ${row.username}`)
}
</script>

<style scoped>
.users {
  padding: 20px;
  min-height: calc(100vh - 60px);
  background-color: #f5f7fa;
}
</style>