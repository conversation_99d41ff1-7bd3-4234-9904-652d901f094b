import request from '@/utils/request'

// 获取用户的开票信息列表
export function getInvoiceInfoList() {
  return request({
    url: '/invoice-info',
    method: 'get'
  })
}

// 创建开票信息
export function createInvoiceInfo(data) {
  return request({
    url: '/invoice-info',
    method: 'post',
    data
  })
}

// 更新开票信息
export function updateInvoiceInfo(id, data) {
  return request({
    url: `/invoice-info/${id}`,
    method: 'put',
    data
  })
}

// 删除开票信息
export function deleteInvoiceInfo(id) {
  return request({
    url: `/invoice-info/${id}`,
    method: 'delete'
  })
}

// 设置默认开票信息
export function setDefaultInvoiceInfo(id) {
  return request({
    url: `/invoice-info/${id}/set-default`,
    method: 'post'
  })
}

// 获取发票列表（只包含已支付的订单）
export function getInvoiceList() {
  return request({
    url: '/invoices',
    method: 'get'
  })
}

// 下载发票
export function downloadInvoice(id) {
  return request({
    url: `/invoices/${id}/download`,
    method: 'get',
    responseType: 'blob'
  })
}
