<template>
  <div class="submission-detail">
    <div class="page-header">
      <h2>投稿详情</h2>
      <div class="header-actions">
        <el-button @click="goBack">返回列表</el-button>
        <el-button 
          v-if="submission && submission.can_edit" 
          type="primary" 
          @click="goToEdit"
        >
          编辑
        </el-button>
        <el-button 
          v-if="submission && submission.status === 'draft'" 
          type="success" 
          @click="handleSubmit"
        >
          提交投稿
        </el-button>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <div v-else-if="submission">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <h3>基本信息</h3>
            <el-tag :type="getStatusType(submission.status)">
              {{ submission.status_label }}
            </el-tag>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="论文标题" :span="2">
            {{ submission.title }}
          </el-descriptions-item>
          <el-descriptions-item label="投稿类型">
            {{ submission.submission_type_label }}
          </el-descriptions-item>
          <el-descriptions-item label="研究领域">
            {{ submission.research_field }}
          </el-descriptions-item>
          <el-descriptions-item label="会议主题">
            {{ submission.meeting_theme || '未选择' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ submission.created_at }}
          </el-descriptions-item>
          <el-descriptions-item label="提交时间">
            {{ submission.submitted_at || '未提交' }}
          </el-descriptions-item>
          <el-descriptions-item label="论文摘要" :span="2">
            <div class="abstract-content">{{ submission.abstract }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="关键词" :span="2">
            <el-tag 
              v-for="keyword in submission.keywords" 
              :key="keyword" 
              style="margin-right: 8px;"
            >
              {{ keyword }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 作者信息 -->
      <el-card class="info-card">
        <template #header>
          <h3>作者信息</h3>
        </template>

        <el-table :data="submission.authors" style="width: 100%">
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="institution" label="所属机构" />
          <el-table-column prop="email" label="邮箱">
            <template #default="{ row }">
              {{ row.email || '未提供' }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 通讯作者信息 -->
      <el-card class="info-card">
        <template #header>
          <h3>通讯作者信息</h3>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="通讯作者">
            {{ submission.corresponding_author }}
          </el-descriptions-item>
          <el-descriptions-item label="通讯邮箱">
            {{ submission.corresponding_email }}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{ submission.corresponding_phone }}
          </el-descriptions-item>
          <el-descriptions-item label="所属机构">
            {{ submission.institution }}
          </el-descriptions-item>
          <el-descriptions-item label="科室/部门" :span="2">
            {{ submission.department || '未提供' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 其他信息 -->
      <el-card class="info-card">
        <template #header>
          <h3>其他信息</h3>
        </template>

        <el-descriptions :column="1" border>
          <el-descriptions-item label="基金资助信息">
            <div class="funding-content">
              {{ submission.funding_info || '无' }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="论文文件">
            <div v-if="submission.original_filename" class="file-info">
              <el-icon><Document /></el-icon>
              <span class="filename">{{ submission.original_filename }}</span>
              <span class="file-size">({{ formatFileSize(submission.file_size) }})</span>
              <el-button 
                type="primary" 
                size="small" 
                @click="downloadFile"
                style="margin-left: 10px;"
              >
                下载
              </el-button>
            </div>
            <span v-else class="no-file">未上传文件</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 审稿意见 -->
      <el-card v-if="submission.review_comments" class="info-card">
        <template #header>
          <h3>审稿意见</h3>
        </template>

        <div class="review-comments">
          {{ submission.review_comments }}
        </div>
        
        <div v-if="submission.reviewed_at" class="review-time">
          审稿时间：{{ submission.reviewed_at }}
        </div>
      </el-card>
    </div>

    <div v-else class="error-state">
      <el-result icon="error" title="投稿不存在" sub-title="请检查投稿ID是否正确">
        <template #extra>
          <el-button type="primary" @click="goBack">返回列表</el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import {
  getSubmissionDetail,
  submitSubmission,
  downloadSubmissionFile
} from '@/api/submission'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const loading = ref(false)
const submission = ref(null)

// 检查付费用户权限
const checkPaidUser = () => {
  if (!userStore.isPaidUser) {
    ElMessage.warning('投稿功能仅限付费用户使用，请先完成会议注册并支付费用')
    router.push('/meeting-registration')
    return false
  }
  return true
}

// 获取投稿详情
const loadSubmissionDetail = async () => {
  if (!checkPaidUser()) return

  loading.value = true
  try {
    const response = await getSubmissionDetail(route.params.id)
    if (response.success) {
      submission.value = response.data
    }
  } catch (error) {
    console.error('获取投稿详情失败:', error)
    ElMessage.error('获取投稿详情失败')
  } finally {
    loading.value = false
  }
}

// 获取状态标签类型
const getStatusType = (status) => {
  const typeMap = {
    'draft': 'info',
    'submitted': 'warning',
    'under_review': 'primary',
    'accepted': 'success',
    'rejected': 'danger',
    'revision_required': 'warning'
  }
  return typeMap[status] || 'info'
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 下载文件
const downloadFile = async () => {
  try {
    const response = await downloadSubmissionFile(route.params.id)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', submission.value.original_filename)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}

// 提交投稿
const handleSubmit = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要提交投稿"${submission.value.title}"吗？提交后将无法修改。`,
      '确认提交',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await submitSubmission(route.params.id)
    if (response.success) {
      ElMessage.success('投稿提交成功')
      await loadSubmissionDetail()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交投稿失败:', error)
      ElMessage.error('提交投稿失败')
    }
  }
}

// 跳转到编辑页面
const goToEdit = () => {
  router.push(`/submission/${route.params.id}/edit`)
}

// 返回列表
const goBack = () => {
  router.push('/submissions')
}

onMounted(() => {
  loadSubmissionDetail()
})
</script>

<style scoped>
.submission-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.loading-container {
  padding: 20px;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.abstract-content {
  line-height: 1.6;
  white-space: pre-wrap;
}

.funding-content {
  line-height: 1.6;
  white-space: pre-wrap;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filename {
  font-weight: 500;
  color: #409eff;
}

.file-size {
  color: #909399;
  font-size: 12px;
}

.no-file {
  color: #909399;
}

.review-comments {
  line-height: 1.6;
  white-space: pre-wrap;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 10px;
}

.review-time {
  color: #909399;
  font-size: 12px;
  text-align: right;
}

.error-state {
  padding: 40px 20px;
  text-align: center;
}
</style>
