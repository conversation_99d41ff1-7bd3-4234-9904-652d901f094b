<template>
  <div class="speech-submission-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑会议发言投稿' : '新建会议发言投稿' }}</h2>
      <el-button @click="goBack">返回列表</el-button>
    </div>

    <el-card>
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="120px"
        v-loading="loading"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h3>基本信息</h3>
          
          <el-form-item label="发言标题" prop="title">
            <el-input 
              v-model="form.title" 
              placeholder="请输入发言标题"
              maxlength="255"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="发言摘要" prop="abstract">
            <el-input 
              v-model="form.abstract" 
              type="textarea" 
              :rows="4"
              placeholder="请输入发言摘要"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>

          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item label="发言类型" prop="speech_type">
                <el-select v-model="form.speech_type" placeholder="请选择发言类型">
                  <el-option label="主题演讲" value="keynote" />
                  <el-option label="特邀报告" value="invited" />
                  <el-option label="一般发言" value="regular" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发言时长" prop="duration">
                <el-input-number 
                  v-model="form.duration" 
                  :min="5" 
                  :max="180" 
                  placeholder="分钟"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="主题分类" prop="topic_category">
                <el-input v-model="form.topic_category" placeholder="请输入主题分类" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="关键词" prop="keywords" required>
            <el-tag
              v-for="(keyword, index) in form.keywords"
              :key="index"
              closable
              @close="removeKeyword(index)"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ keyword }}
            </el-tag>
            <el-input
              v-if="keywordInputVisible"
              ref="keywordInputRef"
              v-model="keywordInputValue"
              size="small"
              style="width: 120px;"
              @keyup.enter="handleKeywordInputConfirm"
              @blur="handleKeywordInputConfirm"
            />
            <el-button v-else size="small" @click="showKeywordInput">
              + 添加关键词
            </el-button>
          </el-form-item>
        </div>

        <!-- 发言人信息 -->
        <div class="form-section">
          <h3>发言人信息</h3>
          
          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item label="发言人姓名" prop="speaker_name">
                <el-input v-model="form.speaker_name" placeholder="请输入发言人姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="职务/头衔" prop="speaker_title">
                <el-input v-model="form.speaker_title" placeholder="请输入职务或头衔" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属机构" prop="speaker_institution">
                <el-input v-model="form.speaker_institution" placeholder="请输入所属机构" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="邮箱" prop="speaker_email">
                <el-input v-model="form.speaker_email" placeholder="请输入邮箱" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话" prop="speaker_phone">
                <el-input v-model="form.speaker_phone" placeholder="请输入电话" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="发言人简介" prop="speaker_bio">
            <el-input 
              v-model="form.speaker_bio" 
              type="textarea" 
              :rows="4"
              placeholder="请输入发言人简介"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 发言内容 -->
        <div class="form-section">
          <h3>发言内容</h3>
          
          <el-form-item label="发言大纲" prop="outline">
            <el-input 
              v-model="form.outline" 
              type="textarea" 
              :rows="6"
              placeholder="请输入详细的发言大纲"
              maxlength="2000"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="目标听众" prop="target_audience">
            <el-input 
              v-model="form.target_audience" 
              type="textarea" 
              :rows="3"
              placeholder="请描述目标听众（可选）"
            />
          </el-form-item>

          <el-form-item label="特殊要求" prop="special_requirements">
            <el-input 
              v-model="form.special_requirements" 
              type="textarea" 
              :rows="3"
              placeholder="请描述设备、场地等特殊要求（可选）"
            />
          </el-form-item>
        </div>

        <!-- 演示文件 -->
        <div class="form-section">
          <h3>演示文件</h3>
          
          <el-form-item label="演示文件" prop="file">
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :limit="1"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :file-list="fileList"
              accept=".pdf,.ppt,.pptx,.doc,.docx"
            >
              <el-button type="primary">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持 PDF、PPT、PPTX、DOC、DOCX 文件，且不超过 20MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" @click="handleSave" :loading="saving">
            {{ isEdit ? '更新' : '保存' }}
          </el-button>
          <el-button 
            v-if="!isEdit || (submissionData && submissionData.status === 'draft')"
            type="success" 
            @click="handleSaveAndSubmit" 
            :loading="saving"
          >
            保存并提交
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  createSpeechSubmission, 
  updateSpeechSubmission, 
  getSpeechSubmissionDetail,
  submitSpeechSubmission 
} from '@/api/speechSubmission'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const formRef = ref(null)
const uploadRef = ref(null)
const keywordInputRef = ref(null)

const loading = ref(false)
const saving = ref(false)
const keywordInputVisible = ref(false)
const keywordInputValue = ref('')
const fileList = ref([])
const submissionData = ref(null)

// 判断是否为编辑模式
const isEdit = computed(() => route.name === 'SpeechSubmissionEdit')

// 表单数据
const form = reactive({
  title: '',
  abstract: '',
  speaker_name: '',
  speaker_title: '',
  speaker_institution: '',
  speaker_email: '',
  speaker_phone: '',
  speaker_bio: '',
  speech_type: 'regular',
  duration: 30,
  topic_category: '',
  keywords: [],
  outline: '',
  target_audience: '',
  special_requirements: '',
  file: null
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入发言标题', trigger: 'blur' }
  ],
  abstract: [
    { required: true, message: '请输入发言摘要', trigger: 'blur' }
  ],
  speaker_name: [
    { required: true, message: '请输入发言人姓名', trigger: 'blur' }
  ],
  speaker_title: [
    { required: true, message: '请输入职务或头衔', trigger: 'blur' }
  ],
  speaker_institution: [
    { required: true, message: '请输入所属机构', trigger: 'blur' }
  ],
  speaker_email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  speaker_phone: [
    { required: true, message: '请输入电话', trigger: 'blur' }
  ],
  speaker_bio: [
    { required: true, message: '请输入发言人简介', trigger: 'blur' }
  ],
  speech_type: [
    { required: true, message: '请选择发言类型', trigger: 'change' }
  ],
  duration: [
    { required: true, message: '请输入发言时长', trigger: 'blur' }
  ],
  topic_category: [
    { required: true, message: '请输入主题分类', trigger: 'blur' }
  ],
  keywords: [
    { 
      type: 'array', 
      min: 1, 
      message: '请至少添加一个关键词', 
      trigger: 'change' 
    }
  ],
  outline: [
    { required: true, message: '请输入发言大纲', trigger: 'blur' }
  ]
}

// 检查付费用户权限
const checkPaidUser = () => {
  if (!userStore.isPaidUser) {
    ElMessage.warning('会议发言投稿功能仅限付费用户使用，请先完成会议注册并支付费用')
    router.push('/meeting-registration')
    return false
  }
  return true
}

// 加载投稿详情（编辑模式）
const loadSubmissionDetail = async () => {
  if (!checkPaidUser()) return
  if (!isEdit.value) return
  
  loading.value = true
  try {
    const response = await getSpeechSubmissionDetail(route.params.id)
    if (response.success) {
      submissionData.value = response.data
      
      // 填充表单数据
      Object.assign(form, {
        title: response.data.title,
        abstract: response.data.abstract,
        speaker_name: response.data.speaker_name,
        speaker_title: response.data.speaker_title,
        speaker_institution: response.data.speaker_institution,
        speaker_email: response.data.speaker_email,
        speaker_phone: response.data.speaker_phone,
        speaker_bio: response.data.speaker_bio,
        speech_type: response.data.speech_type,
        duration: response.data.duration,
        topic_category: response.data.topic_category,
        keywords: response.data.keywords || [],
        outline: response.data.outline,
        target_audience: response.data.target_audience || '',
        special_requirements: response.data.special_requirements || ''
      })

      // 如果有文件，显示在文件列表中
      if (response.data.original_filename) {
        fileList.value = [{
          name: response.data.original_filename,
          url: response.data.file_url
        }]
      }
    }
  } catch (error) {
    console.error('获取发言投稿详情失败:', error)
    ElMessage.error('获取发言投稿详情失败')
    goBack()
  } finally {
    loading.value = false
  }
}

// 显示关键词输入框
const showKeywordInput = () => {
  keywordInputVisible.value = true
  nextTick(() => {
    keywordInputRef.value?.focus()
  })
}

// 确认添加关键词
const handleKeywordInputConfirm = () => {
  if (keywordInputValue.value && !form.keywords.includes(keywordInputValue.value)) {
    form.keywords.push(keywordInputValue.value)
  }
  keywordInputVisible.value = false
  keywordInputValue.value = ''
}

// 删除关键词
const removeKeyword = (index) => {
  form.keywords.splice(index, 1)
}

// 处理文件选择
const handleFileChange = (file) => {
  form.file = file.raw
}

// 处理文件删除
const handleFileRemove = () => {
  form.file = null
}

// 保存投稿
const handleSave = async () => {
  if (!checkPaidUser()) return
  
  try {
    await formRef.value.validate()
    
    saving.value = true
    
    const formData = new FormData()
    
    // 添加表单数据
    Object.keys(form).forEach(key => {
      if (key === 'keywords') {
        formData.append(key, JSON.stringify(form[key]))
      } else if (key === 'file' && form[key]) {
        formData.append(key, form[key])
      } else if (form[key] !== null && form[key] !== undefined) {
        formData.append(key, form[key])
      }
    })

    let response
    if (isEdit.value) {
      response = await updateSpeechSubmission(route.params.id, formData)
    } else {
      response = await createSpeechSubmission(formData)
    }

    if (response.success) {
      ElMessage.success(isEdit.value ? '发言投稿更新成功' : '发言投稿创建成功')
      goBack()
    }
  } catch (error) {
    if (error !== 'validation failed') {
      console.error('保存发言投稿失败:', error)
      ElMessage.error('保存发言投稿失败')
    }
  } finally {
    saving.value = false
  }
}

// 保存并提交投稿
const handleSaveAndSubmit = async () => {
  try {
    await handleSave()
    
    // 如果是新建，需要获取创建后的ID
    if (!isEdit.value) {
      ElMessage.info('请在发言投稿列表中提交您的投稿')
      return
    }

    // 提交投稿
    const submitResponse = await submitSpeechSubmission(route.params.id)
    if (submitResponse.success) {
      ElMessage.success('发言投稿提交成功')
    }
  } catch (error) {
    console.error('保存并提交失败:', error)
  }
}

// 返回列表
const goBack = () => {
  router.push('/speech-submissions')
}

onMounted(() => {
  loadSubmissionDetail()
})
</script>

<style scoped>
.speech-submission-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.form-section:last-of-type {
  border-bottom: none;
}

.form-section h3 {
  margin: 0 0 20px 0;
  color: #409eff;
  font-size: 16px;
  font-weight: 600;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 10px;
}
</style>
