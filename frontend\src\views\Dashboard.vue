<template>
  <div class="dashboard">
    <!-- 欢迎信息 -->
    <div class="welcome-header">
      <el-card>
        <div class="welcome-info">
          <div class="avatar-section">
            <el-avatar :size="60" :src="dashboardData?.user_info?.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
          </div>
          <div class="welcome-text">
            <h2>欢迎回来，{{ dashboardData?.user_info?.real_name || dashboardData?.user_info?.name || '用户' }}！</h2>
            <p>{{ getWelcomeMessage() }}</p>
            <div class="user-level">
              <el-tag :type="getLevelTagType(dashboardData?.user_info?.user_level)" size="large">
                {{ dashboardData?.user_info?.user_level_label }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    <!-- 统计数据卡片 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="6">
        <el-card class="stat-card-container">
          <div class="stat-card">
            <el-icon size="40" color="#409EFF"><Document /></el-icon>
            <div>
              <h3>{{ dashboardData?.statistics?.submissions_count || 0 }}</h3>
              <p>论文投稿</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card-container">
          <div class="stat-card">
            <el-icon size="40" color="#67C23A"><Microphone /></el-icon>
            <div>
              <h3>{{ dashboardData?.statistics?.speech_submissions_count || 0 }}</h3>
              <p>发言投稿</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card-container">
          <div class="stat-card">
            <el-icon size="40" color="#E6A23C"><Files /></el-icon>
            <div>
              <h3>{{ dashboardData?.statistics?.total_papers || 0 }}</h3>
              <p>我的总论数</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card-container">
          <div class="stat-card">
            <el-icon size="40" color="#F56C6C"><Calendar /></el-icon>
            <div>
              <h3>{{ dashboardData?.statistics?.meeting_registrations_count || 0 }}</h3>
              <p>会议注册</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 缴费状态和记录 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 缴费状态 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>缴费状态</span>
            </div>
          </template>

          <!-- 未缴费提醒 -->
          <div v-if="dashboardData?.unpaid_registrations?.length > 0" class="unpaid-alert">
            <el-alert
              title="您有未缴费的会议注册"
              type="warning"
              :closable="false"
              show-icon
            >
              <template #default>
                <div v-for="registration in dashboardData.unpaid_registrations" :key="registration.id" class="unpaid-item">
                  <div class="unpaid-info">
                    <span class="meeting-title">{{ registration.meeting_title }}</span>
                    <span class="fee-amount">¥{{ registration.fee_amount }}</span>
                  </div>
                  <el-button type="primary" size="small" @click="goToPayment(registration.id)">
                    立即缴费
                  </el-button>
                </div>
              </template>
            </el-alert>
          </div>

          <!-- 缴费统计 -->
          <div class="payment-stats">
            <div class="stat-item">
              <label>累计缴费金额：</label>
              <span class="amount">¥{{ dashboardData?.payment_info?.total_paid_amount || '0.00' }}</span>
            </div>
            <div class="stat-item">
              <label>已缴费会议数：</label>
              <span>{{ dashboardData?.payment_info?.paid_meetings_count || 0 }} 场</span>
            </div>
            <div class="stat-item">
              <label>未缴费注册数：</label>
              <span class="unpaid-count">{{ dashboardData?.payment_info?.unpaid_registrations_count || 0 }} 个</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 最近缴费记录 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近缴费记录</span>
              <el-button type="text" @click="viewAllPayments">查看全部</el-button>
            </div>
          </template>

          <div v-if="dashboardData?.payment_info?.recent_payments?.length > 0" class="payment-list">
            <div
              v-for="payment in dashboardData.payment_info.recent_payments"
              :key="payment.id"
              class="payment-item"
            >
              <div class="payment-info">
                <div class="meeting-title">{{ payment.meeting_title }}</div>
                <div class="payment-time">{{ formatDate(payment.paid_at) }}</div>
              </div>
              <div class="payment-amount">¥{{ payment.amount }}</div>
            </div>
          </div>

          <div v-else class="no-payments">
            <el-empty description="暂无缴费记录" :image-size="80" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快捷操作</span>
            </div>
          </template>

          <div class="quick-actions">
            <el-button type="primary" @click="goToSubmissions">
              <el-icon><Document /></el-icon>
              论文投稿
            </el-button>
            <el-button type="success" @click="goToSpeechSubmissions">
              <el-icon><Microphone /></el-icon>
              发言投稿
            </el-button>
            <el-button type="warning" @click="goToMeetings">
              <el-icon><Calendar /></el-icon>
              会议注册
            </el-button>
            <el-button type="info" @click="goToProfile">
              <el-icon><User /></el-icon>
              个人资料
            </el-button>
            <el-button @click="goToMaterialDownload">
              <el-icon><Download /></el-icon>
              资料下载
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>




  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { getDashboardData } from '@/api/dashboard'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

// 仪表盘数据
const dashboardData = ref(null)
const loading = ref(false)

// 获取仪表盘数据
const fetchDashboardData = async () => {
  loading.value = true
  try {
    const response = await getDashboardData()
    if (response.success) {
      dashboardData.value = response.data
    } else {
      ElMessage.error('获取仪表盘数据失败')
    }
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
    ElMessage.error('获取仪表盘数据失败')
  } finally {
    loading.value = false
  }
}

// 工具方法
const getLevelTagType = (level) => {
  const types = {
    'free': 'info',
    'paid': 'success',
    'premium': 'warning',
    'vip': 'danger'
  }
  return types[level] || 'info'
}

const getWelcomeMessage = () => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好！'
  if (hour < 18) return '下午好！'
  return '晚上好！'
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 导航方法
const goToPayment = (registrationId) => {
  router.push(`/payment?registration_id=${registrationId}`)
}

const goToSubmissions = () => {
  router.push('/submissions')
}

const goToSpeechSubmissions = () => {
  router.push('/speech-submissions')
}

const goToMeetings = () => {
  router.push('/meetings')
}

const goToProfile = () => {
  router.push('/profile')
}

const viewAllPayments = () => {
  router.push('/payment')
}

const goToMaterialDownload = () => {
  router.push('/material-download')
}

onMounted(() => {
  fetchDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
  min-height: calc(100vh - 60px);
  background-color: #f5f7fa;
}

.welcome-header {
  margin-bottom: 20px;
}

.welcome-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.avatar-section {
  flex-shrink: 0;
}

.welcome-text h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
}

.welcome-text p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
}

.user-level {
  margin-top: 8px;
}

.stat-card-container {
  height: 120px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 15px;
  height: 100%;
}

.stat-card h3 {
  margin: 0 0 5px 0;
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stat-card p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.unpaid-alert {
  margin-bottom: 20px;
}

.unpaid-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.unpaid-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.meeting-title {
  font-weight: 500;
  color: #303133;
}

.fee-amount {
  color: #f56c6c;
  font-weight: 600;
}

.payment-stats {
  margin-top: 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-item label {
  color: #606266;
  font-size: 14px;
}

.amount {
  color: #67c23a;
  font-weight: 600;
  font-size: 16px;
}

.unpaid-count {
  color: #f56c6c;
  font-weight: 500;
}

.payment-list {
  max-height: 300px;
  overflow-y: auto;
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.payment-item:last-child {
  border-bottom: none;
}

.payment-info {
  flex: 1;
}

.payment-info .meeting-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.payment-time {
  font-size: 12px;
  color: #909399;
}

.payment-amount {
  color: #67c23a;
  font-weight: 600;
  font-size: 16px;
}

.no-payments {
  text-align: center;
  padding: 40px 0;
}

.quick-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.quick-actions .el-button {
  flex: 1;
  min-width: 120px;
}

@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }

  .welcome-info {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .unpaid-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .payment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .quick-actions {
    flex-direction: column;
  }

  .quick-actions .el-button {
    width: 100%;
  }
}
</style>