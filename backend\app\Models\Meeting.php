<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Meeting extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'start_time',
        'end_time',
        'location',
        'type',
        'expert_fee_early',
        'expert_fee_normal',
        'student_fee_early',
        'student_fee_normal',
        'corporate_fee_early',
        'corporate_fee_normal',
        'early_deadline',
        'registration_deadline',
        'max_participants',
        'status',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'early_deadline' => 'datetime',
        'registration_deadline' => 'datetime',
        'expert_fee_early' => 'decimal:2',
        'expert_fee_normal' => 'decimal:2',
        'student_fee_early' => 'decimal:2',
        'student_fee_normal' => 'decimal:2',
        'corporate_fee_early' => 'decimal:2',
        'corporate_fee_normal' => 'decimal:2',
    ];

    /**
     * 获取会议的注册记录
     */
    public function registrations()
    {
        return $this->hasMany(MeetingRegistration::class);
    }

    /**
     * 获取当前费用（根据时间和类型）
     */
    public function getCurrentFee($memberType)
    {
        $isEarlyBird = now()->lt($this->early_deadline);

        switch ($memberType) {
            case 'student':
                return $isEarlyBird ? $this->student_fee_early : $this->student_fee_normal;
            case 'corporate':
                return $isEarlyBird ? $this->corporate_fee_early : $this->corporate_fee_normal;
            case 'expert':
            default:
                return $isEarlyBird ? $this->expert_fee_early : $this->expert_fee_normal;
        }
    }

    /**
     * 检查是否可以注册
     */
    public function canRegister()
    {
        return $this->status === 'published'
            && now()->lt($this->registration_deadline)
            && ($this->max_participants === null || $this->registrations()->count() < $this->max_participants);
    }


}
