import request from '@/utils/request'

// 获取会议发言投稿列表
export function getSpeechSubmissionList() {
  return request({
    url: '/speech-submissions',
    method: 'get'
  })
}

// 获取会议发言投稿详情
export function getSpeechSubmissionDetail(id) {
  return request({
    url: `/speech-submissions/${id}`,
    method: 'get'
  })
}

// 创建会议发言投稿
export function createSpeechSubmission(data) {
  return request({
    url: '/speech-submissions',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 更新会议发言投稿
export function updateSpeechSubmission(id, data) {
  return request({
    url: `/speech-submissions/${id}`,
    method: 'put',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除会议发言投稿
export function deleteSpeechSubmission(id) {
  return request({
    url: `/speech-submissions/${id}`,
    method: 'delete'
  })
}

// 提交会议发言投稿
export function submitSpeechSubmission(id) {
  return request({
    url: `/speech-submissions/${id}/submit`,
    method: 'post'
  })
}

// 下载演示文件
export function downloadSpeechSubmissionFile(id) {
  return request({
    url: `/speech-submissions/${id}/download`,
    method: 'get',
    responseType: 'blob'
  })
}
