<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('speech_submissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade')->comment('投稿用户ID');
            $table->string('title')->comment('发言标题');
            $table->text('abstract')->comment('发言摘要');
            $table->string('speaker_name')->comment('发言人姓名');
            $table->string('speaker_title')->comment('发言人职务/头衔');
            $table->string('speaker_institution')->comment('发言人所属机构');
            $table->string('speaker_email')->comment('发言人邮箱');
            $table->string('speaker_phone')->comment('发言人电话');
            $table->text('speaker_bio')->comment('发言人简介');
            $table->enum('speech_type', ['keynote', 'invited', 'regular'])->comment('发言类型：keynote-主题演讲，invited-特邀报告，regular-一般发言');
            $table->integer('duration')->comment('预计发言时长（分钟）');
            $table->string('topic_category')->comment('主题分类');
            $table->json('keywords')->comment('关键词JSON数组');
            $table->text('outline')->comment('发言大纲');
            $table->text('target_audience')->nullable()->comment('目标听众');
            $table->text('special_requirements')->nullable()->comment('特殊要求（设备、场地等）');
            $table->string('presentation_file')->nullable()->comment('演示文件路径');
            $table->string('original_filename')->nullable()->comment('原始文件名');
            $table->integer('file_size')->nullable()->comment('文件大小(字节)');
            $table->enum('status', ['draft', 'submitted', 'under_review', 'accepted', 'rejected', 'revision_required'])->default('draft')->comment('投稿状态');
            $table->text('review_comments')->nullable()->comment('审稿意见');
            $table->timestamp('submitted_at')->nullable()->comment('提交时间');
            $table->timestamp('reviewed_at')->nullable()->comment('审稿时间');
            $table->timestamps();

            // 添加索引
            $table->index('user_id');
            $table->index('status');
            $table->index('speech_type');
            $table->index('submitted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('speech_submissions');
    }
};
