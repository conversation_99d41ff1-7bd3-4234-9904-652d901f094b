APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# 微信支付配置
WECHAT_PAY_APPID=
WECHAT_PAY_MCH_ID=
WECHAT_PAY_SERIAL_NO=
WECHAT_PAY_PRIVATE_KEY_PATH=cert/apiclient_key.pem
WECHAT_PAY_CERTIFICATE_PATH=cert/apiclient_cert.pem
WECHAT_PAY_NOTIFY_URL=

# 支付宝支付配置
ALIPAY_APP_ID=2021005183661373
# 应用私钥（对应Demo中的merchant_private_key）
ALIPAY_PRIVATE_KEY=
# 支付宝公钥（对应Demo中的alipay_public_key）
ALIPAY_PUBLIC_KEY=
# 支付模式：sandbox=沙箱模式，normal=正式模式
ALIPAY_MODE=sandbox
ALIPAY_SANDBOX=true
# 回调地址
ALIPAY_NOTIFY_URL=http://localhost:8000/api/payments/alipay/notify
ALIPAY_RETURN_URL=http://localhost:8000/api/payments/alipay/return
# 是否使用证书模式（false=普通公钥模式，true=证书模式）
ALIPAY_USE_CERT_MODE=false
# 接口内容加密配置（如果支付宝开启了接口内容加密）
ALIPAY_APP_SECRET_KEY=fO6tbDhBxoe4/4/MVIHDkw==
ALIPAY_AES_KEY=fO6tbDhBxoe4/4/MVIHDkw==

# 前端地址
FRONTEND_URL=http://localhost:5173
