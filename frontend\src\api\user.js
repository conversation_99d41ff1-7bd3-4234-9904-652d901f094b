import request from '@/utils/request'

/**
 * 获取用户个人信息
 */
export const getUserProfile = () => {
  return request.get('/user/profile')
}

/**
 * 更新用户个人信息
 * @param {Object} profileData - 个人信息数据
 * @param {string} profileData.name - 用户名
 * @param {string} profileData.real_name - 真实姓名
 * @param {string} profileData.phone - 手机号
 * @param {string} profileData.avatar - 头像URL
 * @param {string} profileData.member_type - 会员类型
 */
export const updateUserProfile = (profileData) => {
  return request.put('/user/profile', profileData)
}

/**
 * 修改密码
 * @param {Object} passwordData - 密码数据
 * @param {string} passwordData.old_password - 旧密码
 * @param {string} passwordData.new_password - 新密码
 * @param {string} passwordData.new_password_confirmation - 确认新密码
 */
export const changePassword = (passwordData) => {
  return request.post('/user/change-password', passwordData)
}

/**
 * 发送短信验证码
 * @param {string} phone - 手机号
 */
export const sendSmsCode = (phone) => {
  return request.post('/user/send-sms', { phone })
}

/**
 * 绑定手机号
 * @param {Object} bindData - 绑定数据
 * @param {string} bindData.phone - 手机号
 * @param {string} bindData.code - 验证码
 */
export const bindPhone = (bindData) => {
  return request.post('/user/bind-phone', bindData)
}

/**
 * 绑定微信
 */
export const bindWechat = () => {
  return request.post('/user/bind-wechat')
}
