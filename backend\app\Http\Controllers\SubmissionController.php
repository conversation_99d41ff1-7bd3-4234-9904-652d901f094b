<?php

namespace App\Http\Controllers;

use App\Models\Submission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Services\OssService;

class SubmissionController extends Controller
{
    /**
     * 检查用户是否为付费用户
     */
    private function checkPaidUser($user)
    {
        if (!$user->is_paid_user) {
            return response()->json([
                'success' => false,
                'message' => '投稿功能仅限付费用户使用，请先完成会议注册并支付费用',
                'error_code' => 'UNPAID_USER',
                'redirect_url' => '/meeting-registration'
            ], 403);
        }
        return null;
    }

    /**
     * 获取用户的投稿列表
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // 检查是否为付费用户
        $paidCheck = $this->checkPaidUser($user);
        if ($paidCheck) return $paidCheck;
        $submissions = $user->submissions()
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($submission) {
                return [
                    'id' => $submission->id,
                    'title' => $submission->title,
                    'submission_type' => $submission->submission_type,
                    'submission_type_label' => $submission->submission_type_label,
                    'status' => $submission->status,
                    'status_label' => $submission->status_label,
                    'submitted_at' => $submission->submitted_at?->format('Y-m-d H:i:s'),
                    'created_at' => $submission->created_at->format('Y-m-d H:i:s'),
                    'can_edit' => $submission->canEdit(),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $submissions
        ]);
    }

    /**
     * 获取投稿详情
     */
    public function show(Request $request, $id)
    {
        $user = $request->user();

        // 检查是否为付费用户
        $paidCheck = $this->checkPaidUser($user);
        if ($paidCheck) return $paidCheck;
        $submission = $user->submissions()->find($id);

        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => '投稿不存在'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $submission->id,
                'title' => $submission->title,
                'abstract' => $submission->abstract,
                'authors' => $submission->authors,
                'corresponding_author' => $submission->corresponding_author,
                'corresponding_email' => $submission->corresponding_email,
                'corresponding_phone' => $submission->corresponding_phone,
                'institution' => $submission->institution,
                'department' => $submission->department,
                'keywords' => $submission->keywords,
                'submission_type' => $submission->submission_type,
                'submission_type_label' => $submission->submission_type_label,
                'research_field' => $submission->research_field,
                'meeting_theme' => $submission->meeting_theme,
                'funding_info' => $submission->funding_info,
                'file_path' => $submission->file_path,
                'file_url' => $submission->file_url,
                'original_filename' => $submission->original_filename,
                'file_size' => $submission->file_size,
                'status' => $submission->status,
                'status_label' => $submission->status_label,
                'review_comments' => $submission->review_comments,
                'submitted_at' => $submission->submitted_at?->format('Y-m-d H:i:s'),
                'reviewed_at' => $submission->reviewed_at?->format('Y-m-d H:i:s'),
                'created_at' => $submission->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $submission->updated_at->format('Y-m-d H:i:s'),
                'can_edit' => $submission->canEdit(),
            ]
        ]);
    }

    /**
     * 创建投稿
     */
    public function store(Request $request)
    {
        $user = $request->user();

        // 检查是否为付费用户
        $paidCheck = $this->checkPaidUser($user);
        if ($paidCheck) return $paidCheck;

        // 处理JSON字符串字段
        $requestData = $request->all();
        Log::info($requestData);
        if (isset($requestData['authors']) && is_string($requestData['authors'])) {
            $requestData['authors'] = json_decode($requestData['authors'], true);
            // 处理authors中的空email字段
            if (is_array($requestData['authors'])) {
                foreach ($requestData['authors'] as &$author) {
                    if (isset($author['email']) && $author['email'] === '') {
                        $author['email'] = null;
                    }
                }
            }
        }
        if (isset($requestData['keywords']) && is_string($requestData['keywords'])) {
            $requestData['keywords'] = json_decode($requestData['keywords'], true);
        }

        $validator = Validator::make($requestData, [
            'title' => 'required|string|max:255',
            'abstract' => 'required|string',
            'authors' => 'required|array|min:1',
            'authors.*.name' => 'required|string|max:100',
            'authors.*.institution' => 'required|string|max:255',
            'authors.*.email' => 'nullable|email|max:255',
            'corresponding_author' => 'required|string|max:100',
            'corresponding_email' => 'required|email|max:255',
            'corresponding_phone' => 'required|string|max:50',
            'institution' => 'required|string|max:255',
            'department' => 'nullable|string|max:255',
            'keywords' => 'required|array|min:1',
            'keywords.*' => 'required|string|max:50',
            'submission_type' => 'required|in:oral,poster',
            'research_field' => 'required|string|max:255',
            'meeting_theme' => 'required|string|max:255',
            'funding_info' => 'nullable|string',
            'file' => 'nullable|file|mimes:pdf,doc,docx|max:10240', // 10MB
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['user_id'] = $user->id;

        // 处理文件上传
        if ($request->hasFile('file')) {
            try {
                $ossService = new OssService();
                $file = $request->file('file');
                $filename = 'submission_' . $user->id . '_' . time() . '.' . $file->getClientOriginalExtension();

                $uploadResult = $ossService->uploadFile($file, 'submissions', $filename);

                if (!$uploadResult['success']) {
                    return response()->json([
                        'success' => false,
                        'message' => '文件上传失败: ' . $uploadResult['message']
                    ], 500);
                }

                $data['file_path'] = $uploadResult['data']['url'];
                $data['original_filename'] = $file->getClientOriginalName();
                $data['file_size'] = $file->getSize();
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => '文件上传失败: ' . $e->getMessage()
                ], 500);
            }
        }

        $submission = Submission::create($data);

        return response()->json([
            'success' => true,
            'message' => '投稿创建成功',
            'data' => [
                'id' => $submission->id,
                'title' => $submission->title,
                'status' => $submission->status,
                'status_label' => $submission->status_label,
            ]
        ], 201);
    }

    /**
     * 更新投稿
     */
    public function update(Request $request, $id)
    {
        $user = $request->user();

        // 检查是否为付费用户
        $paidCheck = $this->checkPaidUser($user);
        if ($paidCheck) return $paidCheck;
        $submission = $user->submissions()->find($id);

        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => '投稿不存在'
            ], 404);
        }

        if (!$submission->canEdit()) {
            return response()->json([
                'success' => false,
                'message' => '当前状态下不能编辑投稿'
            ], 403);
        }

        // 处理JSON字符串字段
        $requestData = $request->all();
        Log::info($requestData);

        if (isset($requestData['authors']) && is_string($requestData['authors'])) {
            $requestData['authors'] = json_decode($requestData['authors'], true);
            // 处理authors中的空email字段
            if (is_array($requestData['authors'])) {
                foreach ($requestData['authors'] as &$author) {
                    if (isset($author['email']) && $author['email'] === '') {
                        $author['email'] = null;
                    }
                }
            }
        }
        if (isset($requestData['keywords']) && is_string($requestData['keywords'])) {
            $requestData['keywords'] = json_decode($requestData['keywords'], true);
        }

        $validator = Validator::make($requestData, [
            'title' => 'required|string|max:255',
            'abstract' => 'required|string',
            'authors' => 'required|array|min:1',
            'authors.*.name' => 'required|string|max:100',
            'authors.*.institution' => 'required|string|max:255',
            'authors.*.email' => 'nullable|email|max:255',
            'corresponding_author' => 'required|string|max:100',
            'corresponding_email' => 'required|email|max:255',
            'corresponding_phone' => 'required|string|max:50',
            'institution' => 'required|string|max:255',
            'department' => 'nullable|string|max:255',
            'keywords' => 'required|array|min:1',
            'keywords.*' => 'required|string|max:50',
            'submission_type' => 'required|in:oral,poster',
            'research_field' => 'required|string|max:255',
            'meeting_theme' => 'required|string|max:255',
            'funding_info' => 'nullable|string',
            'file' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        // 处理文件上传
        if ($request->hasFile('file')) {
            try {
                $ossService = new OssService();

                // 删除旧文件（如果是OSS URL，提取object key进行删除）
                if ($submission->file_path) {
                    $oldFilePath = $submission->file_path;
                    // 如果是完整URL，提取object key
                    if (filter_var($oldFilePath, FILTER_VALIDATE_URL)) {
                        $cdnUrl = config('filesystems.disks.oss.cdn_domain');
                        if ($cdnUrl && strpos($oldFilePath, $cdnUrl) === 0) {
                            $objectKey = str_replace($cdnUrl . '/', '', $oldFilePath);
                            $ossService->deleteFile($objectKey);
                        }
                    } else {
                        // 如果是相对路径，使用本地存储删除
                        Storage::disk('public')->delete($oldFilePath);
                    }
                }

                $file = $request->file('file');
                $filename = 'submission_' . $user->id . '_' . time() . '.' . $file->getClientOriginalExtension();

                $uploadResult = $ossService->uploadFile($file, 'submissions', $filename);

                if (!$uploadResult['success']) {
                    return response()->json([
                        'success' => false,
                        'message' => '文件上传失败: ' . $uploadResult['message']
                    ], 500);
                }

                $data['file_path'] = $uploadResult['data']['url'];
                $data['original_filename'] = $file->getClientOriginalName();
                $data['file_size'] = $file->getSize();
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => '文件上传失败: ' . $e->getMessage()
                ], 500);
            }
        }

        $submission->update($data);

        return response()->json([
            'success' => true,
            'message' => '投稿更新成功'
        ]);
    }

    /**
     * 删除投稿
     */
    public function destroy(Request $request, $id)
    {
        $user = $request->user();

        // 检查是否为付费用户
        $paidCheck = $this->checkPaidUser($user);
        if ($paidCheck) return $paidCheck;
        $submission = $user->submissions()->find($id);

        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => '投稿不存在'
            ], 404);
        }

        if (!$submission->canEdit()) {
            return response()->json([
                'success' => false,
                'message' => '当前状态下不能删除投稿'
            ], 403);
        }

        // 删除文件
        if ($submission->file_path) {
            try {
                // 如果是完整URL，提取object key进行删除
                if (filter_var($submission->file_path, FILTER_VALIDATE_URL)) {
                    $ossService = new OssService();
                    $cdnUrl = config('filesystems.disks.oss.cdn_domain');
                    if ($cdnUrl && strpos($submission->file_path, $cdnUrl) === 0) {
                        $objectKey = str_replace($cdnUrl . '/', '', $submission->file_path);
                        $ossService->deleteFile($objectKey);
                    }
                } else {
                    // 如果是相对路径，使用本地存储删除
                    Storage::disk('public')->delete($submission->file_path);
                }
            } catch (\Exception $e) {
                // 文件删除失败不影响投稿删除
                \Log::warning('删除投稿文件失败: ' . $e->getMessage());
            }
        }

        $submission->delete();

        return response()->json([
            'success' => true,
            'message' => '投稿删除成功'
        ]);
    }

    /**
     * 提交投稿
     */
    public function submit(Request $request, $id)
    {
        $user = $request->user();

        // 检查是否为付费用户
        $paidCheck = $this->checkPaidUser($user);
        if ($paidCheck) return $paidCheck;
        $submission = $user->submissions()->find($id);

        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => '投稿不存在'
            ], 404);
        }

        if ($submission->status !== Submission::STATUS_DRAFT) {
            return response()->json([
                'success' => false,
                'message' => '只有草稿状态的投稿才能提交'
            ], 403);
        }

        $submission->submit();

        return response()->json([
            'success' => true,
            'message' => '投稿提交成功'
        ]);
    }

    /**
     * 下载投稿文件
     */
    public function download(Request $request, $id)
    {
        $user = $request->user();

        // 检查是否为付费用户
        $paidCheck = $this->checkPaidUser($user);
        if ($paidCheck) return $paidCheck;
        $submission = $user->submissions()->find($id);

        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => '投稿不存在'
            ], 404);
        }

        if (!$submission->file_path) {
            return response()->json([
                'success' => false,
                'message' => '文件不存在'
            ], 404);
        }

        // 检查file_path是否为完整URL（OSS文件）
        if (filter_var($submission->file_path, FILTER_VALIDATE_URL)) {
            // 如果是完整URL，返回下载URL给前端
            return response()->json([
                'success' => true,
                'download_url' => $submission->file_path,
                'filename' => $submission->original_filename
            ]);
        } else {
            // 如果是相对路径，使用本地存储下载
            if (!Storage::disk('public')->exists($submission->file_path)) {
                return response()->json([
                    'success' => false,
                    'message' => '文件不存在'
                ], 404);
            }
            return Storage::disk('public')->download($submission->file_path, $submission->original_filename);
        }
    }
}
