<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceInfo extends Model
{
    protected $table = 'invoice_info';

    protected $fillable = [
        'user_id',
        'invoice_type',
        'invoice_title',
        'tax_number',
        'company_phone',
        'bank_name',
        'bank_account',
        'contact_name',
        'contact_phone',
        'contact_email',
        'is_default'
    ];

    protected $casts = [
        'is_default' => 'boolean',
    ];

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 设置为默认开票信息
     */
    public function setAsDefault(): void
    {
        // 先将该用户的所有开票信息设为非默认
        static::where('user_id', $this->user_id)->update(['is_default' => false]);

        // 将当前记录设为默认
        $this->update(['is_default' => true]);
    }

    /**
     * 获取用户的默认开票信息
     */
    public static function getDefaultByUser(int $userId): ?self
    {
        return static::where('user_id', $userId)
            ->where('is_default', true)
            ->first();
    }

    /**
     * 获取用户的所有开票信息
     */
    public static function getByUser(int $userId)
    {
        return static::where('user_id', $userId)
            ->orderBy('is_default', 'desc')
            ->orderBy('updated_at', 'desc')
            ->get();
    }
}
