<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // 修改member_type枚举，添加corporate类型，并允许null值（未支付状态）
            $table->enum('member_type', ['expert', 'student', 'corporate'])->nullable()->change()->comment('会员类型');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // 回滚到原来的枚举值，不允许null
            $table->enum('member_type', ['expert', 'student'])->default('expert')->change()->comment('会员类型');
        });
    }
};
