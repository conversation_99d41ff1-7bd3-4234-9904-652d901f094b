# 微信公众号配置指南

## 1. 微信公众号后台配置

### 基本配置
- **服务器地址(URL)**: `http://ai.whlxyc.cn/api/wechat/callback`
- **Token**: `W5utUPj43Nz2`
- **EncodingAESKey**: `cVrcj31dbIfZfNSJElMbQXI4Sea8iOZDshmUiWSgYdh`
- **消息加解密方式**: 明文模式（推荐）或兼容模式

### 配置步骤

1. **登录微信公众平台**
   - 访问：https://mp.weixin.qq.com/
   - 使用管理员账号登录

2. **进入开发者工具**
   - 左侧菜单：开发 → 基本配置
   - 点击"修改配置"

3. **填写服务器配置**
   ```
   URL: http://ai.whlxyc.cn/api/wechat/callback
   Token: W5utUPj43Nz2
   EncodingAESKey: cVrcj31dbIfZfNSJElMbQXI4Sea8iOZDshmUiWSgYdh
   消息加解密方式: 明文模式
   ```

4. **提交验证**
   - 点击"提交"按钮
   - 微信服务器会向您的URL发送验证请求
   - 如果配置正确，会显示"配置成功"

## 2. 环境变量配置

确保 `.env` 文件中包含以下配置：

```env
# 微信公众号配置
WECHAT_OFFICIAL_ACCOUNT_APPID=wx899ffb8f21781145
WECHAT_OFFICIAL_ACCOUNT_SECRET=5afc16e76a4c1eac87fcc6d468a9059c
WECHAT_OFFICIAL_ACCOUNT_TOKEN=W5utUPj43Nz2
WECHAT_OFFICIAL_ACCOUNT_AES_KEY=cVrcj31dbIfZfNSJElMbQXI4Sea8iOZDshmUiWSgYdh
```

## 3. 测试验证

### 测试配置信息
访问：`http://ai.whlxyc.cn/api/wechat/test-config`

### 测试二维码生成
访问：`http://ai.whlxyc.cn/api/wechat/qrcode`

## 4. 功能说明

### 支持的功能
- ✅ URL验证（微信服务器验证）
- ✅ 二维码登录
- ✅ 事件消息处理
- ✅ 用户扫码事件
- ✅ 关注/取消关注事件

### API接口
- `GET /api/wechat/callback` - 微信服务器验证
- `POST /api/wechat/callback` - 微信消息回调
- `GET /api/wechat/qrcode` - 生成登录二维码
- `GET /api/wechat/login-status` - 检查登录状态
- `POST /api/wechat/mock-login` - 模拟登录（演示用）

## 5. 故障排除

### 常见错误

1. **Token验证失败**
   - 检查Token是否正确
   - 确保URL可以正常访问
   - 检查服务器时间是否正确

2. **URL无法访问**
   - 确保域名解析正确
   - 检查防火墙设置
   - 确保Web服务器正常运行

3. **签名验证失败**
   - 检查Token配置
   - 确保签名算法正确
   - 检查参数传递

### 调试方法

1. **查看日志**
   ```bash
   tail -f storage/logs/laravel.log
   ```

2. **测试URL验证**
   ```bash
   php artisan tinker
   # 然后执行验证测试
   ```

3. **检查配置**
   ```bash
   php artisan config:show wechat
   ```

## 6. 安全注意事项

1. **Token安全**
   - 使用复杂的Token
   - 定期更换Token
   - 不要在代码中硬编码

2. **HTTPS配置**
   - 生产环境建议使用HTTPS
   - 配置SSL证书

3. **IP白名单**
   - 配置微信服务器IP白名单
   - 限制访问来源

## 7. 生产环境配置

### 域名配置
- 确保域名已备案
- 配置正确的DNS解析
- 使用HTTPS协议

### 服务器配置
- 确保服务器稳定运行
- 配置负载均衡（如需要）
- 监控服务器状态

### 微信公众号设置
- 完成微信认证
- 配置菜单和自动回复
- 设置用户权限
