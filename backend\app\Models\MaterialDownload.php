<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MaterialDownload extends Model
{
    protected $fillable = [
        'user_id',
        'material_id',
        'ip_address',
        'user_agent',
    ];

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联资料
     */
    public function material(): BelongsTo
    {
        return $this->belongsTo(MeetingMaterial::class, 'material_id');
    }
}
