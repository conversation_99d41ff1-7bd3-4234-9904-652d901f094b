<?php

namespace App\Services;

use WeChatPay\Builder;
use WeChatPay\Crypto\Rsa;
use WeChatPay\Util\PemUtil;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class WechatPayService
{
    private $instance;
    private $config;

    public function __construct()
    {
        $this->config = config('wechat.payment.default');

        // 在开发环境下跳过微信支付初始化，直接使用模拟支付
        if (env('APP_ENV') === 'local' || env('WECHAT_PAY_SANDBOX', false)) {
            Log::info('开发环境，跳过微信支付初始化，使用模拟支付');
            return;
        }

        $this->initializeWechatPay();
    }

    /**
     * 初始化微信支付实例
     */
    private function initializeWechatPay()
    {
        try {
            // 验证必要的配置
            $requiredFields = ['app_id', 'mch_id', 'serial_no'];
            foreach ($requiredFields as $field) {
                if (empty($this->config[$field])) {
                    throw new \Exception("微信支付配置缺少必要字段: {$field}");
                }
            }

            // 商户私钥路径 - 支持相对路径和绝对路径
            $privateKeyPath = $this->config['private_key_path'];
            if (!str_starts_with($privateKeyPath, '/') && !str_contains($privateKeyPath, ':\\')) {
                // 相对路径，添加storage_path前缀
                $merchantPrivateKeyFilePath = storage_path($privateKeyPath);
            } else {
                // 绝对路径，直接使用
                $merchantPrivateKeyFilePath =$privateKeyPath;
            }

            // 检查私钥文件是否存在
            if (!file_exists($merchantPrivateKeyFilePath)) {
                Log::info("私钥文件不存在，尝试创建: {$merchantPrivateKeyFilePath}");
                // 如果文件不存在，尝试从环境变量创建
                $this->createPrivateKeyFile($merchantPrivateKeyFilePath);
            }

            // 再次检查文件是否存在
            if (!file_exists($merchantPrivateKeyFilePath)) {
                throw new \Exception("私钥文件不存在: {$merchantPrivateKeyFilePath}");
            }

            // 检查文件是否可读
            if (!is_readable($merchantPrivateKeyFilePath)) {
                throw new \Exception("私钥文件不可读: {$merchantPrivateKeyFilePath}");
            }
            $platformCertificateFilePath = storage_path($this->config['platform_certificate']);
            if (!file_exists($platformCertificateFilePath)) {
                throw new \Exception("平台证书文件不存在: {$platformCertificateFilePath}");
            }
            if (!is_readable($platformCertificateFilePath)) {
                throw new \Exception("平台证书文件不可读: {$platformCertificateFilePath}");
            }
            $merchantCertificateFilePath = storage_path($this->config['certificate_path']);
            if (!file_exists($merchantCertificateFilePath)) {
                throw new \Exception("商户证书文件不存在: {$merchantCertificateFilePath}");
            }
            if (!is_readable($merchantCertificateFilePath)) {
                throw new \Exception("商户证书文件不可读: {$merchantCertificateFilePath}");
            }

            Log::info("加载私钥文件: {$merchantPrivateKeyFilePath}");
            $merchantPrivateKeyInstance = Rsa::from("file://".$merchantPrivateKeyFilePath, Rsa::KEY_TYPE_PRIVATE);
            
            $platformPublicKeyInstance = Rsa::from('file://'.$platformCertificateFilePath, Rsa::KEY_TYPE_PUBLIC);
            $platformCertificateSerial = PemUtil::parseCertificateSerialNo("file://".$platformCertificateFilePath);
            // 商户证书序列号
            $merchantCertificateSerial = $this->config['serial_no'];

            // 构造一个 APIv3 客户端实例（简化版，不需要平台证书）
            $this->instance = Builder::factory([
                'mchid'      => $this->config['mch_id'],
                'serial'     => $merchantCertificateSerial,
                'privateKey' => $merchantPrivateKeyInstance,
                'certs' => [
                    $platformCertificateSerial => $platformPublicKeyInstance,
                ],
                'merchant' => [
                    'cert' => $merchantCertificateFilePath,
                    'key' => $merchantPrivateKeyFilePath,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('微信支付初始化失败: ' . $e->getMessage());
            throw new \Exception('微信支付配置错误');
        }
    }

    /**
     * 创建私钥文件
     */
    private function createPrivateKeyFile($filePath)
    {
        // 从环境变量获取私钥内容
        $privateKeyContent = env('WECHAT_PAY_PRIVATE_KEY');

        // 如果环境变量中没有私钥，使用API密钥作为临时方案
        if (empty($privateKeyContent)) {
            $privateKeyContent = $this->config['api_key'];
        }

        if (empty($privateKeyContent)) {
            throw new \Exception('私钥内容为空，请设置WECHAT_PAY_PRIVATE_KEY环境变量');
        }

        // 确保目录存在
        $directory = dirname($filePath);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }

        // 格式化私钥内容
        if (!str_contains($privateKeyContent, '-----BEGIN')) {
            $privateKeyContent = "-----BEGIN PRIVATE KEY-----\n" .
                                chunk_split($privateKeyContent, 64, "\n") .
                                "-----END PRIVATE KEY-----\n";
        }

        file_put_contents($filePath, $privateKeyContent);
        chmod($filePath, 0600); // 设置安全权限
    }

    /**
     * 创建Native支付订单（扫码支付）
     */
    public function createNativeOrder($orderData)
    {
        try {
            // 开发环境下使用模拟支付
            if (env('APP_ENV') === 'local' || env('WECHAT_PAY_SANDBOX', true)) {
                return $this->createMockNativeOrder($orderData);
            }

            $response = $this->instance
                ->v3->pay->transactions->native
                ->post([
                    'json' => [
                        'mchid'        => $this->config['mch_id'],
                        'out_trade_no' => $orderData['out_trade_no'],
                        'appid'        => $this->config['app_id'],
                        'description'  => $orderData['description'],
                        'notify_url'   => $this->config['notify_url'],
                        'amount'       => [
                            'total'    => $orderData['total_fee'], // 金额，单位：分
                            'currency' => 'CNY'
                        ],
                    ],
                ]);

            $result = json_decode($response->getBody(), true);

            if (isset($result['code_url'])) {
                return [
                    'success' => true,
                    'code_url' => $result['code_url'],
                    'out_trade_no' => $orderData['out_trade_no']
                ];
            }

            return [
                'success' => false,
                'message' => '创建支付订单失败'
            ];

        } catch (\Exception $e) {
            Log::error('创建微信支付订单失败: ' . $e->getMessage());
            // 如果真实支付失败，回退到模拟支付
            return $this->createMockNativeOrder($orderData);
        }
    }

    /**
     * 创建模拟Native支付订单
     */
    private function createMockNativeOrder($orderData)
    {
        // 生成模拟的支付URL，使用特殊的mock协议
        $mockCodeUrl = "mock://wechat-pay/simulate?" . http_build_query([
            'out_trade_no' => $orderData['out_trade_no'],
            'amount' => $orderData['total_fee'],
            'description' => $orderData['description'],
            'timestamp' => time()
        ]);

        Log::info('创建模拟微信支付订单', [
            'out_trade_no' => $orderData['out_trade_no'],
            'amount' => $orderData['total_fee'],
            'description' => $orderData['description'],
            'mock_url' => $mockCodeUrl
        ]);

        return [
            'success' => true,
            'code_url' => $mockCodeUrl,
            'out_trade_no' => $orderData['out_trade_no'],
            'is_mock' => true
        ];
    }

    /**
     * 创建JSAPI支付订单（公众号支付）
     */
    public function createJsapiOrder($orderData, $openid)
    {
        try {
            $response = $this->instance
                ->v3->pay->transactions->jsapi
                ->post([
                    'json' => [
                        'mchid'        => $this->config['mch_id'],
                        'out_trade_no' => $orderData['out_trade_no'],
                        'appid'        => $this->config['app_id'],
                        'description'  => $orderData['description'],
                        'notify_url'   => $this->config['notify_url'],
                        'amount'       => [
                            'total'    => $orderData['total_fee'],
                            'currency' => 'CNY'
                        ],
                        'payer' => [
                            'openid' => $openid
                        ]
                    ],
                ]);

            $result = json_decode($response->getBody(), true);
            
            if (isset($result['prepay_id'])) {
                // 生成前端调起支付所需的参数
                $payParams = $this->generateJsapiPayParams($result['prepay_id']);
                
                return [
                    'success' => true,
                    'prepay_id' => $result['prepay_id'],
                    'pay_params' => $payParams
                ];
            }

            return [
                'success' => false,
                'message' => '创建支付订单失败'
            ];

        } catch (\Exception $e) {
            Log::error('创建微信JSAPI支付订单失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '支付服务异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 查询订单状态
     */
    public function queryOrder($outTradeNo)
    {
        try {
            $response = $this->instance
                ->v3->pay->transactions->outTradeNo->{$outTradeNo}
                ->get([
                    'query' => [
                        'mchid' => $this->config['mch_id'],
                    ],
                ]);

            $result = json_decode($response->getBody(), true);
            
            return [
                'success' => true,
                'data' => $result
            ];

        } catch (\Exception $e) {
            Log::error('查询微信支付订单失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '查询订单失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 生成JSAPI支付参数
     */
    private function generateJsapiPayParams($prepayId)
    {
        $appId = $this->config['app_id'];
        $timeStamp = (string)time();
        $nonceStr = Str::random(32);
        $package = "prepay_id={$prepayId}";
        $signType = 'RSA';

        // 构造签名字符串
        $message = $appId . "\n" . $timeStamp . "\n" . $nonceStr . "\n" . $package . "\n";
        
        // 使用商户私钥签名
        $merchantPrivateKeyFilePath = storage_path($this->config['private_key_path']);
        $merchantPrivateKeyInstance = Rsa::from($merchantPrivateKeyFilePath, Rsa::KEY_TYPE_PRIVATE);
        $signature = Rsa::sign($message, $merchantPrivateKeyInstance);

        return [
            'appId' => $appId,
            'timeStamp' => $timeStamp,
            'nonceStr' => $nonceStr,
            'package' => $package,
            'signType' => $signType,
            'paySign' => $signature
        ];
    }

    /**
     * 验证回调签名
     */
    public function verifyNotify($headers, $body)
    {
        try {
            // 获取平台证书
            $platformCertificateFilePath = storage_path($this->config['certificate_path']);
            $platformPublicKeyInstance = Rsa::from($platformCertificateFilePath, Rsa::KEY_TYPE_PUBLIC);

            // 验证签名
            $timeStamp = $headers['Wechatpay-Timestamp'] ?? '';
            $nonce = $headers['Wechatpay-Nonce'] ?? '';
            $signature = $headers['Wechatpay-Signature'] ?? '';
            $serial = $headers['Wechatpay-Serial'] ?? '';

            $message = $timeStamp . "\n" . $nonce . "\n" . $body . "\n";
            
            return Rsa::verify($message, $signature, $platformPublicKeyInstance);

        } catch (\Exception $e) {
            Log::error('验证微信支付回调签名失败: ' . $e->getMessage());
            return false;
        }
    }
}
