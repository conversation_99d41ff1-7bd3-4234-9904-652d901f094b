<template>
  <div class="profile-page">
    <!-- 顶部个人信息卡片 -->
    <div class="profile-header">
      <el-card class="profile-card">
        <div class="user-info">
          <div class="avatar-section">
            <el-avatar :size="80" :src="profileForm.avatar">
              <el-icon size="40"><User /></el-icon>
            </el-avatar>
          </div>
          <div class="user-details">
            <h2 class="username">{{ userStore.user?.real_name || userStore.user?.name || '用户' }}</h2>
            <div class="user-tags">
              <el-tag
                :type="getMemberTypeTagType(userStore.user?.member_type)"
                class="member-tag"
              >
                {{ getMemberTypeLabel(userStore.user?.member_type) }}
              </el-tag>
              <el-tag
                :type="userStore.isPaidUser ? 'success' : 'info'"
                class="member-tag"
                effect="dark"
              >
                {{ userStore.userLevelLabel }}
              </el-tag>
              <el-tag v-if="userStore.user?.phone_verified_at" type="info" class="member-tag">
                已验证手机
              </el-tag>
              <el-tag v-if="userStore.user?.wechat_openid" type="success" class="member-tag">
                已绑定微信
              </el-tag>
            </div>
            <p class="user-desc">
              欢迎来到会议管理系统，您可以在这里管理您的会议信息和个人资料。
            </p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 付费统计 -->
    <div class="payment-stats" v-if="userStore.isPaidUser">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>付费统计</h3>
            <div class="header-actions">
              <el-tag :type="userStore.userLevel === 'vip' ? 'danger' : 'success'" effect="dark">
                {{ userStore.userLevelLabel }}
              </el-tag>
              <el-button size="small" @click="refreshUserInfo" :loading="refreshing">
                <el-icon><Refresh /></el-icon>
                刷新状态
              </el-button>
            </div>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">¥{{ userStore.user?.total_paid_amount || '0.00' }}</div>
              <div class="stat-label">累计付费</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ userStore.user?.paid_meetings_count || 0 }}</div>
              <div class="stat-label">付费会议</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ formatDate(userStore.user?.paid_at) }}</div>
              <div class="stat-label">首次付费</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">
                <el-icon color="#67c23a"><Trophy /></el-icon>
              </div>
              <div class="stat-label">会员等级</div>
            </div>
          </el-col>
        </el-row>

        <!-- 实时状态信息 -->
        <div class="status-info">
          <el-divider content-position="left">
            <span style="color: #909399; font-size: 14px;">实时状态信息</span>
          </el-divider>
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="status-item">
                <span class="status-label">付费状态：</span>
                <el-tag :type="userStore.isPaidUser ? 'success' : 'info'" size="small">
                  {{ userStore.isPaidUser ? '已付费' : '未付费' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="status-item">
                <span class="status-label">用户等级：</span>
                <el-tag :type="getUserLevelType(userStore.userLevel)" size="small">
                  {{ userStore.userLevelLabel }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="status-item">
                <span class="status-label">最后更新：</span>
                <span class="status-time">{{ new Date().toLocaleTimeString('zh-CN') }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 个人资料编辑 -->
    <div class="profile-edit">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>个人资料</h3>
            <div class="header-actions">
              <el-button v-if="!editMode" type="primary" @click="enterEditMode">
                <el-icon><Edit /></el-icon>
                编辑资料
              </el-button>
              <div v-else>
                <el-button @click="cancelEdit">取消</el-button>
                <el-button type="primary" :loading="profileLoading" @click="saveProfile">
                  <el-icon><Check /></el-icon>
                  保存
                </el-button>
              </div>
            </div>
          </div>
        </template>

        <el-form
          ref="profileFormRef"
          :model="profileForm"
          :rules="profileRules"
          label-width="100px"
          :disabled="!editMode"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="真实姓名" prop="real_name">
                <el-input v-model="profileForm.real_name" placeholder="请输入真实姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="profileForm.gender">
                  <el-radio value="male">男</el-radio>
                  <el-radio value="female">女</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="工作单位" prop="organization">
                <el-input v-model="profileForm.organization" placeholder="请输入工作单位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职称" prop="job_title">
                <el-input v-model="profileForm.job_title" placeholder="请输入职称" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="profileForm.email" placeholder="请输入邮箱" type="email" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="会员类型" prop="member_type">
                <!--
                <el-select
                  v-model="profileForm.member_type"
                  placeholder="支付成功后自动关联"
                  :disabled="true"
                >
                  <el-option label="未注册" value="" />
                  <el-option label="正式代表" value="expert" />
                  <el-option label="学生代表" value="student" />
                  <el-option label="企业代表" value="corporate" />
                </el-select>-->
                
                  <el-text size="small" type="info">
                    {{ getMemberTypeHint() }}
                  </el-text>
                
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="是否做报告" prop="will_present">
                <el-radio-group v-model="profileForm.will_present">
                  <el-radio :value="true">是</el-radio>
                  <el-radio :value="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>

    <!-- 手机号绑定 -->
    <div class="phone-binding">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>手机号绑定</h3>
            <el-tag v-if="userStore.user?.phone_verified_at" type="success">
              已绑定：{{ userStore.user?.phone }}
            </el-tag>
            <el-tag v-else type="warning">未绑定</el-tag>
          </div>
        </template>

        <el-form
          ref="phoneFormRef"
          :model="phoneForm"
          :rules="phoneRules"
          label-width="100px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="手机号" prop="phone">
                <el-input
                  v-model="phoneForm.phone"
                  placeholder="请输入手机号"
                  :disabled="!!userStore.user?.phone_verified_at"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="验证码" prop="code">
                <div class="code-input">
                  <el-input
                    v-model="phoneForm.code"
                    placeholder="请输入验证码"
                    :disabled="!!userStore.user?.phone_verified_at"
                  />
                  <el-button
                    :disabled="codeDisabled || !!userStore.user?.phone_verified_at"
                    @click="sendPhoneCode"
                    style="margin-left: 10px"
                  >
                    {{ codeText }}
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item v-if="!userStore.user?.phone_verified_at">
            <el-button type="primary" :loading="phoneLoading" @click="handleBindPhone">
              绑定手机号
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 微信绑定 -->
    <div class="wechat-binding">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>微信绑定</h3>
            <el-tag v-if="userStore.user?.wechat_openid" type="success">
              已绑定微信
            </el-tag>
            <el-tag v-else type="warning">未绑定</el-tag>
          </div>
        </template>

        <div class="wechat-content">
          <div v-if="userStore.user?.wechat_openid" class="wechat-bound">
            <el-result icon="success" title="微信已绑定" sub-title="您的账号已成功绑定微信">
              <template #extra>
                <el-button type="danger" @click="handleUnbindWechat">解绑微信</el-button>
              </template>
            </el-result>
          </div>
          <div v-else class="wechat-unbind">
            <el-result icon="info" title="微信未绑定" sub-title="绑定微信后可以使用微信扫码登录">
              <template #extra>
                <el-button type="primary" :loading="wechatLoading" @click="handleBindWechat">
                  <el-icon><ChatDotRound /></el-icon>
                  绑定微信
                </el-button>
              </template>
            </el-result>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 注册费用表格 -->
    <div class="registration-fees">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>注册费</h3>
          </div>
        </template>

        <el-table :data="feeData" style="width: 100%" border>
          <el-table-column prop="memberType" label="会员人员身份" align="center" />
          <el-table-column prop="beforeDate" label="9月30日（含）前" align="center">
            <template #default="scope">
              <span class="fee-amount">{{ scope.row.beforeDate }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="afterDate" label="10月1日及以后" align="center">
            <template #default="scope">
              <span class="fee-amount">{{ scope.row.afterDate }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 我的注册记录 -->
    <div class="my-registrations">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>我的注册记录</h3>
            <el-button type="primary" @click="refreshRegistrations">刷新</el-button>
          </div>
        </template>

        <div v-if="loading" class="loading-container">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载中...</span>
        </div>

        <el-table v-else :data="Array.isArray(meetingStore.myRegistrations) ? meetingStore.myRegistrations : []" style="width: 100%" border>
          <el-table-column prop="meeting.title" label="会议名称" />
          <el-table-column prop="registration_type" label="注册类型" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.registration_type === 'expert' ? 'success' : 'warning'">
                {{ scope.row.registration_type === 'expert' ? '专家' : '学生' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="fee_amount" label="费用" align="center">
            <template #default="scope">
              <span class="fee-amount">¥{{ scope.row.fee_amount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="payment_status" label="支付状态" align="center">
            <template #default="scope">
              <el-tag
                :type="getPaymentStatusType(scope.row.payment_status)"
              >
                {{ getPaymentStatusText(scope.row.payment_status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="registered_at" label="注册时间" align="center">
            <template #default="scope">
              {{ formatDate(scope.row.registered_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120">
            <template #default="scope">
              <el-button
                v-if="scope.row.payment_status === 'pending'"
                type="primary"
                size="small"
                @click="goToPayment(scope.row)"
              >
                去支付
              </el-button>
              <el-tag v-else type="success" size="small">已完成</el-tag>
            </template>
          </el-table-column>
        </el-table>

        <el-empty v-if="!loading && (!meetingStore.myRegistrations || meetingStore.myRegistrations.length === 0)" description="暂无注册记录" />
      </el-card>
    </div>

    <!-- 注册提醒 -->
    <div class="registration-notice">
      <el-alert
        title="注册提醒"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>请注意注册截止时间，提前注册可享受优惠价格。如有疑问，请联系会议组织方。</p>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useMeetingStore } from '@/stores/meeting'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Loading, Trophy, Refresh, Edit, Check, ChatDotRound } from '@element-plus/icons-vue'
import { sendSmsCode } from '@/api/sms'

const router = useRouter()
const userStore = useUserStore()
const meetingStore = useMeetingStore()

const loading = ref(false)
const refreshing = ref(false)
const editMode = ref(false)
const profileLoading = ref(false)
const phoneLoading = ref(false)
const wechatLoading = ref(false)
const codeDisabled = ref(false)
const codeText = ref('发送验证码')
const countdown = ref(0)

// 表单引用
const profileFormRef = ref()
const phoneFormRef = ref()

const profileForm = reactive({
  name: '',
  real_name: '',
  gender: '',
  organization: '',
  will_present: false,
  job_title: '',
  email: '',
  member_type: 'expert',
  avatar: ''
})

const phoneForm = reactive({
  phone: '',
  code: ''
})

// 表单验证规则
const profileRules = {
  real_name: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '真实姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  organization: [
    { required: true, message: '请输入工作单位', trigger: 'blur' },
    { max: 255, message: '工作单位长度不能超过 255 个字符', trigger: 'blur' }
  ],
  job_title: [
    { required: true, message: '请输入职称', trigger: 'blur' },
    { max: 255, message: '职称长度不能超过 255 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  member_type: [
    { required: false, message: '会员类型由支付成功后自动关联', trigger: 'change' }
  ],
  will_present: [
    { required: true, message: '请选择是否做会议报告', trigger: 'change' }
  ]
}

const phoneRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码必须是6位数字', trigger: 'blur' }
  ]
}

// 注册费用数据
const feeData = ref([
  {
    memberType: '正式代表',
    beforeDate: '2000元/人',
    afterDate: '2800元/人'
  },
  {
    memberType: '学生代表',
    beforeDate: '1200元/人',
    afterDate: '1600元/人'
  },
  {
    memberType: '企业代表',
    beforeDate: '5000元/人',
    afterDate: '6000元/人'
  }
])

// 获取注册类型标签
const getRegistrationTypeLabel = (type) => {
  const labels = {
    'expert': '正式代表',
    'student': '学生代表',
    'corporate': '企业代表'
  }
  return labels[type] || type
}

// 获取会员类型标签（用于个人资料显示）
const getMemberTypeLabel = (type) => {
  const labels = {
    'expert': '专家会员',
    'student': '学生会员',
    'corporate': '企业会员'
  }
  return labels[type] || '未设置'
}

// 获取会员类型标签颜色
const getMemberTypeTagType = (type) => {
  const types = {
    'expert': 'success',
    'student': 'warning',
    'corporate': 'info'
  }
  return types[type] || 'info'
}

// 获取会员类型提示
const getMemberTypeHint = () => {
  if (!userStore.user) return '加载中...'

  if (!userStore.user.is_paid_user) {
    return '未注册'
  }

  if (userStore.user.member_type) {
    return `会员类型：${getRegistrationTypeLabel(userStore.user.member_type)}`
  }

  return '已支付，但会员类型未设置'
}

// 刷新用户信息
const refreshUserInfo = async () => {
  refreshing.value = true
  try {
    const result = await userStore.getCurrentUser()
    if (result.success) {
      //ElMessage.success('用户信息已刷新')
      console.log('用户信息已刷新:', {
        isPaidUser: userStore.isPaidUser,
        userLevel: userStore.userLevel,
        userLevelLabel: userStore.userLevelLabel,
        totalPaidAmount: userStore.user?.total_paid_amount,
        paidMeetingsCount: userStore.user?.paid_meetings_count,
        paidAt: userStore.user?.paid_at
      })

      // 刷新表单数据
      initForm()
    } else {
      ElMessage.error(result.message || '获取用户信息失败')
    }
  } catch (error) {
    console.error('刷新用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  } finally {
    refreshing.value = false
  }
}

const initForm = () => {
  if (userStore.user) {
    Object.assign(profileForm, {
      name: userStore.user.name || '',
      real_name: userStore.user.real_name || '',
      gender: userStore.user.gender || '',
      organization: userStore.user.organization || '',
      will_present: userStore.user.will_present || false,
      job_title: userStore.user.job_title || '',
      email: userStore.user.email || '',
      member_type: userStore.user.member_type || null,
      avatar: userStore.user.avatar || ''
    })
    phoneForm.phone = userStore.user.phone || ''
  }
}

// 个人资料编辑相关方法
const enterEditMode = () => {
  editMode.value = true
  initForm()
}

const cancelEdit = () => {
  editMode.value = false
  initForm()
}

const saveProfile = async () => {
  if (!profileFormRef.value) return

  try {
    await profileFormRef.value.validate()
    profileLoading.value = true

    const result = await userStore.updateProfile(profileForm)
    if (result.success) {
      ElMessage.success('个人资料更新成功')
      editMode.value = false
      await refreshUserInfo()
    } else {
      ElMessage.error(result.message || '更新失败')
    }
  } catch (error) {
    console.error('保存个人资料失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    profileLoading.value = false
  }
}

// 手机号绑定相关方法
const sendPhoneCode = async () => {
  if (!phoneForm.phone) {
    ElMessage.warning('请先输入手机号')
    return
  }

  if (!/^1[3-9]\d{9}$/.test(phoneForm.phone)) {
    ElMessage.error('请输入正确的手机号')
    return
  }

  try {
    const result = await sendSmsCode({
      phone: phoneForm.phone,
      type: 'bind'
    })
    if (result.success) {
      ElMessage.success('验证码已发送')
      startCountdown()
    } else {
      ElMessage.error(result.message || '发送失败')
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error('发送失败，请重试')
  }
}

const startCountdown = () => {
  codeDisabled.value = true
  countdown.value = 60

  const timer = setInterval(() => {
    countdown.value--
    codeText.value = `${countdown.value}s后重发`

    if (countdown.value <= 0) {
      clearInterval(timer)
      codeDisabled.value = false
      codeText.value = '发送验证码'
    }
  }, 1000)
}

const handleBindPhone = async () => {
  if (!phoneFormRef.value) return

  try {
    await phoneFormRef.value.validate()
    phoneLoading.value = true

    const result = await userStore.bindPhone(phoneForm)
    if (result.success) {
      ElMessage.success('手机号绑定成功')
      phoneForm.phone = ''
      phoneForm.code = ''
      await refreshUserInfo()
    } else {
      ElMessage.error(result.message || '绑定失败')
    }
  } catch (error) {
    console.error('绑定手机号失败:', error)
    ElMessage.error('绑定失败，请重试')
  } finally {
    phoneLoading.value = false
  }
}

// 微信绑定相关方法
const handleBindWechat = async () => {
  try {
    wechatLoading.value = true
    const result = await userStore.bindWechat()
    if (result.success) {
      ElMessage.success('微信绑定成功')
      await refreshUserInfo()
    } else {
      ElMessage.error(result.message || '绑定失败')
    }
  } catch (error) {
    console.error('绑定微信失败:', error)
    ElMessage.error('绑定失败，请重试')
  } finally {
    wechatLoading.value = false
  }
}

const handleUnbindWechat = async () => {
  try {
    await ElMessageBox.confirm('确定要解绑微信吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 这里需要实现解绑微信的API
    ElMessage.info('解绑功能暂未实现')
  } catch (error) {
    // 用户取消操作
  }
}

// 刷新注册记录
const refreshRegistrations = async () => {
  loading.value = true
  try {
    const result = await meetingStore.fetchMyRegistrations()
    if (!result.success) {
      console.error('获取注册记录失败:', result.message)
      ElMessage.error('加载注册记录失败: ' + (result.message || '未知错误'))
    }
  } catch (error) {
    console.error('刷新注册记录异常:', error)
    ElMessage.error('加载注册记录失败，请重试')
  } finally {
    loading.value = false
  }
}

// 跳转到支付页面
const goToPayment = (registration) => {
  router.push({
    name: 'PaymentProcess',
    query: {
      registrationId: registration.id,
      meetingId: registration.meeting_id
    }
  })
}

// 获取支付状态类型
const getPaymentStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'paid': 'success',
    'failed': 'danger',
    'refunded': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取支付状态文本
const getPaymentStatusText = (status) => {
  const statusMap = {
    'pending': '待支付',
    'paid': '已支付',
    'failed': '支付失败',
    'refunded': '已退款'
  }
  return statusMap[status] || '未知'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取用户等级对应的标签类型
const getUserLevelType = (level) => {
  const typeMap = {
    'free': 'info',
    'paid': 'success',
    'premium': 'warning',
    'vip': 'danger'
  }
  return typeMap[level] || 'info'
}

onMounted(async () => {
  // 获取最新的用户信息，包括付费状态
  await refreshUserInfo()
  initForm()
  await refreshRegistrations()
})
</script>

<style scoped>
.profile-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  height: 100%;
}

.profile-header {
  margin-bottom: 20px;
}

.profile-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
}

.avatar-section {
  flex-shrink: 0;
}

.user-details {
  flex: 1;
}

.username {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.user-tags {
  margin-bottom: 15px;
}

.member-tag {
  margin-right: 8px;
  margin-bottom: 5px;
}

.user-desc {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.registration-fees {
  margin-bottom: 20px;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: bold;
}

.fee-amount {
  font-weight: bold;
  color: #E6A23C;
}

.my-registrations {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 40px;
  color: #909399;
}

.registration-notice {
  margin-bottom: 20px;
}

.payment-stats {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-info {
  margin-top: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.status-label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.status-time {
  font-size: 12px;
  color: #909399;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: bold;
}

:deep(.el-alert) {
  border-radius: 8px;
}

/* 新增样式 */
.profile-edit,
.phone-binding,
.wechat-binding {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.code-input {
  display: flex;
  align-items: center;
}

.wechat-content {
  text-align: center;
}

.wechat-bound,
.wechat-unbind {
  padding: 20px;
}

:deep(.el-result) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-input.is-disabled .el-input__inner) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

.member-type-hint {
  margin-top: 4px;
}

.member-type-hint .el-text {
  font-size: 12px;
}
</style>
