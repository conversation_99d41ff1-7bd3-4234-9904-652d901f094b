<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_info', function (Blueprint $table) {
            // 删除地址相关字段
            $table->dropColumn(['company_address', 'recipient_name', 'recipient_phone', 'recipient_address', 'recipient_email']);

            // 添加新的联系信息字段
            $table->string('contact_phone')->after('bank_account')->comment('联系电话');
            $table->string('contact_email')->nullable()->after('contact_phone')->comment('联系邮箱');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_info', function (Blueprint $table) {
            // 恢复原来的字段
            $table->dropColumn(['contact_phone', 'contact_email']);

            // 添加回原来的字段
            $table->string('company_address')->nullable()->after('tax_number')->comment('公司地址');
            $table->string('recipient_name')->after('bank_account')->comment('收件人姓名');
            $table->string('recipient_phone')->after('recipient_name')->comment('收件人电话');
            $table->string('recipient_address')->after('recipient_phone')->comment('收件人地址');
            $table->string('recipient_email')->nullable()->after('recipient_address')->comment('收件人邮箱');
        });
    }
};
