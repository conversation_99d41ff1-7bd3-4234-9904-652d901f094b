<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('is_paid_user')->default(false)->after('member_type')->comment('是否为付费用户');
            $table->timestamp('paid_at')->nullable()->after('is_paid_user')->comment('首次付费时间');
            $table->decimal('total_paid_amount', 10, 2)->default(0)->after('paid_at')->comment('累计付费金额');
            $table->integer('paid_meetings_count')->default(0)->after('total_paid_amount')->comment('付费会议数量');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'is_paid_user',
                'paid_at',
                'total_paid_amount',
                'paid_meetings_count'
            ]);
        });
    }
};
