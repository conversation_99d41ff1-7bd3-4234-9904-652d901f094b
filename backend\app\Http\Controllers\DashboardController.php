<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Submission;
use App\Models\SpeechSubmission;
use App\Models\MeetingRegistration;
use App\Models\Payment;

class DashboardController extends Controller
{
    /**
     * 获取仪表盘数据
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        // 获取用户基本信息
        $userInfo = [
            'id' => $user->id,
            'name' => $user->name,
            'real_name' => $user->real_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'member_type' => $user->member_type,
            'is_paid_user' => $user->is_paid_user,
            'user_level' => $this->getUserLevel($user),
            'user_level_label' => $this->getUserLevelLabel($user),
            'paid_at' => $user->paid_at,
            'avatar' => $user->avatar,
        ];

        // 获取统计数据
        $statistics = [
            'submissions_count' => $user->submissions()->count(),
            'speech_submissions_count' => $user->speechSubmissions()->count(),
            'total_papers' => $user->submissions()->count() + $user->speechSubmissions()->count(),
            'meeting_registrations_count' => $user->meetingRegistrations()->count(),
        ];

        // 获取支付信息
        $paymentInfo = [
            'total_paid_amount' => $user->total_paid_amount ?? '0.00',
            'paid_meetings_count' => $user->paid_meetings_count ?? 0,
            'unpaid_registrations_count' => $user->meetingRegistrations()
                ->where('payment_status', '!=', 'paid')
                ->count(),
            'recent_payments' => $user->payments()
                ->with('meetingRegistration.meeting')
                ->where('status', 'success')
                ->orderBy('paid_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($payment) {
                    return [
                        'id' => $payment->id,
                        'amount' => $payment->amount,
                        'paid_at' => $payment->paid_at,
                        'meeting_title' => $payment->meetingRegistration->meeting->title ?? '未知会议',
                        'transaction_id' => $payment->transaction_id,
                    ];
                }),
        ];

        // 获取未缴费的会议注册
        $unpaidRegistrations = $user->meetingRegistrations()
            ->with('meeting')
            ->where('payment_status', '!=', 'paid')
            ->get()
            ->map(function ($registration) {
                return [
                    'id' => $registration->id,
                    'meeting_title' => $registration->meeting->title,
                    'fee_amount' => $registration->fee_amount,
                    'registration_type' => $registration->registration_type,
                    'registered_at' => $registration->registered_at,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'user_info' => $userInfo,
                'statistics' => $statistics,
                'payment_info' => $paymentInfo,
                'unpaid_registrations' => $unpaidRegistrations,
            ]
        ]);
    }

    /**
     * 获取用户等级
     */
    private function getUserLevel(User $user): string
    {
        if (!$user->is_paid_user) {
            return 'free';
        }

        $meetingsCount = $user->paid_meetings_count ?? 0;
        if ($meetingsCount >= 5) {
            return 'vip';
        }
        if ($meetingsCount >= 2) {
            return 'premium';
        }
        return 'paid';
    }

    /**
     * 获取用户等级标签
     */
    private function getUserLevelLabel(User $user): string
    {
        $level = $this->getUserLevel($user);
        $labels = [
            'free' => '免费用户',
            'paid' => '付费用户',
            'premium' => '高级用户',
            'vip' => 'VIP用户'
        ];

        return $labels[$level] ?? '普通用户';
    }
}
