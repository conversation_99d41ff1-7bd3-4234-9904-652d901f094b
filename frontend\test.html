<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部署测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-item {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .success { border-left: 4px solid #4CAF50; }
        .error { border-left: 4px solid #f44336; }
        .info { border-left: 4px solid #2196F3; }
    </style>
</head>
<body>
    <h1>🔧 前端部署测试页面</h1>
    
    <div class="test-item info">
        <h3>📍 当前访问地址</h3>
        <p id="current-url"></p>
    </div>

    <div class="test-item" id="static-test">
        <h3>📁 静态资源测试</h3>
        <p>测试CSS文件加载：<span id="css-status">检测中...</span></p>
        <p>测试JS文件加载：<span id="js-status">检测中...</span></p>
    </div>

    <div class="test-item" id="api-test">
        <h3>🌐 API连接测试</h3>
        <p>API地址：<span id="api-url"></span></p>
        <p>连接状态：<span id="api-status">检测中...</span></p>
    </div>

    <div class="test-item info">
        <h3>💡 解决建议</h3>
        <div id="suggestions"></div>
    </div>

    <script>
        // 显示当前URL
        document.getElementById('current-url').textContent = window.location.href;

        // 测试静态资源
        function testStaticResources() {
            // 测试CSS
            const cssPath = '/h5/assets/css/index-5GF16OIB.css';
            fetch(cssPath)
                .then(response => {
                    if (response.ok) {
                        document.getElementById('css-status').innerHTML = '<span style="color: green;">✅ 成功</span>';
                    } else {
                        document.getElementById('css-status').innerHTML = '<span style="color: red;">❌ 失败 (' + response.status + ')</span>';
                    }
                })
                .catch(error => {
                    document.getElementById('css-status').innerHTML = '<span style="color: red;">❌ 网络错误</span>';
                });

            // 测试JS
            const jsPath = '/h5/assets/js/index-BfhMU0Gw.js';
            fetch(jsPath)
                .then(response => {
                    if (response.ok) {
                        document.getElementById('js-status').innerHTML = '<span style="color: green;">✅ 成功</span>';
                    } else {
                        document.getElementById('js-status').innerHTML = '<span style="color: red;">❌ 失败 (' + response.status + ')</span>';
                    }
                })
                .catch(error => {
                    document.getElementById('js-status').innerHTML = '<span style="color: red;">❌ 网络错误</span>';
                });
        }

        // 测试API连接
        function testAPI() {
            const apiUrl = '/api/test';
            document.getElementById('api-url').textContent = window.location.origin + apiUrl;
            
            fetch(apiUrl)
                .then(response => {
                    if (response.ok) {
                        document.getElementById('api-status').innerHTML = '<span style="color: green;">✅ API连接正常</span>';
                    } else {
                        document.getElementById('api-status').innerHTML = '<span style="color: orange;">⚠️ API返回错误 (' + response.status + ')</span>';
                    }
                })
                .catch(error => {
                    document.getElementById('api-status').innerHTML = '<span style="color: red;">❌ API连接失败</span>';
                });
        }

        // 生成建议
        function generateSuggestions() {
            const suggestions = [
                '1. 确保所有文件都上传到了正确的 /h5/ 目录下',
                '2. 检查服务器是否支持SPA路由（需要将所有请求重定向到index.html）',
                '3. 确认服务器MIME类型配置正确',
                '4. 检查API服务是否正常运行',
                '5. 如果使用CDN，确保缓存已更新'
            ];
            
            document.getElementById('suggestions').innerHTML = suggestions.map(s => '<p>' + s + '</p>').join('');
        }

        // 执行测试
        testStaticResources();
        testAPI();
        generateSuggestions();
    </script>
</body>
</html>
