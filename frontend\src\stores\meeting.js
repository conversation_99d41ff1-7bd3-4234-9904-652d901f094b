import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as meetingApi from '@/api/meeting'

export const useMeetingStore = defineStore('meeting', () => {
  const meetings = ref([])
  const currentMeeting = ref(null)
  const myRegistrations = ref([])
  const loading = ref(false)

  // 计算属性
  const publishedMeetings = computed(() => 
    meetings.value.filter(meeting => meeting.status === 'published')
  )

  const upcomingMeetings = computed(() => 
    meetings.value.filter(meeting => 
      meeting.status === 'published' && 
      new Date(meeting.start_time) > new Date()
    )
  )

  // 获取会议列表
  const fetchMeetings = async (params = {}) => {
    try {
      loading.value = true
      const response = await meetingApi.getMeetings(params)
      meetings.value = response.data
      return { success: true }
    } catch (error) {
      return { success: false, message: error.message }
    } finally {
      loading.value = false
    }
  }

  // 获取会议详情
  const fetchMeetingDetail = async (id) => {
    try {
      loading.value = true
      const response = await meetingApi.getMeetingDetail(id)
      currentMeeting.value = response.data
      return { success: true }
    } catch (error) {
      return { success: false, message: error.message }
    } finally {
      loading.value = false
    }
  }

  // 注册会议
  const registerMeeting = async (id, registrationData) => {
    try {
      const response = await meetingApi.registerMeeting(id, registrationData)
      
      // 更新我的注册记录
      await fetchMyRegistrations()
      
      return { success: true, data: response.data, message: response.message }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  // 获取我的注册记录
  const fetchMyRegistrations = async () => {
    try {
      const response = await meetingApi.getMyRegistrations()
      // 确保数据是数组格式
      if (response && response.data) {
        // 处理分页数据或直接数组数据
        const data = response.data.data || response.data
        myRegistrations.value = Array.isArray(data) ? data : []
      } else {
        myRegistrations.value = []
      }
      return { success: true }
    } catch (error) {
      console.error('获取注册记录失败:', error)
      myRegistrations.value = []
      return { success: false, message: error.message }
    }
  }

  // 检查是否已注册某个会议
  const isRegistered = (meetingId) => {
    return myRegistrations.value.some(
      registration => registration.meeting_id === meetingId
    )
  }

  // 获取会议注册记录
  const getRegistration = (meetingId) => {
    return myRegistrations.value.find(
      registration => registration.meeting_id === meetingId
    )
  }

  // 计算会议费用
  const calculateFee = (meeting, memberType) => {
    if (!meeting) return 0

    const now = new Date()
    const earlyDeadline = new Date(meeting.early_deadline)
    const isEarlyBird = now < earlyDeadline

    let fee
    switch (memberType) {
      case 'student':
        fee = isEarlyBird ? meeting.student_fee_early : meeting.student_fee_normal
        break
      case 'corporate':
        fee = isEarlyBird ? meeting.corporate_fee_early : meeting.corporate_fee_normal
        break
      case 'expert':
      default:
        fee = isEarlyBird ? meeting.expert_fee_early : meeting.expert_fee_normal
        break
    }

    // 确保返回数字类型
    return parseFloat(fee) || 0
  }

  // 检查会议是否可以注册
  const canRegister = (meeting) => {
    if (!meeting) return false
    
    const now = new Date()
    const registrationDeadline = new Date(meeting.registration_deadline)
    
    return meeting.status === 'published' && 
           now < registrationDeadline &&
           (meeting.max_participants === null || 
            meeting.registered_count < meeting.max_participants)
  }

  // 清空当前会议
  const clearCurrentMeeting = () => {
    currentMeeting.value = null
  }

  // 重置状态
  const reset = () => {
    meetings.value = []
    currentMeeting.value = null
    myRegistrations.value = []
    loading.value = false
  }

  return {
    // 状态
    meetings,
    currentMeeting,
    myRegistrations,
    loading,
    
    // 计算属性
    publishedMeetings,
    upcomingMeetings,
    
    // 方法
    fetchMeetings,
    fetchMeetingDetail,
    registerMeeting,
    fetchMyRegistrations,
    isRegistered,
    getRegistration,
    calculateFee,
    canRegister,
    clearCurrentMeeting,
    reset
  }
})
