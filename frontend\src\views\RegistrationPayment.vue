<template>
  <div class="registration-payment">
    <el-card>
      <template #header>
        <h2>报名和缴费</h2>
      </template>

      <el-tabs v-model="activeTab" type="border-card">
        <!-- 网站报名缴费 -->
        <el-tab-pane label="网站报名缴费" name="website">
          <div class="tab-content">
            <div class="sub-tabs">
              <el-button 
                :type="activeSubTab === 'expert' ? 'primary' : ''"
                @click="activeSubTab = 'expert'"
              >
                报名缴费地址-专家
              </el-button>
              <el-button 
                :type="activeSubTab === 'student' ? 'primary' : ''"
                @click="activeSubTab = 'student'"
              >
                报名缴费地址-学生
              </el-button>
              <el-button 
                :type="activeSubTab === 'student2' ? 'primary' : ''"
                @click="activeSubTab = 'student2'"
              >
                报名缴费地址-学生
              </el-button>
            </div>

            <div class="content-area">
              <div class="meeting-info">
                <p><strong>会议报名费用：300-500元</strong></p>
                <p><strong>缴费截止日期：2025年7月26日之前</strong></p>
              </div>

              <div class="form-area">
                <el-form :model="registrationForm" label-width="120px">
                  <el-form-item label="参会类型">
                    <el-select v-model="registrationForm.type" placeholder="请选择参会类型">
                      <el-option label="正式代表" value="expert" />
                      <el-option label="学生代表" value="student" />
                      <el-option label="企业代表" value="corporate" />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="姓名">
                    <el-input v-model="registrationForm.name" placeholder="请输入姓名" />
                  </el-form-item>

                  <el-form-item label="单位">
                    <el-input v-model="registrationForm.organization" placeholder="请输入工作单位" />
                  </el-form-item>

                  <el-form-item label="联系电话">
                    <el-input v-model="registrationForm.phone" placeholder="请输入联系电话" />
                  </el-form-item>

                  <el-form-item label="邮箱">
                    <el-input v-model="registrationForm.email" placeholder="请输入邮箱地址" />
                  </el-form-item>

                  <el-form-item label="备注">
                    <el-input 
                      v-model="registrationForm.remarks" 
                      type="textarea" 
                      :rows="4"
                      placeholder="请输入备注信息"
                    />
                  </el-form-item>

                  <!-- 新增开票信息部分 -->
                  <el-divider content-position="left">开票信息</el-divider>

                  <el-form-item label="发票类型" prop="invoiceType">
                    <el-radio-group v-model="registrationForm.invoiceType">
                      <el-radio label="personal">个人</el-radio>
                      <el-radio label="company">企业</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <el-form-item
                    v-if="registrationForm.invoiceType === 'company'"
                    label="企业名称"
                    prop="companyName"
                  >
                    <el-input v-model="registrationForm.companyName" placeholder="请输入企业名称" />
                  </el-form-item>

                  <el-form-item
                    v-if="registrationForm.invoiceType === 'company'"
                    label="税号"
                    prop="taxNumber"
                  >
                    <el-input v-model="registrationForm.taxNumber" placeholder="请输入税号" />
                  </el-form-item>

                  <el-form-item label="发票抬头" prop="invoiceTitle">
                    <el-input v-model="registrationForm.invoiceTitle" placeholder="请输入发票抬头" />
                  </el-form-item>

                  <el-form-item label="开户银行" prop="bankName">
                    <el-input v-model="registrationForm.bankName" placeholder="请输入开户银行" />
                  </el-form-item>

                  <el-form-item label="银行账号" prop="bankAccount">
                    <el-input v-model="registrationForm.bankAccount" placeholder="请输入银行账号" />
                  </el-form-item>

                  <el-form-item label="联系人" prop="invoiceContact">
                    <el-input v-model="registrationForm.invoiceContact" placeholder="请输入开票联系人姓名" />
                  </el-form-item>

                  <el-form-item label="联系电话" prop="invoicePhone">
                    <el-input v-model="registrationForm.invoicePhone" placeholder="请输入开票联系电话" />
                  </el-form-item>

                  <el-form-item label="收票邮箱" prop="invoiceEmail">
                    <el-input v-model="registrationForm.invoiceEmail" placeholder="请输入接收发票的邮箱" />
                  </el-form-item>
                </el-form>

                <div class="form-actions">
                  <el-checkbox v-model="agreedToTerms">已阅读条款</el-checkbox>
                  <el-button 
                    type="primary" 
                    :disabled="!agreedToTerms"
                    @click="submitRegistration"
                  >
                    开始缴费
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 现场开票地址 -->
        <el-tab-pane label="现场开票地址" name="onsite">
          <div class="tab-content">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="开票地点">
                会议现场财务处
              </el-descriptions-item>
              <el-descriptions-item label="开票时间">
                会议期间 9:00-17:00
              </el-descriptions-item>
              <el-descriptions-item label="所需材料">
                身份证、缴费凭证、发票抬头信息
              </el-descriptions-item>
              <el-descriptions-item label="联系电话">
                010-12345678
              </el-descriptions-item>
            </el-descriptions>

            <el-alert
              title="温馨提示"
              type="info"
              :closable="false"
              style="margin-top: 20px;"
            >
              现场开票需要排队等候，建议提前准备好相关材料。如需增值税专用发票，请提前联系财务部门。
            </el-alert>
          </div>
        </el-tab-pane>

        <!-- 缴费凭证 -->
        <el-tab-pane label="缴费凭证" name="receipt">
          <div class="tab-content">
            <el-upload
              class="upload-demo"
              drag
              action="#"
              :on-change="handleFileChange"
              :auto-upload="false"
              accept=".jpg,.jpeg,.png,.pdf"
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                将缴费凭证拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  支持 jpg/png/pdf 文件，且不超过 10MB
                </div>
              </template>
            </el-upload>

            <div class="receipt-list" v-if="uploadedFiles.length > 0">
              <h4>已上传的凭证：</h4>
              <el-list>
                <el-list-item v-for="file in uploadedFiles" :key="file.name">
                  <div class="file-info">
                    <el-icon><Document /></el-icon>
                    <span>{{ file.name }}</span>
                    <el-button type="danger" size="small" @click="removeFile(file)">
                      删除
                    </el-button>
                  </div>
                </el-list-item>
              </el-list>
            </div>
          </div>
        </el-tab-pane>

        <!-- 特殊政策 -->
        <el-tab-pane label="特殊政策" name="policy">
          <div class="tab-content">
            <div class="policy-content">
              <h3>会议优惠政策</h3>
              
              <el-collapse v-model="activePolicyItems">
                <el-collapse-item title="学生优惠政策" name="student">
                  <p>1. 在校学生凭学生证可享受优惠价格</p>
                  <p>2. 博士生、硕士生、本科生均可享受学生价</p>
                  <p>3. 需提供有效学生证明材料</p>
                </el-collapse-item>
                
                <el-collapse-item title="团体报名优惠" name="group">
                  <p>1. 5人以上团体报名可享受9折优惠</p>
                  <p>2. 10人以上团体报名可享受8.5折优惠</p>
                  <p>3. 需统一报名和缴费</p>
                </el-collapse-item>
                
                <el-collapse-item title="早鸟优惠" name="early">
                  <p>1. 7月15日前报名可享受早鸟价格</p>
                  <p>2. 早鸟价格比正常价格优惠200元</p>
                  <p>3. 名额有限，先到先得</p>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </el-tab-pane>

        <!-- 更改政策 -->
        <el-tab-pane label="更改政策" name="change">
          <div class="tab-content">
            <div class="change-policy">
              <h3>报名信息更改政策</h3>
              
              <el-timeline>
                <el-timeline-item timestamp="报名后24小时内" type="primary">
                  可免费更改报名信息（姓名、单位、联系方式等）
                </el-timeline-item>
                <el-timeline-item timestamp="报名后24-72小时" type="warning">
                  更改报名信息需支付50元手续费
                </el-timeline-item>
                <el-timeline-item timestamp="会议前7天内" type="danger">
                  不支持更改报名信息，如有特殊情况请联系组委会
                </el-timeline-item>
              </el-timeline>

              <el-alert
                title="退费政策"
                type="warning"
                :closable="false"
                style="margin-top: 20px;"
              >
                <p>1. 会议前30天申请退费，可退还80%费用</p>
                <p>2. 会议前15天申请退费，可退还50%费用</p>
                <p>3. 会议前7天内不支持退费</p>
                <p>4. 退费申请需提供书面说明和相关证明</p>
              </el-alert>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, UploadFilled } from '@element-plus/icons-vue'

const activeTab = ref('website')
const activeSubTab = ref('expert')
const agreedToTerms = ref(false)
const activePolicyItems = ref(['student'])
const uploadedFiles = ref([])

const registrationForm = reactive({
  type: '',
  name: '',
  organization: '',
  phone: '',
  email: '',
  remarks: '',
  // 开票信息字段
  invoiceType: 'personal',
  companyName: '',
  taxNumber: '',
  invoiceTitle: '',
  bankName: '',
  bankAccount: '',
  invoiceContact: '',
  invoicePhone: '',
  invoiceEmail: ''
})

const submitRegistration = () => {
  if (!agreedToTerms.value) {
    ElMessage.warning('请先阅读并同意相关条款')
    return
  }
  
  // 验证开票信息
  if (!registrationForm.invoiceTitle || !registrationForm.invoiceEmail ||
      !registrationForm.bankName || !registrationForm.bankAccount ||
      !registrationForm.invoiceContact || !registrationForm.invoicePhone) {
    ElMessage.warning('请完善开票信息')
    return
  }

  if (registrationForm.invoiceType === 'company' &&
      (!registrationForm.companyName || !registrationForm.taxNumber)) {
    ElMessage.warning('请完善企业开票信息')
    return
  }

  ElMessage.success('报名信息提交成功，请完成缴费')
  // 跳转到支付页面
  // router.push('/payment-process')
}

const handleFileChange = (file) => {
  uploadedFiles.value.push(file)
  ElMessage.success('文件上传成功')
}

const removeFile = (file) => {
  const index = uploadedFiles.value.indexOf(file)
  if (index > -1) {
    uploadedFiles.value.splice(index, 1)
    ElMessage.success('文件删除成功')
  }
}
</script>

<style scoped>
.registration-payment {
  padding: 20px;
  min-height: calc(100vh - 60px);
  background-color: #f5f7fa;
}

.tab-content {
  padding: 20px;
}

.sub-tabs {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.content-area {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 20px;
  background-color: #fff;
}

.meeting-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f0f9ff;
  border-left: 4px solid #409eff;
}

.meeting-info p {
  margin: 5px 0;
  color: #303133;
}

.form-area {
  margin-top: 20px;
}

.form-actions {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.receipt-list {
  margin-top: 20px;
}

.receipt-list h4 {
  color: #303133;
  margin-bottom: 10px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.file-info span {
  flex: 1;
}

.policy-content,
.change-policy {
  max-width: 800px;
}

.policy-content h3,
.change-policy h3 {
  color: #303133;
  margin-bottom: 20px;
}

:deep(.el-collapse-item__content) {
  padding: 15px 20px;
}

:deep(.el-timeline-item__content) {
  padding-left: 15px;
}
</style>
