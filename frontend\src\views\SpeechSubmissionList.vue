<template>
  <div class="speech-submission-list">
    <div class="page-header">
      <h2>会议发言投稿</h2>
      <el-button 
        v-if="userStore.isPaidUser" 
        type="primary" 
        @click="goToCreate"
      >
        <el-icon><Plus /></el-icon>
        新建发言投稿
      </el-button>
    </div>

    <el-card>
      <!-- 未付费用户提示 -->
      <div v-if="!userStore.isPaidUser" class="unpaid-notice">
        <el-alert
          title="会议发言投稿功能仅限付费用户使用"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="unpaid-content">
              <p>您需要先完成会议注册并支付费用后才能使用会议发言投稿功能。</p>
              <div class="unpaid-actions">
                <el-button type="primary" @click="goToRegistration">
                  立即注册会议
                </el-button>
                <el-button @click="goToProfile">
                  查看我的注册状态
                </el-button>
              </div>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 付费用户内容 -->
      <div v-else>
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>

        <div v-else-if="speechSubmissions.length === 0" class="empty-state">
          <el-empty description="暂无会议发言投稿">
            <el-button type="primary" @click="goToCreate">立即投稿</el-button>
          </el-empty>
        </div>

        <div v-else>
          <el-table :data="speechSubmissions" style="width: 100%">
            <el-table-column prop="title" label="发言标题" min-width="200">
              <template #default="{ row }">
                <el-link @click="goToDetail(row.id)" type="primary">
                  {{ row.title }}
                </el-link>
              </template>
            </el-table-column>

            <el-table-column prop="speaker_name" label="发言人" width="120" />

            <el-table-column prop="speech_type_label" label="发言类型" width="120" />

            <el-table-column prop="duration" label="时长" width="80">
              <template #default="{ row }">
                {{ row.duration }}分钟
              </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" width="120">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ row.status_label }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="submitted_at" label="提交时间" width="180">
              <template #default="{ row }">
                {{ row.submitted_at || '未提交' }}
              </template>
            </el-table-column>

            <el-table-column prop="created_at" label="创建时间" width="180" />

            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="goToDetail(row.id)">查看</el-button>
                <el-button 
                  v-if="row.can_edit" 
                  size="small" 
                  type="primary" 
                  @click="goToEdit(row.id)"
                >
                  编辑
                </el-button>
                <el-button 
                  v-if="row.status === 'draft'" 
                  size="small" 
                  type="success" 
                  @click="handleSubmit(row)"
                >
                  提交
                </el-button>
                <el-button 
                  v-if="row.can_edit" 
                  size="small" 
                  type="danger" 
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { 
  getSpeechSubmissionList, 
  deleteSpeechSubmission, 
  submitSpeechSubmission 
} from '@/api/speechSubmission'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const speechSubmissions = ref([])

// 检查付费用户权限
const checkPaidUser = () => {
  if (!userStore.isPaidUser) {
    ElMessage.warning('会议发言投稿功能仅限付费用户使用，请先完成会议注册并支付费用')
    router.push('/meeting-registration')
    return false
  }
  return true
}

// 获取会议发言投稿列表
const loadSpeechSubmissions = async () => {
  if (!checkPaidUser()) return
  
  loading.value = true
  try {
    const response = await getSpeechSubmissionList()
    if (response.success) {
      speechSubmissions.value = response.data
    }
  } catch (error) {
    console.error('获取会议发言投稿列表失败:', error)
    
    // 检查是否是未付费用户错误
    if (error.response?.data?.error_code === 'UNPAID_USER') {
      ElMessage.warning(error.response.data.message)
      router.push('/meeting-registration')
      return
    }
    
    ElMessage.error('获取会议发言投稿列表失败')
  } finally {
    loading.value = false
  }
}

// 获取状态标签类型
const getStatusType = (status) => {
  const typeMap = {
    'draft': 'info',
    'submitted': 'warning',
    'under_review': 'primary',
    'accepted': 'success',
    'rejected': 'danger',
    'revision_required': 'warning'
  }
  return typeMap[status] || 'info'
}

// 跳转到创建页面
const goToCreate = () => {
  if (!checkPaidUser()) return
  router.push('/speech-submission/create')
}

// 跳转到详情页面
const goToDetail = (id) => {
  router.push(`/speech-submission/${id}`)
}

// 跳转到编辑页面
const goToEdit = (id) => {
  router.push(`/speech-submission/${id}/edit`)
}

// 跳转到会议注册页面
const goToRegistration = () => {
  router.push('/meeting-registration')
}

// 跳转到个人中心
const goToProfile = () => {
  router.push('/profile')
}

// 提交投稿
const handleSubmit = async (submission) => {
  try {
    await ElMessageBox.confirm(
      `确定要提交发言投稿"${submission.title}"吗？提交后将无法修改。`,
      '确认提交',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await submitSpeechSubmission(submission.id)
    if (response.success) {
      ElMessage.success('发言投稿提交成功')
      await loadSpeechSubmissions()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交发言投稿失败:', error)
      ElMessage.error('提交发言投稿失败')
    }
  }
}

// 删除投稿
const handleDelete = async (submission) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除发言投稿"${submission.title}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await deleteSpeechSubmission(submission.id)
    if (response.success) {
      ElMessage.success('发言投稿删除成功')
      await loadSpeechSubmissions()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除发言投稿失败:', error)
      ElMessage.error('删除发言投稿失败')
    }
  }
}

onMounted(() => {
  // 只有付费用户才加载投稿列表
  if (userStore.isPaidUser) {
    loadSpeechSubmissions()
  }
})
</script>

<style scoped>
.speech-submission-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.loading-container {
  padding: 20px;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.unpaid-notice {
  margin-bottom: 20px;
}

.unpaid-content {
  margin: 15px 0;
}

.unpaid-content p {
  margin: 0 0 15px 0;
  color: #606266;
  line-height: 1.6;
}

.unpaid-actions {
  display: flex;
  gap: 12px;
}
</style>
