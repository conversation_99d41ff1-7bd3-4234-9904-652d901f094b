import request from '@/utils/request'

/**
 * 获取会议列表
 * @param {Object} params - 查询参数
 * @param {number} params.per_page - 每页数量
 */
export const getMeetings = (params = {}) => {
  return request.get('/meetings', { params })
}

/**
 * 获取会议详情
 * @param {number} id - 会议ID
 */
export const getMeetingDetail = (id) => {
  return request.get(`/meetings/${id}`)
}

/**
 * 注册会议
 * @param {number} id - 会议ID
 * @param {Object} registrationData - 注册数据
 * @param {string} registrationData.registration_type - 注册类型 (expert/student/corporate)
 * @param {string} registrationData.notes - 备注
 */
export const registerMeeting = (id, registrationData) => {
  return request.post(`/meetings/${id}/register`, registrationData)
}

/**
 * 获取我的会议注册记录
 */
export const getMyRegistrations = () => {
  return request.get('/meetings/my-registrations')
}
