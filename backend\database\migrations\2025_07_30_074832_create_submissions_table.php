<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('submissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade')->comment('投稿用户ID');
            $table->string('title')->comment('论文标题');
            $table->text('abstract')->comment('论文摘要');
            $table->json('authors')->comment('作者信息JSON');
            $table->string('corresponding_author')->comment('通讯作者');
            $table->string('corresponding_email')->comment('通讯作者邮箱');
            $table->string('corresponding_phone')->comment('通讯作者电话');
            $table->string('institution')->comment('所属机构');
            $table->string('department')->nullable()->comment('所属科室/部门');
            $table->json('keywords')->comment('关键词JSON数组');
            $table->enum('submission_type', ['oral', 'poster'])->comment('投稿类型：oral-口头报告，poster-壁报展示');
            $table->string('research_field')->comment('研究领域');
            $table->text('funding_info')->nullable()->comment('基金资助信息');
            $table->string('file_path')->nullable()->comment('论文文件路径');
            $table->string('original_filename')->nullable()->comment('原始文件名');
            $table->integer('file_size')->nullable()->comment('文件大小(字节)');
            $table->enum('status', ['draft', 'submitted', 'under_review', 'accepted', 'rejected', 'revision_required'])->default('draft')->comment('投稿状态');
            $table->text('review_comments')->nullable()->comment('审稿意见');
            $table->timestamp('submitted_at')->nullable()->comment('提交时间');
            $table->timestamp('reviewed_at')->nullable()->comment('审稿时间');
            $table->timestamps();

            // 添加索引
            $table->index('user_id');
            $table->index('status');
            $table->index('submission_type');
            $table->index('submitted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('submissions');
    }
};
