<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\SmsService;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SmsController extends Controller
{
    private $smsService;

    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * 发送短信验证码
     */
    public function sendCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|regex:/^1[3-9]\d{9}$/',
            'type' => 'in:login,register,bind' // 验证码类型
        ], [
            'phone.required' => '手机号不能为空',
            'phone.regex' => '手机号格式不正确',
            'type.in' => '验证码类型不正确'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }

        $phone = $request->input('phone');
        $type = $request->input('type', 'login');

        // 检查是否可以发送短信
        $canSend = $this->smsService->canSendSms($phone);
        if (!$canSend['can_send']) {
            return response()->json([
                'success' => false,
                'message' => $canSend['message'],
                'wait_seconds' => $canSend['wait_seconds'] ?? 0
            ], 429);
        }

        // 根据类型进行不同的验证
        if ($type === 'register') {
            // 注册时检查手机号是否已存在
            if (User::where('phone', $phone)->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => '该手机号已注册，请直接登录'
                ], 400);
            }
        }
        // 移除登录时的注册检查，因为登录接口支持自动注册

        // 生成验证码
        $code = $this->smsService->generateCode();

        // 发送短信
        $result = $this->smsService->sendVerificationCode($phone, $code);

        if ($result['success']) {
            // 存储验证码
            $this->smsService->storeCode($phone, $code);

            Log::info('短信验证码发送成功', [
                'phone' => $phone,
                'type' => $type,
                'code' => $code // 开发环境记录，生产环境应该移除
            ]);

            return response()->json([
                'success' => true,
                'message' => '验证码发送成功',
                'data' => [
                    'phone' => $phone,
                    'expires_in' => 300, // 5分钟
                    'dev_code' => app()->environment('local') ? $code : null // 开发环境返回验证码
                ]
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['message'],
                'error_code' => $result['error_code'] ?? null
            ], 500);
        }
    }

    /**
     * 短信验证码登录
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|regex:/^1[3-9]\d{9}$/',
            'code' => 'required|string|size:6'
        ], [
            'phone.required' => '手机号不能为空',
            'phone.regex' => '手机号格式不正确',
            'code.required' => '验证码不能为空',
            'code.size' => '验证码必须是6位数字'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }

        $phone = $request->input('phone');
        $code = $request->input('code');

        // 验证验证码
        $verifyResult = $this->smsService->verifyCode($phone, $code);
        if (!$verifyResult['success']) {
            return response()->json([
                'success' => false,
                'message' => $verifyResult['message']
            ], 400);
        }

        // 查找或创建用户
        $user = User::where('phone', $phone)->first();

        if (!$user) {
            // 如果用户不存在，自动创建
            $user = User::create([
                'name' => '手机用户' . substr($phone, -4),
                'phone' => $phone,
                'email' => $phone . '@sms.local', // 临时邮箱
                'password' => bcrypt(Str::random(32)), // 随机密码
                'member_type' => 'expert', // 默认为专家
                'phone_verified_at' => now()
            ]);

            Log::info('短信登录自动创建用户', [
                'phone' => $phone,
                'user_id' => $user->id
            ]);
        } else {
            // 更新手机验证时间
            $user->update(['phone_verified_at' => now()]);
        }

        // 生成token
        $token = $user->createToken('sms_login')->plainTextToken;

        Log::info('短信验证码登录成功', [
            'phone' => $phone,
            'user_id' => $user->id
        ]);

        return response()->json([
            'success' => true,
            'message' => '登录成功',
            'data' => [
                'user' => $user,
                'token' => $token
            ]
        ]);
    }

    /**
     * 绑定手机号
     */
    public function bindPhone(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|regex:/^1[3-9]\d{9}$/|unique:users,phone',
            'code' => 'required|string|size:6'
        ], [
            'phone.required' => '手机号不能为空',
            'phone.regex' => '手机号格式不正确',
            'phone.unique' => '该手机号已被其他用户绑定',
            'code.required' => '验证码不能为空',
            'code.size' => '验证码必须是6位数字'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }

        $phone = $request->input('phone');
        $code = $request->input('code');

        // 验证验证码
        $verifyResult = $this->smsService->verifyCode($phone, $code);
        if (!$verifyResult['success']) {
            return response()->json([
                'success' => false,
                'message' => $verifyResult['message']
            ], 400);
        }

        // 绑定手机号
        $user = $request->user();
        $user->update([
            'phone' => $phone,
            'phone_verified_at' => now()
        ]);

        Log::info('用户绑定手机号成功', [
            'user_id' => $user->id,
            'phone' => $phone
        ]);

        return response()->json([
            'success' => true,
            'message' => '手机号绑定成功',
            'data' => [
                'user' => $user->fresh()
            ]
        ]);
    }

    /**
     * 获取短信配置状态
     */
    public function getConfigStatus()
    {
        $status = $this->smsService->getConfigStatus();

        return response()->json([
            'success' => true,
            'message' => '短信配置状态',
            'data' => $status
        ]);
    }
}
