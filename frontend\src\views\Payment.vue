<template>
  <div class="payment">
    <!-- 支付记录 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <h2>支付记录（已支付）</h2>
          <el-button type="primary" @click="refreshPayments">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
        
        <el-table
          :data="paymentHistory"
          :loading="loading"
          style="width: 100%"
          stripe
        >
          <el-table-column prop="order_no" label="订单号" width="180">
            <template #default="scope">
              <el-text type="info" size="small">{{ scope.row.order_no }}</el-text>
            </template>
          </el-table-column>
          <el-table-column prop="date" label="支付时间" width="160" />
          <el-table-column prop="amount" label="支付金额" width="120">
            <template #default="scope">
              <span class="amount">¥{{ scope.row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="method" label="支付方式" width="120">
            <template #default="scope">
              <el-tag
                :type="scope.row.method === '微信支付' ? 'success' : 'primary'"
                size="small"
              >
                {{ scope.row.method }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="200" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag type="success">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空状态 -->
        <el-empty v-if="!paymentHistory.length" description="暂无已支付记录" />
      </el-card>

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { getMyPayments } from '@/api/payment'

const userStore = useUserStore()
const loading = ref(false)

// 支付记录数据
const paymentHistory = ref([])

// 获取支付记录
const fetchPaymentHistory = async () => {
  try {
    loading.value = true

    // 调用API
    const result = await getMyPayments()

    // 转换数据格式以适配表格显示
    paymentHistory.value = result.data.data.map(payment => ({
      id: payment.id,
      order_no: payment.transaction_id,
      date: formatDateTime(payment.created_at),
      amount: payment.amount,
      method: getPaymentMethodName(payment.payment_method),
      description: getPaymentDescription(payment),
      status: getPaymentStatusText(payment.status)
    }))
  } catch (error) {
    console.error('获取支付记录失败:', error)
    ElMessage.error('获取支付记录失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期时间
const formatDateTime = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取支付方式名称
const getPaymentMethodName = (method) => {
  const methodMap = {
    'wechat_native': '微信支付',
    'wechat_jsapi': '微信支付',
    'alipay': '支付宝'
  }
  return methodMap[method] || method
}

// 获取支付描述
const getPaymentDescription = (payment) => {
  if (payment.meeting_registration && payment.meeting_registration.meeting) {
    return `会议注册费用 - ${payment.meeting_registration.meeting.title}`
  }
  return '会议注册费用'
}

// 获取支付状态文本
const getPaymentStatusText = (status) => {
  const statusMap = {
    'pending': '待支付',
    'success': '成功',
    'failed': '失败',
    'cancelled': '已取消',
    'refunded': '已退款'
  }
  return statusMap[status] || status
}

// 刷新支付记录
const refreshPayments = () => {
  fetchPaymentHistory()
}

// 页面加载时获取数据
onMounted(() => {
  fetchPaymentHistory()
})
</script>

<style scoped>
.payment {
  padding: 20px;
  min-height: calc(100vh - 60px);
  background-color: #f5f7fa;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
}

.amount {
  font-weight: bold;
  color: #e6a23c;
  font-size: 16px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover td) {
  background-color: #f8f9fa;
}

/* 状态标签样式 */
:deep(.el-tag) {
  font-weight: 500;
}

/* 空状态样式 */
:deep(.el-empty) {
  padding: 60px 0;
}
</style>
