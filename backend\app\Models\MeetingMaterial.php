<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;

class MeetingMaterial extends Model
{
    protected $fillable = [
        'title',
        'description',
        'category',
        'file_path',
        'original_filename',
        'file_size',
        'file_type',
        'download_count',
        'is_public',
        'meeting_id',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'is_active' => 'boolean',
        'file_size' => 'integer',
        'download_count' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * 关联会议
     */
    public function meeting(): BelongsTo
    {
        return $this->belongsTo(Meeting::class);
    }

    /**
     * 下载记录
     */
    public function downloads(): HasMany
    {
        return $this->hasMany(MaterialDownload::class, 'material_id');
    }

    /**
     * 获取文件URL
     */
    public function getFileUrlAttribute(): string
    {
        // 如果file_path已经是完整URL，直接返回
        if (filter_var($this->file_path, FILTER_VALIDATE_URL)) {
            return $this->file_path;
        }

        // 否则通过Storage系统生成URL
        return Storage::disk(config('filesystems.default'))->url($this->file_path);
    }

    /**
     * 获取格式化文件大小
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * 检查用户是否可以下载
     */
    public function canDownload(User $user): bool
    {
        // 如果是公开资料，所有人都可以下载
        if ($this->is_public) {
            return true;
        }

        // 检查用户是否为付费用户
        return $user->isPaidUser();
    }

    /**
     * 记录下载
     */
    public function recordDownload(User $user, string $ipAddress = null, string $userAgent = null): void
    {
        // 增加下载次数
        $this->increment('download_count');

        // 记录下载历史
        $this->downloads()->create([
            'user_id' => $user->id,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
        ]);
    }

    /**
     * 作用域：仅活跃资料
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按分类筛选
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * 作用域：按会议筛选
     */
    public function scopeByMeeting($query, int $meetingId)
    {
        return $query->where('meeting_id', $meetingId);
    }

    /**
     * 作用域：公开资料
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * 作用域：付费资料
     */
    public function scopePaid($query)
    {
        return $query->where('is_public', false);
    }
}
