# 前端部署指南

本文档详细介绍了Vue.js前端项目的各种部署方案。

## 环境要求

- Node.js 16+
- npm 或 yarn
- Docker (可选)
- Nginx (可选)

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 环境配置

根据部署环境修改相应的环境变量文件：

- 开发环境：`.env.development`
- 生产环境：`.env.production`

**重要配置项：**
```env
# API基础地址 - 必须修改为实际的后端API地址
VITE_API_BASE_URL=https://your-api-domain.com/api

# 应用基础路径 - 如果部署在子目录需要修改
VITE_BASE_URL=/
```

### 3. 构建项目

```bash
# 生产环境构建
npm run build:prod

# 或者使用默认构建
npm run build
```

构建完成后，静态文件将生成在 `dist/` 目录中。

## 部署方案

### 方案一：静态文件部署 (推荐)

#### 1.1 Nginx部署

**步骤：**

1. 构建项目：
```bash
npm run build:prod
```

2. 将 `dist/` 目录内容上传到服务器：
```bash
scp -r dist/* user@server:/var/www/html/
```

3. 配置Nginx：
```bash
# 复制nginx配置文件
sudo cp deploy/nginx.conf /etc/nginx/sites-available/frontend
sudo ln -s /etc/nginx/sites-available/frontend /etc/nginx/sites-enabled/

# 修改配置文件中的域名和路径
sudo nano /etc/nginx/sites-available/frontend

# 测试配置并重启
sudo nginx -t
sudo systemctl restart nginx
```

**配置要点：**
- 修改 `server_name` 为你的域名
- 修改 `root` 为实际的文件路径
- 修改 `proxy_pass` 为后端API地址

#### 1.2 Apache部署

1. 构建项目并上传文件到Apache根目录

2. 创建 `.htaccess` 文件：
```apache
RewriteEngine On
RewriteBase /

# Handle Vue Router history mode
RewriteRule ^index\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
```

### 方案二：Docker部署

#### 2.1 单容器部署

```bash
# 构建镜像
npm run deploy:docker

# 运行容器
npm run deploy:docker-run

# 或者手动运行
docker run -d -p 80:80 --name frontend-app frontend-app
```

#### 2.2 Docker Compose部署

```bash
# 启动服务
npm run deploy:docker-compose

# 或者手动启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 方案三：云平台部署

#### 3.1 Vercel部署

1. 安装Vercel CLI：
```bash
npm i -g vercel
```

2. 登录并部署：
```bash
vercel login
vercel --prod
```

3. 配置 `vercel.json`：
```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "handle": "filesystem"
    },
    {
      "src": "/.*",
      "dest": "/index.html"
    }
  ]
}
```

#### 3.2 Netlify部署

1. 在项目根目录创建 `netlify.toml`：
```toml
[build]
  publish = "dist"
  command = "npm run build:prod"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

2. 连接GitHub仓库并自动部署

### 方案四：服务器直接部署

```bash
# 在服务器上克隆代码
git clone <repository-url>
cd frontend

# 安装依赖
npm install

# 构建项目
npm run build:prod

# 使用PM2管理进程 (可选)
npm install -g pm2
pm2 serve dist 80 --name "frontend" --spa
```

## 环境变量配置

### 开发环境 (.env.development)
```env
VITE_API_BASE_URL=http://localhost:8000/api
VITE_DEBUG=true
```

### 生产环境 (.env.production)
```env
VITE_API_BASE_URL=https://api.yourdomain.com/api
VITE_DEBUG=false
```

## 常见问题

### 1. 路由404问题

**问题：** 刷新页面或直接访问路由时出现404错误

**解决：** 确保服务器配置了Vue Router的history模式支持，将所有请求重定向到index.html

### 2. API跨域问题

**问题：** 前端无法访问后端API

**解决方案：**
- 后端配置CORS
- 使用Nginx代理
- 确保API地址配置正确

### 3. 静态资源路径问题

**问题：** 部署后静态资源加载失败

**解决：** 检查 `VITE_BASE_URL` 配置是否正确

### 4. 构建失败

**问题：** npm run build 失败

**解决：**
```bash
# 清理缓存
npm run clean
npm install

# 重新构建
npm run build:prod
```

## 性能优化

### 1. 启用Gzip压缩
在Nginx或Apache中启用gzip压缩

### 2. 设置缓存策略
为静态资源设置长期缓存

### 3. CDN加速
将静态资源部署到CDN

### 4. 代码分割
已在vite.config.js中配置了代码分割

## 监控和日志

### 1. Nginx访问日志
```bash
tail -f /var/log/nginx/frontend_access.log
```

### 2. Docker日志
```bash
docker logs -f frontend-app
```

### 3. 性能监控
建议集成前端监控工具如Sentry等

## 安全配置

1. 设置安全头（已在nginx.conf中配置）
2. 启用HTTPS
3. 定期更新依赖包
4. 配置防火墙规则

## 备份和回滚

### 备份策略
```bash
# 备份当前版本
cp -r /var/www/html /var/www/html.backup.$(date +%Y%m%d)
```

### 回滚操作
```bash
# 回滚到备份版本
rm -rf /var/www/html
mv /var/www/html.backup.20240731 /var/www/html
```

## 自动化部署

可以使用GitHub Actions、GitLab CI/CD等工具实现自动化部署。

示例GitHub Actions配置：
```yaml
name: Deploy Frontend

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
    - name: Install dependencies
      run: npm ci
    - name: Build
      run: npm run build:prod
    - name: Deploy
      run: |
        # 部署脚本
```

## 联系支持

如有部署问题，请联系技术支持团队。
