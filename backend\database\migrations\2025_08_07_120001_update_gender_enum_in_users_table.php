<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 先删除现有的gender列
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('gender');
        });

        // 重新添加gender列，只包含male和female
        Schema::table('users', function (Blueprint $table) {
            $table->enum('gender', ['male', 'female'])->nullable()->after('real_name')->comment('性别');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 回滚时恢复包含other选项的enum
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('gender');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->enum('gender', ['male', 'female', 'other'])->nullable()->after('real_name')->comment('性别');
        });
    }
};
