import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/Register.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/wechat-guide',
    name: 'WechatLoginGuide',
    component: () => import('@/views/WechatLoginGuide.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: () => import('@/layout/Layout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue')
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/views/Users.vue')
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/Profile.vue')
      },
      // {
      //   path: 'info-center',
      //   name: 'InfoCenter',
      //   component: () => import('@/views/InfoCenter.vue')
      // },
      {
        path: 'contact',
        name: 'Contact',
        component: () => import('@/views/Contact.vue')
      },
      {
        path: 'payment',
        name: 'Payment',
        component: () => import('@/views/Payment.vue')
      },
      // {
      //   path: 'legal',
      //   name: 'Legal',
      //   component: () => import('@/views/Legal.vue')
      // },
      {
        path: 'meeting-registration',
        name: 'MeetingRegistration',
        component: () => import('@/views/MeetingRegistration.vue')
      },
      {
        path: 'submissions',
        name: 'SubmissionList',
        component: () => import('@/views/SubmissionList.vue')
      },
      {
        path: 'submission/create',
        name: 'SubmissionCreate',
        component: () => import('@/views/SubmissionForm.vue')
      },
      {
        path: 'submission/:id',
        name: 'SubmissionDetail',
        component: () => import('@/views/SubmissionDetail.vue')
      },
      {
        path: 'submission/:id/edit',
        name: 'SubmissionEdit',
        component: () => import('@/views/SubmissionForm.vue')
      },
      // 会议发言投稿页面 - 暂时隐藏
      // {
      //   path: 'speech-submissions',
      //   name: 'SpeechSubmissionList',
      //   component: () => import('@/views/SpeechSubmissionList.vue')
      // },
      // {
      //   path: 'speech-submission/create',
      //   name: 'SpeechSubmissionCreate',
      //   component: () => import('@/views/SpeechSubmissionForm.vue')
      // },
      // {
      //   path: 'speech-submission/:id',
      //   name: 'SpeechSubmissionDetail',
      //   component: () => import('@/views/SpeechSubmissionDetail.vue')
      // },
      // {
      //   path: 'speech-submission/:id/edit',
      //   name: 'SpeechSubmissionEdit',
      //   component: () => import('@/views/SpeechSubmissionForm.vue')
      // },
      // {
      //   path: 'registration-payment',
      //   name: 'RegistrationPayment',
      //   component: () => import('@/views/RegistrationPayment.vue')
      // },
      {
        path: 'payment-process',
        name: 'PaymentProcess',
        component: () => import('@/views/PaymentProcess.vue')
      },
      {
        path: 'invoice-download',
        name: 'InvoiceDownload',
        component: () => import('@/views/InvoiceDownload.vue')
      },
      {
        path: 'material-download',
        name: 'MaterialDownload',
        component: () => import('@/views/MaterialDownload.vue')
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_URL || '/'),
  routes
})

router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login')
  } else if ((to.name === 'Login' || to.name === 'Register') && userStore.isLoggedIn) {
    next('/')
  } else {
    next()
  }
})

export default router