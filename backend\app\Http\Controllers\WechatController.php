<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use App\Models\User;
use EasyWeChat\OfficialAccount\Application;
use EasyWeChat\Kernel\Exceptions\Exception;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

class WechatController extends Controller
{
    /**
     * 获取微信扫码登录二维码
     */
    public function getQrCode()
    {
        try {
            // 生成唯一的场景值
            $sceneStr = 'login_' . time() . '_' . Str::random(8);

            // 调用微信API创建临时二维码
            $app = new Application(config('wechat.official_account.default'));

            $result = $app->getClient()->postJson('cgi-bin/qrcode/create', [
                'expire_seconds' => 600, // 10分钟有效期
                'action_name' => 'QR_STR_SCENE',
                'action_info' => [
                    'scene' => [
                        'scene_str' => $sceneStr
                    ]
                ]
            ]);

            // 检查微信API返回结果
            if (isset($result['errcode']) && $result['errcode'] !== 0) {
                $errorMsg = $this->getWechatErrorMessage($result['errcode'], $result['errmsg'] ?? '');
                \Log::error('微信API返回错误', $result);

                return response()->json([
                    'success' => false,
                    'message' => $errorMsg,
                    'error_code' => $result['errcode'],
                    'error_detail' => $result['errmsg'] ?? '',
                    'suggestions' => $this->getErrorSuggestions($result['errcode'])
                ], 400);
            }

            if (!isset($result['ticket'])) {
                \Log::error('微信API返回格式异常', $result);
                return response()->json([
                    'success' => false,
                    'message' => '微信API返回数据格式异常',
                    'error_detail' => '未获取到ticket'
                ], 500);
            }

            // 获取微信二维码图片
            $qrcodeImageUrl = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" . urlencode($result['ticket']);
            $qrcodeBase64 = $this->generateQrCodeBase64($qrcodeImageUrl, 'real');

            // 将场景值存储到缓存中，用于后续验证
            Cache::put("wechat_login_scene:{$sceneStr}", [
                'status' => 'waiting',
                'created_at' => now(),
                'ticket' => $result['ticket'],
                'type' => 'real'
            ], 600);

            \Log::info('微信二维码生成成功', [
                'scene_str' => $sceneStr,
                'ticket' => $result['ticket']
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'qrcode_base64' => $qrcodeBase64,
                    'scene_str' => $sceneStr,
                    'expires_in' => 600,
                    'ticket' => $result['ticket'],
                    'type' => 'real',
                    'note' => '请使用微信扫码关注公众号登录'
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('微信二维码生成失败: ' . $e->getMessage(), [
                'exception' => $e->getTraceAsString()
            ]);

            // 解析具体错误信息
            $errorMessage = $this->parseWechatException($e);

            return response()->json([
                'success' => false,
                'message' => $errorMessage['message'],
                'error_type' => $errorMessage['type'],
                'suggestions' => $errorMessage['suggestions']
            ], 500);
        }
    }

    /**
     * 生成二维码Base64图片
     */
    private function generateQrCodeBase64($data, $type = 'real')
    {
        try {
            if ($type === 'real' && filter_var($data, FILTER_VALIDATE_URL)) {
                // 如果是真实的微信二维码URL，先尝试下载图片
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 10,
                        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    ]
                ]);

                $imageData = @file_get_contents($data, false, $context);
                if ($imageData !== false) {
                    return 'data:image/jpeg;base64,' . base64_encode($imageData);
                }
            }

            // 使用本地二维码库生成
            return $this->generateLocalQrCode($data, $type);

        } catch (\Exception $e) {
            \Log::error('生成二维码Base64失败: ' . $e->getMessage());
            return $this->generateLocalQrCode($data, $type);
        }
    }

    /**
     * 使用本地库生成二维码
     */
    private function generateLocalQrCode($data, $type = 'real')
    {
        try {
            // 根据类型决定二维码内容
            if ($type === 'real') {
                $qrData = $data; // 真实的微信URL
            } else {
                // 演示模式，生成包含场景值的提示信息
                $qrData = "微信公众号登录\n场景值: " . substr($data, -20) . "\n请配置IP白名单后使用";
            }

            // 创建二维码
            $qrCode = new QrCode($qrData);
            $qrCode->setSize(200);
            $qrCode->setMargin(10);

            // 生成PNG图片
            $writer = new PngWriter();
            $result = $writer->write($qrCode);

            // 转换为base64
            $imageData = $result->getString();
            return 'data:image/png;base64,' . base64_encode($imageData);

        } catch (\Exception $e) {
            \Log::error('本地二维码生成失败: ' . $e->getMessage());
            return $this->generateSimpleSvgQrCode($data);
        }
    }

    /**
     * 生成简单的SVG二维码（备用方案）
     */
    private function generateSimpleSvgQrCode($data)
    {
        $svg = '<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="200" fill="white"/>
            <rect x="20" y="20" width="160" height="160" fill="none" stroke="black" stroke-width="2"/>
            <text x="100" y="80" text-anchor="middle" font-family="Arial" font-size="12" fill="black">微信扫码登录</text>
            <text x="100" y="100" text-anchor="middle" font-family="Arial" font-size="10" fill="gray">二维码生成中...</text>
            <text x="100" y="120" text-anchor="middle" font-family="Arial" font-size="8" fill="gray">请稍后刷新</text>
            <!-- 简单的二维码图案 -->
            <rect x="40" y="40" width="20" height="20" fill="black"/>
            <rect x="80" y="40" width="20" height="20" fill="black"/>
            <rect x="140" y="40" width="20" height="20" fill="black"/>
            <rect x="40" y="80" width="20" height="20" fill="black"/>
            <rect x="140" y="80" width="20" height="20" fill="black"/>
            <rect x="40" y="140" width="20" height="20" fill="black"/>
            <rect x="80" y="140" width="20" height="20" fill="black"/>
            <rect x="140" y="140" width="20" height="20" fill="black"/>
        </svg>';

        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }

    /**
     * 获取微信错误信息
     */
    private function getWechatErrorMessage($errcode, $errmsg)
    {
        $errorMessages = [
            40001 => 'AppSecret错误或者AppSecret不属于这个公众号',
            40013 => '不合法的AppID',
            40014 => '不合法的access_token',
            40164 => 'IP地址不在白名单中，请添加服务器IP到微信公众号白名单',
            41001 => '缺少access_token参数',
            42001 => 'access_token超时，请重新获取',
            48001 => 'api功能未授权',
            50001 => '用户未授权该api',
            50002 => '用户受限，可能是违规后接口被封禁',
        ];

        return $errorMessages[$errcode] ?? "微信API错误 (错误码: {$errcode}): {$errmsg}";
    }

    /**
     * 获取错误建议
     */
    private function getErrorSuggestions($errcode)
    {
        $suggestions = [
            40164 => [
                '1. 登录微信公众平台 https://mp.weixin.qq.com/',
                '2. 进入 开发 → 基本配置',
                '3. 在IP白名单中添加服务器IP: ' . request()->server('SERVER_ADDR'),
                '4. 保存配置并等待5-10分钟生效'
            ],
            40001 => [
                '1. 检查微信公众号的AppSecret是否正确',
                '2. 确认AppSecret与AppID匹配',
                '3. 重新生成AppSecret并更新配置'
            ],
            40013 => [
                '1. 检查微信公众号的AppID是否正确',
                '2. 确认AppID格式正确（以wx开头）'
            ],
            42001 => [
                '1. access_token已过期，系统会自动重新获取',
                '2. 如果问题持续，请检查系统时间是否正确'
            ]
        ];

        return $suggestions[$errcode] ?? [
            '1. 检查微信公众号配置是否正确',
            '2. 确认网络连接正常',
            '3. 查看微信公众平台开发文档'
        ];
    }

    /**
     * 解析微信异常
     */
    private function parseWechatException(\Exception $e)
    {
        $message = $e->getMessage();

        // 检查是否是IP白名单问题
        if (strpos($message, 'ip') !== false && strpos($message, 'whitelist') !== false) {
            return [
                'type' => 'ip_whitelist_error',
                'message' => 'IP地址不在微信公众号白名单中',
                'suggestions' => [
                    '1. 登录微信公众平台 https://mp.weixin.qq.com/',
                    '2. 进入 开发 → 基本配置',
                    '3. 在IP白名单中添加服务器IP: ' . request()->server('SERVER_ADDR'),
                    '4. 保存配置并等待5-10分钟生效'
                ]
            ];
        }

        // 检查是否是网络连接问题
        if (strpos($message, 'Connection') !== false || strpos($message, 'timeout') !== false) {
            return [
                'type' => 'network_error',
                'message' => '网络连接失败，无法连接到微信服务器',
                'suggestions' => [
                    '1. 检查服务器网络连接',
                    '2. 确认防火墙设置允许访问微信API',
                    '3. 稍后重试'
                ]
            ];
        }

        // 默认错误
        return [
            'type' => 'unknown_error',
            'message' => '微信接口调用失败: ' . $message,
            'suggestions' => [
                '1. 检查微信公众号配置',
                '2. 确认网络连接正常',
                '3. 查看详细错误日志'
            ]
        ];
    }

    /**
     * 检查扫码登录状态
     */
    public function checkLoginStatus(Request $request)
    {
        $sceneStr = $request->input('scene_str');

        if (!$sceneStr) {
            return response()->json([
                'success' => false,
                'message' => '场景值不能为空'
            ], 400);
        }

        $cacheKey = "wechat_login_scene:{$sceneStr}";
        $loginData = Cache::get($cacheKey);

        if (!$loginData) {
            return response()->json([
                'success' => false,
                'message' => '二维码已过期'
            ], 400);
        }

        return response()->json([
            'success' => true,
            'data' => $loginData
        ]);
    }

    /**
     * 模拟微信扫码登录成功（仅用于演示）
     */
    public function mockLogin(Request $request)
    {
        $sceneStr = $request->input('scene_str');

        if (!$sceneStr) {
            return response()->json([
                'success' => false,
                'message' => '场景值不能为空'
            ], 400);
        }

        $cacheKey = "wechat_login_scene:{$sceneStr}";
        $loginData = Cache::get($cacheKey);

        if (!$loginData) {
            return response()->json([
                'success' => false,
                'message' => '二维码已过期'
            ], 400);
        }

        // 模拟创建或获取用户
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '微信演示用户',
                'real_name' => '张三',
                'phone' => '13800138000',
                'member_type' => 'expert',
                'password' => bcrypt('123456'),
                'wechat_openid' => 'demo_openid_' . time()
            ]
        );

        // 生成token
        $token = $user->createToken('wechat_login')->plainTextToken;

        // 更新缓存状态
        Cache::put($cacheKey, [
            'status' => 'success',
            'user' => $user,
            'token' => $token,
            'created_at' => $loginData['created_at']
        ], 600);

        return response()->json([
            'success' => true,
            'message' => '模拟登录成功',
            'data' => [
                'status' => 'success',
                'user' => $user,
                'token' => $token
            ]
        ]);
    }

    /**
     * 微信事件回调处理
     */
    public function callback(Request $request)
    {
        // 如果是GET请求，说明是微信服务器验证URL
        if ($request->isMethod('GET')) {
            return $this->verifyUrl($request);
        }

        // POST请求处理微信消息
        try {
            $app = new Application(config('wechat.official_account.default'));

            // 验证签名
            if (!$this->validateSignature($request)) {
                \Log::error('微信回调签名验证失败');
                return response('Unauthorized', 401);
            }

            // 获取微信消息内容
            $content = $request->getContent();
            \Log::info('微信回调消息: ' . $content);

            // 解析XML消息
            $message = $this->parseXmlMessage($content);

            if ($message && isset($message['MsgType'])) {
                if ($message['MsgType'] === 'event') {
                    if ($message['Event'] === 'SCAN' || $message['Event'] === 'subscribe') {
                        $sceneStr = $message['EventKey'];
                        if ($message['Event'] === 'subscribe') {
                            $sceneStr = str_replace('qrscene_', '', $sceneStr);
                        }

                        $this->handleWechatLogin($message['FromUserName'], $sceneStr);
                    }
                }
            }

            return response('success', 200);

        } catch (\Exception $e) {
            \Log::error('微信回调处理失败：' . $e->getMessage());
            return response('success', 200); // 微信要求返回success
        }
    }

    /**
     * 验证微信服务器URL
     */
    private function verifyUrl(Request $request)
    {
        $signature = $request->get('signature');
        $timestamp = $request->get('timestamp');
        $nonce = $request->get('nonce');
        $echostr = $request->get('echostr');

        if ($this->validateSignature($request)) {
            \Log::info('微信URL验证成功');
            return response($echostr, 200);
        } else {
            \Log::error('微信URL验证失败');
            return response('验证失败', 403);
        }
    }

    /**
     * 验证微信服务器签名
     */
    private function validateSignature(Request $request)
    {
        $signature = $request->get('signature');
        $timestamp = $request->get('timestamp');
        $nonce = $request->get('nonce');
        $token = config('wechat.official_account.default.token');

        if (!$signature || !$timestamp || !$nonce || !$token) {
            return false;
        }

        $tmpArr = [$token, $timestamp, $nonce];
        sort($tmpArr, SORT_STRING);
        $tmpStr = implode($tmpArr);
        $tmpStr = sha1($tmpStr);

        return $tmpStr === $signature;
    }

    /**
     * 解析XML消息
     */
    private function parseXmlMessage($content)
    {
        try {
            $xml = simplexml_load_string($content, 'SimpleXMLElement', LIBXML_NOCDATA);
            if ($xml === false) {
                return null;
            }

            $message = [];
            foreach ($xml as $key => $value) {
                $message[$key] = (string)$value;
            }

            return $message;
        } catch (\Exception $e) {
            \Log::error('解析XML消息失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 处理微信登录
     */
    private function handleWechatLogin($openid, $sceneStr)
    {
        \Log::info('开始处理微信登录', [
            'openid' => $openid,
            'scene_str' => $sceneStr
        ]);

        try {
            $app = new Application(config('wechat.official_account.default'));

            // 首先检查用户是否已存在
            $existingUser = User::where('wechat_openid', $openid)->first();

            if ($existingUser) {
                \Log::info('找到已存在的微信用户', [
                    'user_id' => $existingUser->id,
                    'user_name' => $existingUser->name,
                    'openid' => $openid
                ]);

                // 用户已存在，直接登录
                $token = $existingUser->createToken('wechat_login')->plainTextToken;

                // 更新缓存状态
                $cacheKey = "wechat_login_scene:{$sceneStr}";
                Cache::put($cacheKey, [
                    'status' => 'success',
                    'user' => $existingUser,
                    'token' => $token,
                    'logged_at' => now(),
                    'login_type' => 'existing_user'
                ], 300);

                \Log::info('微信用户登录成功', [
                    'user_id' => $existingUser->id,
                    'scene_str' => $sceneStr
                ]);

                return;
            }

            // 用户不存在，获取用户信息并创建新用户
            \Log::info('用户不存在，开始获取微信用户信息', ['openid' => $openid]);

            $userInfo = null;
            try {
                $userInfo = $app->getClient()->get('cgi-bin/user/info', [
                    'openid' => $openid,
                    'lang' => 'zh_CN'
                ]);

                \Log::info('获取微信用户信息成功', [
                    'openid' => $openid,
                    'nickname' => $userInfo['nickname'] ?? 'N/A',
                    'subscribe' => $userInfo['subscribe'] ?? 'N/A'
                ]);
            } catch (\Exception $e) {
                \Log::warning('获取微信用户信息失败，使用默认信息', [
                    'openid' => $openid,
                    'error' => $e->getMessage()
                ]);
                // 即使获取用户信息失败，也创建用户，使用默认信息
                $userInfo = [];
            }

            // 创建新用户
            $userData = [
                'name' => $userInfo['nickname'] ?? '微信用户_' . substr($openid, -6),
                'email' => $openid . '@wechat.local', // 临时邮箱
                'password' => bcrypt(Str::random(32)), // 随机密码
                'wechat_openid' => $openid,
                'wechat_unionid' => $userInfo['unionid'] ?? null,
                'avatar' => $userInfo['headimgurl'] ?? null,
                'member_type' => 'expert', // 默认为专家
                'real_name' => $userInfo['nickname'] ?? '微信用户',
                'phone' => null, // 微信登录暂时不获取手机号
            ];

            $user = User::create($userData);

            \Log::info('微信新用户创建成功', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'openid' => $openid,
                'scene_str' => $sceneStr
            ]);

            // 生成token
            $token = $user->createToken('wechat_login')->plainTextToken;

            // 更新缓存状态
            $cacheKey = "wechat_login_scene:{$sceneStr}";
            Cache::put($cacheKey, [
                'status' => 'success',
                'user' => $user,
                'token' => $token,
                'logged_at' => now(),
                'login_type' => 'new_user'
            ], 300); // 5分钟有效期

            \Log::info('微信新用户登录成功', [
                'user_id' => $user->id,
                'scene_str' => $sceneStr
            ]);

        } catch (\Exception $e) {
            \Log::error('处理微信登录失败', [
                'openid' => $openid,
                'scene_str' => $sceneStr,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 更新缓存状态为失败
            $cacheKey = "wechat_login_scene:{$sceneStr}";
            Cache::put($cacheKey, [
                'status' => 'failed',
                'message' => '登录处理失败: ' . $e->getMessage(),
                'error_detail' => $e->getMessage(),
                'failed_at' => now()
            ], 300);
        }
    }

    /**
     * 测试微信配置
     */
    public function testConfig()
    {
        $config = config('wechat.official_account.default');

        return response()->json([
            'success' => true,
            'message' => '微信配置信息',
            'data' => [
                'app_id' => $config['app_id'],
                'token' => $config['token'],
                'aes_key' => $config['aes_key'],
                'callback_url' => url('/api/wechat/callback'),
                'domain' => request()->getHost(),
                'full_url' => request()->fullUrl(),
                'server_ip' => request()->server('SERVER_ADDR'),
                'user_agent' => request()->userAgent(),
                'headers' => request()->headers->all(),
                'note' => '请在微信公众号后台配置URL: ' . url('/api/wechat/callback') . ' 和Token: ' . $config['token'],
                'config_steps' => [
                    '1. 登录微信公众平台 https://mp.weixin.qq.com/',
                    '2. 进入 开发 → 基本配置',
                    '3. 点击 修改配置',
                    '4. 填写服务器地址: ' . url('/api/wechat/callback'),
                    '5. 填写Token: ' . $config['token'],
                    '6. 填写EncodingAESKey: ' . $config['aes_key'],
                    '7. 选择消息加解密方式: 明文模式',
                    '8. 点击提交验证'
                ]
            ]
        ]);
    }

    /**
     * 测试错误处理（仅用于演示）
     */
    public function testError(Request $request)
    {
        $errorType = $request->input('type', 'ip_whitelist');

        switch ($errorType) {
            case 'ip_whitelist':
                return response()->json([
                    'success' => false,
                    'message' => 'IP地址不在微信公众号白名单中',
                    'error_code' => 40164,
                    'error_type' => 'ip_whitelist_error',
                    'error_detail' => 'invalid ip **************, not in whitelist',
                    'suggestions' => [
                        '1. 登录微信公众平台 https://mp.weixin.qq.com/',
                        '2. 进入 开发 → 基本配置',
                        '3. 在IP白名单中添加服务器IP: **************',
                        '4. 保存配置并等待5-10分钟生效'
                    ]
                ], 400);

            case 'auth_error':
                return response()->json([
                    'success' => false,
                    'message' => 'AppSecret错误或者AppSecret不属于这个公众号',
                    'error_code' => 40001,
                    'error_type' => 'auth_error',
                    'suggestions' => [
                        '1. 检查微信公众号的AppSecret是否正确',
                        '2. 确认AppSecret与AppID匹配',
                        '3. 重新生成AppSecret并更新配置'
                    ]
                ], 400);

            default:
                return response()->json([
                    'success' => false,
                    'message' => '微信接口调用失败: 网络连接超时',
                    'error_type' => 'network_error',
                    'suggestions' => [
                        '1. 检查服务器网络连接',
                        '2. 确认防火墙设置允许访问微信API',
                        '3. 稍后重试'
                    ]
                ], 500);
        }
    }

    /**
     * 测试微信登录流程
     */
    public function testLogin(Request $request)
    {
        $sceneStr = $request->input('scene_str');
        $openid = $request->input('openid', 'test_openid_' . time());

        if (!$sceneStr) {
            return response()->json([
                'success' => false,
                'message' => '场景值不能为空'
            ], 400);
        }

        \Log::info('测试微信登录', [
            'scene_str' => $sceneStr,
            'openid' => $openid
        ]);

        // 调用实际的登录处理方法
        $this->handleWechatLogin($openid, $sceneStr);

        return response()->json([
            'success' => true,
            'message' => '测试登录处理完成',
            'data' => [
                'scene_str' => $sceneStr,
                'openid' => $openid
            ]
        ]);
    }

    /**
     * 调试微信回调
     */
    public function debugCallback(Request $request)
    {
        $data = [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'headers' => $request->headers->all(),
            'query_params' => $request->query(),
            'body' => $request->getContent(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()->toDateTimeString()
        ];

        \Log::info('微信回调调试信息', $data);

        return response()->json([
            'success' => true,
            'message' => '调试信息已记录',
            'data' => $data
        ]);
    }

    /**
     * 显示微信配置页面
     */
    public function showConfig()
    {
        $config = config('wechat.official_account.default');
        $callbackUrl = url('/api/wechat/callback');

        $html = "
        <!DOCTYPE html>
        <html>
        <head>
            <title>微信公众号配置</title>
            <meta charset='utf-8'>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .config-box { background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0; }
                .step { margin: 10px 0; padding: 10px; background: white; border-radius: 4px; }
                .highlight { background: #ffffcc; padding: 2px 4px; }
                .success { color: #28a745; }
                .error { color: #dc3545; }
            </style>
        </head>
        <body>
            <h1>微信公众号配置指南</h1>

            <div class='config-box'>
                <h2>配置信息</h2>
                <p><strong>服务器地址(URL):</strong> <span class='highlight'>{$callbackUrl}</span></p>
                <p><strong>Token:</strong> <span class='highlight'>{$config['token']}</span></p>
                <p><strong>EncodingAESKey:</strong> <span class='highlight'>{$config['aes_key']}</span></p>
                <p><strong>AppID:</strong> <span class='highlight'>{$config['app_id']}</span></p>
            </div>

            <div class='config-box'>
                <h2>配置步骤</h2>
                <div class='step'>1. 登录微信公众平台: <a href='https://mp.weixin.qq.com/' target='_blank'>https://mp.weixin.qq.com/</a></div>
                <div class='step'>2. 进入 <strong>开发</strong> → <strong>基本配置</strong></div>
                <div class='step'>3. 点击 <strong>修改配置</strong></div>
                <div class='step'>4. 填写服务器地址: <span class='highlight'>{$callbackUrl}</span></div>
                <div class='step'>5. 填写Token: <span class='highlight'>{$config['token']}</span></div>
                <div class='step'>6. 填写EncodingAESKey: <span class='highlight'>{$config['aes_key']}</span></div>
                <div class='step'>7. 选择消息加解密方式: <strong>明文模式</strong></div>
                <div class='step'>8. 点击 <strong>提交</strong> 验证</div>
            </div>

            <div class='config-box'>
                <h2>测试链接</h2>
                <p><a href='{$callbackUrl}' target='_blank'>测试回调URL</a></p>
                <p><a href='/api/wechat/test-config' target='_blank'>查看配置JSON</a></p>
                <p><a href='/api/wechat/qrcode' target='_blank'>测试二维码生成</a></p>
            </div>

            <div class='config-box'>
                <h2>注意事项</h2>
                <ul>
                    <li>确保域名 <strong>ai.whlxyc.cn</strong> 可以正常访问</li>
                    <li>微信服务器会向您的URL发送GET请求进行验证</li>
                    <li>验证成功后，微信会向您的URL发送POST请求推送消息</li>
                    <li>如果验证失败，请检查Token是否正确</li>
                </ul>
            </div>
        </body>
        </html>";

        return response($html)->header('Content-Type', 'text/html; charset=utf-8');
    }
}
