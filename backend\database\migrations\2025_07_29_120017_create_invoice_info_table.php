<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_info', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade')->comment('用户ID');
            $table->string('invoice_type')->comment('发票类型：personal-个人，company-企业');
            $table->string('invoice_title')->comment('发票抬头');
            $table->string('tax_number')->nullable()->comment('纳税人识别号');
            $table->string('company_phone')->nullable()->comment('公司电话');
            $table->string('bank_name')->nullable()->comment('开户银行');
            $table->string('bank_account')->nullable()->comment('银行账号');
            $table->string('contact_phone')->comment('联系电话');
            $table->string('contact_email')->nullable()->comment('联系邮箱');
            $table->boolean('is_default')->default(false)->comment('是否为默认开票信息');
            $table->timestamps();

            // 添加索引
            $table->index('user_id');
            $table->index(['user_id', 'is_default']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_info');
    }
};
