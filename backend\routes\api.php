<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\WechatController;
use App\Http\Controllers\MeetingController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\InvoiceInfoController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\SubmissionController;
use App\Http\Controllers\SpeechSubmissionController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\MeetingMaterialController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// 测试路由
Route::get('/test', function () {
    return response()->json(['message' => 'API is working!']);
});

Route::get('/test-meetings', function () {
    $meetings = \App\Models\Meeting::all();
    return response()->json(['success' => true, 'data' => $meetings]);
});

Route::get('/meetings-simple', function () {
    return response()->json([
        'success' => true,
        'data' => \App\Models\Meeting::where('status', 'published')->get()
    ]);
});

// 测试微信支付配置
Route::get('/test-wechat-pay', function () {
    try {
        $config = config('wechat.payment.default');
        return response()->json([
            'success' => true,
            'message' => '微信支付配置正常',
            'config' => [
                'app_id' => $config['app_id'],
                'mch_id' => $config['mch_id'],
                'notify_url' => $config['notify_url']
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => '微信支付配置错误: ' . $e->getMessage()
        ], 500);
    }
});

// 测试注册路由
Route::post('/test-register', function (Request $request) {
    return response()->json([
        'success' => true,
        'message' => '注册测试成功',
        'data' => $request->all()
    ]);
});

// 公开路由
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// 短信验证码相关路由
Route::prefix('sms')->group(function () {
    Route::post('/send-code', [\App\Http\Controllers\SmsController::class, 'sendCode']);
    Route::post('/login', [\App\Http\Controllers\SmsController::class, 'login']);
    Route::get('/config-status', [\App\Http\Controllers\SmsController::class, 'getConfigStatus']);
});

// 微信登录相关路由
Route::prefix('wechat')->group(function () {
    Route::get('/qrcode', [WechatController::class, 'getQrCode']);
    Route::get('/login-status', [WechatController::class, 'checkLoginStatus']);
    Route::any('/callback', [WechatController::class, 'callback']);
    Route::post('/mock-login', [WechatController::class, 'mockLogin']); // 演示用
    Route::get('/test-config', [WechatController::class, 'testConfig']); // 测试配置
    Route::get('/config', [WechatController::class, 'showConfig']); // 配置页面
    Route::get('/test-error', [WechatController::class, 'testError']); // 测试错误处理
    Route::any('/debug', [WechatController::class, 'debugCallback']); // 调试用
    Route::post('/test-login', [WechatController::class, 'testLogin']); // 测试登录流程
});

// 需要认证的路由
Route::middleware('auth:sanctum')->group(function () {
    // 用户认证
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/me', [AuthController::class, 'me']);

    // 仪表盘
    Route::get('/dashboard', [DashboardController::class, 'index']);

    // 资料下载
    Route::prefix('materials')->group(function () {
        Route::get('/', [MeetingMaterialController::class, 'index']);
        Route::get('/categories', [MeetingMaterialController::class, 'categories']);
        Route::get('/my-downloads', [MeetingMaterialController::class, 'myDownloads']);
        Route::get('/{id}', [MeetingMaterialController::class, 'show']);
        Route::get('/{id}/download', [MeetingMaterialController::class, 'download']);
    });

    // 用户管理
    Route::prefix('user')->group(function () {
        Route::get('/profile', [UserController::class, 'profile']);
        Route::put('/profile', [UserController::class, 'updateProfile']);
        Route::post('/change-password', [UserController::class, 'changePassword']);
        Route::post('/bind-phone', [UserController::class, 'bindPhone']);
        Route::post('/send-sms', [UserController::class, 'sendSms']);
        Route::post('/bind-wechat', [UserController::class, 'bindWechat']);

        // 短信绑定手机号
        Route::post('/bind-phone-sms', [\App\Http\Controllers\SmsController::class, 'bindPhone']);
    });
    
    // 会议管理
    Route::prefix('meetings')->group(function () {
        Route::get('/', function () {
            $meetings = \App\Models\Meeting::where('status', 'published')->get();
            return response()->json(['success' => true, 'data' => $meetings]);
        });
        // 注意：具体路由要放在参数路由之前
        Route::get('/my-registrations', [MeetingController::class, 'myRegistrations']);
        Route::get('/{id}', [MeetingController::class, 'show']);
        Route::post('/{id}/register', [MeetingController::class, 'register']);
    });
    
    // 支付相关
    Route::prefix('payments')->group(function () {
        Route::post('/create', [PaymentController::class, 'create']);
        Route::get('/status/{id}', [PaymentController::class, 'status']);
        Route::get('/my-payments', [PaymentController::class, 'myPayments']);
        Route::post('/{id}/upload-screenshot', [PaymentController::class, 'uploadTransferScreenshot']);
        Route::post('/{id}/review', [PaymentController::class, 'reviewOfflinePayment']);
    });

    // 开票信息管理
    Route::prefix('invoice-info')->group(function () {
        Route::get('/', [InvoiceInfoController::class, 'index']);
        Route::post('/', [InvoiceInfoController::class, 'store']);
        Route::put('/{id}', [InvoiceInfoController::class, 'update']);
        Route::delete('/{id}', [InvoiceInfoController::class, 'destroy']);
        Route::post('/{id}/set-default', [InvoiceInfoController::class, 'setDefault']);
    });

    // 发票管理
    Route::prefix('invoices')->group(function () {
        Route::get('/', [InvoiceController::class, 'index']);
        Route::get('/{id}/download', [InvoiceController::class, 'download']);
    });

    // 投稿管理
    Route::prefix('submissions')->group(function () {
        Route::get('/', [SubmissionController::class, 'index']);
        Route::post('/', [SubmissionController::class, 'store']);
        Route::get('/{id}', [SubmissionController::class, 'show']);
        Route::put('/{id}', [SubmissionController::class, 'update']);
        Route::delete('/{id}', [SubmissionController::class, 'destroy']);
        Route::post('/{id}/submit', [SubmissionController::class, 'submit']);
        Route::get('/{id}/download', [SubmissionController::class, 'download']);
    });

    // 会议发言投稿管理
    Route::prefix('speech-submissions')->group(function () {
        Route::get('/', [SpeechSubmissionController::class, 'index']);
        Route::post('/', [SpeechSubmissionController::class, 'store']);
        Route::get('/{id}', [SpeechSubmissionController::class, 'show']);
        Route::put('/{id}', [SpeechSubmissionController::class, 'update']);
        Route::delete('/{id}', [SpeechSubmissionController::class, 'destroy']);
        Route::post('/{id}/submit', [SpeechSubmissionController::class, 'submit']);
        Route::get('/{id}/download', [SpeechSubmissionController::class, 'download']);
    });
});

// 支付回调路由（不需要认证）
Route::prefix('payments')->group(function () {
    Route::post('/wechat/notify', [PaymentController::class, 'wechatNotify']);
    Route::post('/alipay/notify', [PaymentController::class, 'alipayNotify']);
    Route::get('/alipay/return', [PaymentController::class, 'alipayReturn']);
});

// 测试路由
Route::get('/test/oss', function () {
    try {
        $ossService = new \App\Services\OssService();
        return response()->json([
            'success' => true,
            'message' => 'OSS服务初始化成功',
            'config' => [
                'bucket' => config('filesystems.disks.oss.bucket'),
                'endpoint' => config('filesystems.disks.oss.endpoint'),
                'cdn_url' => config('filesystems.disks.oss.cdn_url')
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'OSS服务初始化失败: ' . $e->getMessage()
        ], 500);
    }
});

// 支付测试路由
Route::prefix('test')->group(function () {
    Route::get('/payment-guide', [\App\Http\Controllers\PaymentTestController::class, 'getTestGuide']);
    Route::get('/create-test-data', [\App\Http\Controllers\PaymentTestController::class, 'createTestData']);
    Route::post('/mock-payment-success', [\App\Http\Controllers\PaymentTestController::class, 'mockPaymentSuccess']);
    Route::delete('/clean-test-data', [\App\Http\Controllers\PaymentTestController::class, 'cleanTestData']);
    Route::get('/wechat-pay-config', function () {
        try {
            $config = config('wechat.payment.default');

            // 检查证书文件路径
            $privateKeyPath = $config['private_key_path'];
            $certificatePath = $config['certificate_path'];

            // 处理路径
            if (!str_starts_with($privateKeyPath, '/') && !str_contains($privateKeyPath, ':\\')) {
                $fullPrivateKeyPath = storage_path($privateKeyPath);
            } else {
                $fullPrivateKeyPath = $privateKeyPath;
            }

            if (!str_starts_with($certificatePath, '/') && !str_contains($certificatePath, ':\\')) {
                $fullCertificatePath = storage_path($certificatePath);
            } else {
                $fullCertificatePath = $certificatePath;
            }

            return response()->json([
                'success' => true,
                'message' => '微信支付配置检查',
                'data' => [
                    'app_id' => $config['app_id'] ? '已配置' : '未配置',
                    'mch_id' => $config['mch_id'] ? '已配置' : '未配置',
                    'serial_no' => $config['serial_no'] ? '已配置' : '未配置',
                    'api_key' => $config['api_key'] ? '已配置' : '未配置',
                    'notify_url' => $config['notify_url'],
                    'private_key_path' => $privateKeyPath,
                    'certificate_path' => $certificatePath,
                    'full_private_key_path' => $fullPrivateKeyPath,
                    'full_certificate_path' => $fullCertificatePath,
                    'private_key_exists' => file_exists($fullPrivateKeyPath),
                    'certificate_exists' => file_exists($fullCertificatePath),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '配置检查失败: ' . $e->getMessage()
            ], 500);
        }
    });

    Route::get('/user-paid-status', function () {
        $user = \App\Models\User::first();
        return response()->json([
            'success' => true,
            'data' => [
                'user_id' => $user->id,
                'is_paid_user' => $user->is_paid_user,
                'paid_at' => $user->paid_at,
                'total_paid_amount' => $user->total_paid_amount,
                'paid_meetings_count' => $user->paid_meetings_count,
                'user_level' => $user->getUserLevel(),
                'user_level_label' => $user->getUserLevelLabel(),
            ]
        ]);
    });

    Route::post('/upgrade-user', function () {
        $user = \App\Models\User::first();
        $user->upgradeToPaidUser(2000);
        return response()->json([
            'success' => true,
            'message' => '用户已升级为付费用户',
            'data' => [
                'user_id' => $user->id,
                'is_paid_user' => $user->fresh()->is_paid_user,
                'total_paid_amount' => $user->fresh()->total_paid_amount,
                'paid_meetings_count' => $user->fresh()->paid_meetings_count,
            ]
        ]);
    });

    Route::post('/simulate-payment-success/{paymentId}', function ($paymentId) {
        $payment = \App\Models\Payment::find($paymentId);

        if (!$payment) {
            return response()->json([
                'success' => false,
                'message' => '支付记录不存在'
            ], 404);
        }

        if ($payment->status === 'success') {
            return response()->json([
                'success' => false,
                'message' => '支付已经成功'
            ], 400);
        }

        // 模拟支付成功
        $paymentController = new \App\Http\Controllers\PaymentController();
        $reflection = new ReflectionClass($paymentController);
        $method = $reflection->getMethod('updatePaymentSuccess');
        $method->setAccessible(true);

        $mockPaymentData = [
            'transaction_id' => $payment->transaction_id,
            'trade_state' => 'SUCCESS',
            'trade_state_desc' => '支付成功',
            'success_time' => now()->toISOString()
        ];

        $method->invoke($paymentController, $payment, $mockPaymentData);

        return response()->json([
            'success' => true,
            'message' => '支付成功模拟完成',
            'data' => [
                'payment_id' => $payment->id,
                'status' => $payment->fresh()->status,
                'registration_status' => $payment->meetingRegistration->fresh()->payment_status,
                'user_upgraded' => $payment->user->fresh()->is_paid_user
            ]
        ]);
    });

    Route::get('/create-test-token', function () {
        $user = \App\Models\User::first();
        $token = $user->createToken('test-token')->plainTextToken;
        return response()->json([
            'success' => true,
            'data' => [
                'user_id' => $user->id,
                'token' => $token
            ]
        ]);
    });

    Route::get('/test-wechat-service', function () {
        try {
            // 测试WechatPayService初始化
            $service = new \App\Services\WechatPayService();

            // 测试创建模拟订单
            $orderData = [
                'out_trade_no' => 'TEST_' . time(),
                'description' => '测试订单',
                'total_fee' => 100
            ];

            $result = $service->createNativeOrder($orderData);

            return response()->json([
                'success' => true,
                'message' => 'WechatPayService测试成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'WechatPayService测试失败: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    });

    Route::get('/alipay-config', function () {
        try {
            $alipayService = app(\App\Services\AlipayService::class);
            $configCheck = $alipayService->checkConfig();

            return response()->json([
                'success' => true,
                'message' => '支付宝配置检查',
                'data' => $configCheck,
                'environment' => env('APP_ENV'),
                'alipay_sandbox' => env('ALIPAY_SANDBOX', true)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取支付宝配置失败: ' . $e->getMessage()
            ], 500);
        }
    });
});
