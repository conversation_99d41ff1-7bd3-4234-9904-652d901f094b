import request from '@/utils/request'

/**
 * 用户登录
 * @param {Object} credentials - 登录凭据
 * @param {string} credentials.email - 邮箱
 * @param {string} credentials.password - 密码
 */
export const login = (credentials) => {
  return request.post('/login', credentials)
}

/**
 * 用户注册
 * @param {Object} userData - 用户数据
 * @param {string} userData.name - 用户名
 * @param {string} userData.email - 邮箱
 * @param {string} userData.password - 密码
 * @param {string} userData.password_confirmation - 确认密码
 * @param {string} userData.real_name - 真实姓名
 * @param {string} userData.phone - 手机号
 * @param {string} userData.member_type - 会员类型 (expert/student)
 */
export const register = (userData) => {
  return request.post('/register', userData)
}

/**
 * 用户登出
 */
export const logout = () => {
  return request.post('/logout')
}

/**
 * 获取当前用户信息
 */
export const getCurrentUser = () => {
  return request.get('/me')
}

/**
 * 获取微信扫码登录二维码
 */
export const getWechatQrCode = () => {
  return request.get('/wechat/qrcode')
}

/**
 * 检查微信扫码登录状态
 * @param {string} sceneStr - 场景值
 */
export const checkWechatLoginStatus = (sceneStr) => {
  return request.get('/wechat/login-status', {
    params: { scene_str: sceneStr }
  })
}
