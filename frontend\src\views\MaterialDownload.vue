<template>
  <div class="material-download">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>资料下载</h1>
      <p>会议相关资料和学术文档下载中心</p>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-select
            v-model="filters.category"
            placeholder="选择资料分类"
            clearable
            @change="handleFilterChange"
          >
            <el-option
              v-for="(label, value) in categories"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-select
            v-model="filters.meeting_id"
            placeholder="选择关联会议"
            clearable
            @change="handleFilterChange"
          >
            <el-option
              v-for="meeting in meetings"
              :key="meeting.id"
              :label="meeting.title"
              :value="meeting.id"
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="fetchMaterials">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 资料列表 -->
    <div class="materials-grid">
      <el-row :gutter="20">
        <el-col
          v-for="material in materials"
          :key="material.id"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
        >
          <el-card class="material-card" shadow="hover">
            <div class="material-info">
              <div class="material-icon">
                <el-icon size="40" :color="getFileTypeColor(material.file_type)">
                  <component :is="getFileTypeIcon(material.file_type)" />
                </el-icon>
              </div>
              
              <div class="material-details">
                <h3 class="material-title">{{ material.title }}</h3>
                <p class="material-description">{{ material.description || '暂无描述' }}</p>
                
                <div class="material-meta">
                  <div class="meta-item">
                    <el-icon><Files /></el-icon>
                    <span>{{ material.formatted_file_size }}</span>
                  </div>
                  <div class="meta-item">
                    <el-icon><Download /></el-icon>
                    <span>{{ material.download_count }} 次下载</span>
                  </div>
                  <div v-if="material.meeting" class="meta-item">
                    <el-icon><Calendar /></el-icon>
                    <span>{{ material.meeting.title }}</span>
                  </div>
                </div>

                <div class="material-actions">
                  <el-button
                    v-if="material.can_download"
                    type="primary"
                    @click="handleDownload(material)"
                    :loading="downloadingIds.includes(material.id)"
                  >
                    <el-icon><Download /></el-icon>
                    下载
                  </el-button>
                  <el-button
                    v-else
                    type="warning"
                    @click="handlePaymentRequired"
                  >
                    <el-icon><Lock /></el-icon>
                    需要付费
                  </el-button>
                  <el-button
                    type="info"
                    text
                    @click="viewMaterialDetail(material)"
                  >
                    <el-icon><View /></el-icon>
                    详情
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 空状态 -->
    <div v-if="materials.length === 0 && !loading" class="empty-state">
      <el-empty description="暂无资料" />
    </div>

    <!-- 分页 -->
    <div v-if="pagination.total > 0" class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.current_page"
        v-model:page-size="pagination.per_page"
        :total="pagination.total"
        :page-sizes="[12, 24, 48, 96]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 资料详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="资料详情"
      width="600px"
    >
      <div v-if="selectedMaterial" class="material-detail">
        <div class="detail-header">
          <el-icon size="60" :color="getFileTypeColor(selectedMaterial.file_type)">
            <component :is="getFileTypeIcon(selectedMaterial.file_type)" />
          </el-icon>
          <div class="detail-info">
            <h2>{{ selectedMaterial.title }}</h2>
            <p>{{ selectedMaterial.description || '暂无描述' }}</p>
          </div>
        </div>
        
        <div class="detail-meta">
          <div class="meta-row">
            <label>文件大小：</label>
            <span>{{ selectedMaterial.formatted_file_size }}</span>
          </div>
          <div class="meta-row">
            <label>文件类型：</label>
            <span>{{ selectedMaterial.file_type }}</span>
          </div>
          <div class="meta-row">
            <label>下载次数：</label>
            <span>{{ selectedMaterial.download_count }} 次</span>
          </div>
          <div v-if="selectedMaterial.meeting" class="meta-row">
            <label>关联会议：</label>
            <span>{{ selectedMaterial.meeting.title }}</span>
          </div>
          <div class="meta-row">
            <label>上传时间：</label>
            <span>{{ formatDate(selectedMaterial.created_at) }}</span>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
        <el-button
          v-if="selectedMaterial?.can_download"
          type="primary"
          @click="handleDownload(selectedMaterial)"
          :loading="downloadingIds.includes(selectedMaterial?.id)"
        >
          <el-icon><Download /></el-icon>
          下载
        </el-button>
        <el-button
          v-else
          type="warning"
          @click="handlePaymentRequired"
        >
          <el-icon><Lock /></el-icon>
          需要付费
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { getMaterials, getMaterialCategories, downloadMaterial } from '@/api/material'
import { getMeetings } from '@/api/meeting'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const materials = ref([])
const categories = ref({})
const meetings = ref([])
const downloadingIds = ref([])

// 筛选条件
const filters = reactive({
  category: '',
  meeting_id: ''
})

// 分页数据
const pagination = reactive({
  current_page: 1,
  per_page: 12,
  total: 0
})

// 对话框
const showDetailDialog = ref(false)
const selectedMaterial = ref(null)

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)
const isPaidUser = computed(() => userStore.isPaidUser)

// 方法
const fetchMaterials = async () => {
  loading.value = true
  try {
    const params = {
      ...filters,
      page: pagination.current_page,
      per_page: pagination.per_page
    }
    
    const response = await getMaterials(params)
    if (response.success) {
      materials.value = response.data.data
      pagination.current_page = response.data.current_page
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('获取资料列表失败:', error)
    ElMessage.error('获取资料列表失败')
  } finally {
    loading.value = false
  }
}

const fetchCategories = async () => {
  try {
    const response = await getMaterialCategories()
    if (response.success) {
      categories.value = response.data
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

const fetchMeetings = async () => {
  try {
    const response = await getMeetings()
    if (response.success) {
      meetings.value = response.data
    }
  } catch (error) {
    console.error('获取会议列表失败:', error)
  }
}

const handleFilterChange = () => {
  pagination.current_page = 1
  fetchMaterials()
}

const resetFilters = () => {
  Object.assign(filters, {
    category: '',
    meeting_id: ''
  })
  pagination.current_page = 1
  fetchMaterials()
}

const handleDownload = async (material) => {
  if (!material.can_download) {
    handlePaymentRequired()
    return
  }

  downloadingIds.value.push(material.id)
  
  try {
    const response = await downloadMaterial(material.id)

    // 检查响应是否包含下载URL
    if (response.success && response.download_url) {
      // 如果返回下载URL，直接打开新窗口下载
      const link = document.createElement('a')
      link.href = response.download_url
      link.download = response.filename || material.original_filename
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } else {
      // 如果返回blob数据，使用原来的方式
      const blob = new Blob([response])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = material.original_filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    }

    ElMessage.success('下载成功')

    // 更新下载次数
    material.download_count++
    
  } catch (error) {
    console.error('下载失败:', error)
    if (error.response?.data?.code === 'PAYMENT_REQUIRED') {
      handlePaymentRequired()
    } else {
      ElMessage.error('下载失败')
    }
  } finally {
    downloadingIds.value = downloadingIds.value.filter(id => id !== material.id)
  }
}

const handlePaymentRequired = () => {
  ElMessageBox.confirm(
    '您需要成为付费用户才能下载资料，是否前往会议注册页面？',
    '需要付费',
    {
      confirmButtonText: '前往注册',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    router.push('/meeting-registration')
  }).catch(() => {
    // 用户取消
  })
}

const viewMaterialDetail = (material) => {
  selectedMaterial.value = material
  showDetailDialog.value = true
}

const handleSizeChange = (size) => {
  pagination.per_page = size
  pagination.current_page = 1
  fetchMaterials()
}

const handleCurrentChange = (page) => {
  pagination.current_page = page
  fetchMaterials()
}

const getFileTypeIcon = (fileType) => {
  const iconMap = {
    'pdf': 'Document',
    'doc': 'Document',
    'docx': 'Document',
    'xls': 'Document',
    'xlsx': 'Document',
    'ppt': 'Document',
    'pptx': 'Document',
    'zip': 'FolderOpened',
    'rar': 'FolderOpened',
    'mp4': 'VideoPlay',
    'avi': 'VideoPlay',
    'mov': 'VideoPlay',
    'jpg': 'Picture',
    'jpeg': 'Picture',
    'png': 'Picture',
    'gif': 'Picture'
  }
  
  const ext = fileType.toLowerCase()
  return iconMap[ext] || 'Document'
}

const getFileTypeColor = (fileType) => {
  const colorMap = {
    'pdf': '#f56565',
    'doc': '#4299e1',
    'docx': '#4299e1',
    'xls': '#48bb78',
    'xlsx': '#48bb78',
    'ppt': '#ed8936',
    'pptx': '#ed8936',
    'zip': '#805ad5',
    'rar': '#805ad5',
    'mp4': '#e53e3e',
    'avi': '#e53e3e',
    'mov': '#e53e3e',
    'jpg': '#38b2ac',
    'jpeg': '#38b2ac',
    'png': '#38b2ac',
    'gif': '#38b2ac'
  }
  
  const ext = fileType.toLowerCase()
  return colorMap[ext] || '#718096'
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchCategories()
  fetchMeetings()
  fetchMaterials()
})
</script>

<style scoped>
.material-download {
  padding: 20px;
  min-height: calc(100vh - 60px);
  background-color: #f5f7fa;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 28px;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.filter-card {
  margin-bottom: 20px;
}

.materials-grid {
  margin-bottom: 30px;
}

.material-card {
  height: 100%;
  transition: transform 0.2s;
}

.material-card:hover {
  transform: translateY(-2px);
}

.material-info {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.material-icon {
  text-align: center;
  margin-bottom: 15px;
}

.material-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.material-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.material-description {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.material-meta {
  margin-bottom: 15px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 5px;
  font-size: 12px;
  color: #909399;
}

.material-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.material-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 30px;
}

.detail-info h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.detail-info p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.detail-meta {
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.meta-row {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
}

.meta-row label {
  width: 100px;
  color: #606266;
  font-weight: 500;
}

.meta-row span {
  color: #303133;
}

@media (max-width: 768px) {
  .material-download {
    padding: 10px;
  }
  
  .page-header h1 {
    font-size: 24px;
  }
  
  .material-actions {
    flex-direction: column;
  }
  
  .detail-header {
    flex-direction: column;
    text-align: center;
  }
}
</style>
