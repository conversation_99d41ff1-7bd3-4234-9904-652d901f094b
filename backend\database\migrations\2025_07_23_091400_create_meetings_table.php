<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meetings', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('会议标题');
            $table->text('description')->nullable()->comment('会议描述');
            $table->datetime('start_time')->comment('开始时间');
            $table->datetime('end_time')->comment('结束时间');
            $table->string('location')->nullable()->comment('会议地点');
            $table->enum('type', ['online', 'offline', 'hybrid'])->default('offline')->comment('会议类型');
            $table->decimal('expert_fee_early', 8, 2)->default(2000.00)->comment('专家早鸟价');
            $table->decimal('expert_fee_normal', 8, 2)->default(2200.00)->comment('专家正常价');
            $table->decimal('student_fee_early', 8, 2)->default(1500.00)->comment('学生早鸟价');
            $table->decimal('student_fee_normal', 8, 2)->default(1600.00)->comment('学生正常价');
            $table->datetime('early_deadline')->comment('早鸟截止时间');
            $table->datetime('registration_deadline')->comment('注册截止时间');
            $table->integer('max_participants')->nullable()->comment('最大参与人数');
            $table->enum('status', ['draft', 'published', 'cancelled', 'completed'])->default('draft')->comment('会议状态');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meetings');
    }
};
