<!DOCTYPE html>
<html>
<head>
    <title>短信验证码登录测试</title>
    <meta charset="utf-8">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .code-container {
            display: flex;
            gap: 10px;
        }
        .code-container input {
            flex: 1;
        }
        button {
            background: #007cba;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            min-width: 120px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .full-width {
            width: 100%;
            margin-top: 10px;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-phones {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .test-phones h4 {
            margin: 0 0 10px 0;
        }
        .test-phones ul {
            margin: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>短信验证码登录测试</h1>
        
        <div class="test-phones">
            <h4>测试手机号（开发模式，不实际发送短信）:</h4>
            <ul>
                <li>13800138000</li>
                <li>13800138001</li>
                <li>13800138002</li>
                <li>13800138003</li>
                <li>13800138004</li>
            </ul>
        </div>

        <div class="form-group">
            <label for="phone">手机号:</label>
            <input type="tel" id="phone" placeholder="请输入手机号" maxlength="11">
        </div>

        <div class="form-group">
            <label for="code">验证码:</label>
            <div class="code-container">
                <input type="text" id="code" placeholder="请输入6位验证码" maxlength="6">
                <button id="sendCodeBtn" onclick="sendCode()">获取验证码</button>
            </div>
        </div>

        <button class="full-width" onclick="login()">登录</button>

        <div id="result" class="result"></div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
            <h3>功能说明:</h3>
            <ul>
                <li>开发环境下使用测试手机号不会实际发送短信</li>
                <li>验证码会在响应中返回，自动填入输入框</li>
                <li>未注册的手机号会自动创建账号</li>
                <li>验证码有效期5分钟</li>
                <li>1分钟内只能发送一次验证码</li>
            </ul>
        </div>
    </div>

    <script>
        let countdown = 0;
        let countdownTimer = null;

        function updateSendButton() {
            const btn = document.getElementById('sendCodeBtn');
            if (countdown > 0) {
                btn.textContent = `${countdown}s后重试`;
                btn.disabled = true;
            } else {
                btn.textContent = '获取验证码';
                btn.disabled = false;
            }
        }

        function startCountdown(seconds) {
            countdown = seconds;
            countdownTimer = setInterval(() => {
                countdown--;
                updateSendButton();
                if (countdown <= 0) {
                    clearInterval(countdownTimer);
                }
            }, 1000);
            updateSendButton();
        }

        function showResult(message, type) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }

        async function sendCode() {
            const phone = document.getElementById('phone').value;
            
            if (!phone) {
                showResult('请输入手机号', 'error');
                return;
            }

            if (!/^1[3-9]\d{9}$/.test(phone)) {
                showResult('手机号格式不正确', 'error');
                return;
            }

            try {
                const response = await fetch('/api/sms/send-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        phone: phone,
                        type: 'login'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showResult('验证码发送成功', 'success');
                    
                    // 开发环境自动填入验证码
                    if (data.data.dev_code) {
                        document.getElementById('code').value = data.data.dev_code;
                        showResult(`验证码发送成功，开发环境验证码: ${data.data.dev_code}`, 'info');
                    }

                    startCountdown(60);
                } else {
                    showResult(data.message, 'error');
                    if (data.wait_seconds) {
                        startCountdown(data.wait_seconds);
                    }
                }
            } catch (error) {
                showResult('发送验证码失败: ' + error.message, 'error');
            }
        }

        async function login() {
            const phone = document.getElementById('phone').value;
            const code = document.getElementById('code').value;

            if (!phone) {
                showResult('请输入手机号', 'error');
                return;
            }

            if (!code) {
                showResult('请输入验证码', 'error');
                return;
            }

            try {
                const response = await fetch('/api/sms/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        phone: phone,
                        code: code
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showResult(`登录成功！用户: ${data.data.user.name}, Token: ${data.data.token.substring(0, 20)}...`, 'success');
                } else {
                    showResult(data.message, 'error');
                }
            } catch (error) {
                showResult('登录失败: ' + error.message, 'error');
            }
        }

        // 手机号输入限制
        document.getElementById('phone').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/\D/g, '');
        });

        // 验证码输入限制
        document.getElementById('code').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/\D/g, '');
        });
    </script>
</body>
</html>
