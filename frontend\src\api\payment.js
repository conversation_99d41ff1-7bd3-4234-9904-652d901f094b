import request from '@/utils/request'

/**
 * 创建支付订单
 * @param {Object} paymentData - 支付数据
 * @param {number} paymentData.meeting_registration_id - 会议注册ID
 * @param {string} paymentData.payment_method - 支付方式 (wechat_native/wechat_jsapi)
 * @param {string} paymentData.openid - 微信openid (JSAPI支付时需要)
 */
export const createPayment = (paymentData) => {
  return request.post('/payments/create', paymentData)
}

/**
 * 查询支付状态
 * @param {number} id - 支付ID
 */
export const getPaymentStatus = (id) => {
  return request.get(`/payments/status/${id}`)
}

/**
 * 获取我的支付记录
 */
export const getMyPayments = () => {
  return request.get('/payments/my-payments')
}

/**
 * 上传转账截图
 * @param {number} paymentId - 支付ID
 * @param {FormData} formData - 包含文件的表单数据
 */
export const uploadTransferScreenshot = (paymentId, formData) => {
  return request.post(`/payments/${paymentId}/upload-screenshot`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 审核线下支付
 * @param {number} paymentId - 支付ID
 * @param {Object} reviewData - 审核数据
 * @param {string} reviewData.action - 审核动作 (approve/reject)
 * @param {string} reviewData.notes - 审核备注
 */
export const reviewOfflinePayment = (paymentId, reviewData) => {
  return request.post(`/payments/${paymentId}/review`, reviewData)
}

/**
 * 轮询支付状态
 * @param {number} paymentId - 支付ID
 * @param {number} maxAttempts - 最大轮询次数
 * @param {number} interval - 轮询间隔(毫秒)
 */
export const pollPaymentStatus = (paymentId, maxAttempts = 60, interval = 2000) => {
  return new Promise((resolve, reject) => {
    let attempts = 0
    
    const poll = async () => {
      try {
        attempts++
        const response = await getPaymentStatus(paymentId)
        
        if (response.data.status === 'success') {
          resolve(response)
        } else if (response.data.status === 'failed') {
          reject(new Error('支付失败'))
        } else if (attempts >= maxAttempts) {
          reject(new Error('支付超时'))
        } else {
          setTimeout(poll, interval)
        }
      } catch (error) {
        if (attempts >= maxAttempts) {
          reject(error)
        } else {
          setTimeout(poll, interval)
        }
      }
    }
    
    poll()
  })
}
