<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('organization')->nullable()->after('real_name')->comment('工作单位');
            $table->boolean('will_present')->default(false)->after('organization')->comment('是否做会议报告');
            $table->string('job_title')->nullable()->after('will_present')->comment('职称');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['organization', 'will_present', 'job_title']);
        });
    }
};
