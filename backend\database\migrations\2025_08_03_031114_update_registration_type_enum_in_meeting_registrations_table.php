<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('meeting_registrations', function (Blueprint $table) {
            // 修改registration_type枚举，添加corporate类型
            $table->enum('registration_type', ['expert', 'student', 'corporate'])->change()->comment('注册类型');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('meeting_registrations', function (Blueprint $table) {
            // 回滚到原来的枚举值
            $table->enum('registration_type', ['expert', 'student'])->change()->comment('注册类型');
        });
    }
};
