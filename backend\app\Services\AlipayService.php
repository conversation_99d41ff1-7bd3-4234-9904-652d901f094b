<?php

namespace App\Services;

use Yansongda\Pay\Pay;
use Yansongda\Pay\Log;
use Illuminate\Support\Facades\Log as LaravelLog;
use Exception;
use \Illuminate\Support\Facades\Cache;

class AlipayService
{
    private $config;

    public function __construct()
    {
        // 检查是否使用证书模式还是普通公钥模式
        $this->config = $this->getCertModeConfig();
    }



    /**
     * 获取证书模式配置
     */
    private function getCertModeConfig()
    {
        return [
            'alipay' => [
                'default' => [
                    // 必填-支付宝分配的 app_id
                    'app_id' => env('ALIPAY_APP_ID', '2021005183661373'),
                    // 必填-应用私钥
                    'app_secret_cert' => env('ALIPAY_PRIVATE_KEY'),
                    // 必填-应用公钥证书路径
                    'app_public_cert_path' => 'file://'.storage_path('cert/appCertPublicKey_2021005183661373.crt'),
                    // 必填-支付宝公钥证书路径
                    'alipay_public_cert_path' => storage_path('cert/alipayCertPublicKey_RSA2.crt'),
                    // 必填-支付宝根证书路径
                    'alipay_root_cert_path' => storage_path('cert/alipayRootCert.crt'),
                    // 选填-默认为正常模式
                    'mode' => env('ALIPAY_MODE', Pay::MODE_NORMAL),
                    // 选填-接口内容加密密钥
                    'app_secret_key' => env('ALIPAY_APP_SECRET_KEY', 'fO6tbDhBxoe4/4/MVIHDkw=='),
                    // 选填-AES密钥
                    'aes_key' => env('ALIPAY_AES_KEY', 'fO6tbDhBxoe4/4/MVIHDkw=='),
                ],
            ],
            'http' => [
                'timeout' => 5.0,
                'connect_timeout' => 5.0,
            ],
            'logger' => [
                'enable' => true,
                'file' => storage_path('logs/alipay.log'),
                'level' => 'info',
                'type' => 'single',
                'max_file' => 30,
            ],
        ];
    }

    /**
     * 检查支付宝配置是否正确
     */
    public function checkConfig()
    {
        $config = $this->config['alipay']['default'];
        $issues = [];

        // 检查必填配置
        if (empty($config['app_id'])) {
            $issues[] = 'ALIPAY_APP_ID 未配置';
        }

        if (empty($config['app_secret_cert'])) {
            $issues[] = 'ALIPAY_PRIVATE_KEY 未配置';
        }

        // 检查证书文件（生产环境）
        if ($config['mode'] !== Pay::MODE_SANDBOX) {
            $certFiles = [
                'app_public_cert_path' => '应用公钥证书',
                'alipay_public_cert_path' => '支付宝公钥证书',
                'alipay_root_cert_path' => '支付宝根证书'
            ];

            foreach ($certFiles as $key => $name) {
                if (!file_exists($config[$key])) {
                    $issues[] = "{$name} 文件不存在: {$config[$key]}";
                }
            }
        }

        // 检查接口内容加密配置
        if (!empty($config['app_secret_key']) || !empty($config['aes_key'])) {
            if (empty($config['app_secret_key'])) {
                $issues[] = '开启接口内容加密时，ALIPAY_APP_SECRET_KEY 不能为空';
            }
            if (empty($config['aes_key'])) {
                $issues[] = '开启接口内容加密时，ALIPAY_AES_KEY 不能为空';
            }
        }

        return [
            'success' => empty($issues),
            'issues' => $issues,
            'config_summary' => [
                'app_id' => $config['app_id'],
                'mode' => $config['mode'],
                'has_encryption' => !empty($config['app_secret_key']) && !empty($config['aes_key']),
                'cert_files_exist' => $config['mode'] === Pay::MODE_SANDBOX ? 'N/A (沙箱模式)' : $this->checkCertFiles()
            ]
        ];
    }

    /**
     * 检查证书文件是否存在
     */
    private function checkCertFiles()
    {
        $config = $this->config['alipay']['default'];
        $files = [
            'app_public_cert_path',
            'alipay_public_cert_path',
            'alipay_root_cert_path'
        ];

        $existing = 0;
        foreach ($files as $file) {
            if (file_exists($config[$file])) {
                $existing++;
            }
        }

        return "{$existing}/3 个证书文件存在";
    }

    /**
     * 创建支付宝网页支付订单
     */
    public function createWebOrder($orderData)
    {
        try {
            // 开发环境下使用模拟支付
            if (env('APP_ENV') === 'local' || env('ALIPAY_SANDBOX', false)) {
                return $this->createMockWebOrder($orderData);
            }

            $order = [
                'out_trade_no' => $orderData['out_trade_no'],
                'total_amount' => $orderData['total_amount'],
                'subject' => $orderData['subject'],
                'return_url' => env('ALIPAY_RETURN_URL', ''),
                'notify_url' => env('ALIPAY_NOTIFY_URL', ''),
            ];

            $result = Pay::alipay($this->config)->web($order);
            $htmlContent = $result->getBody()->getContents();

            // 创建一个临时的支付页面URL
            $paymentToken = md5($orderData['out_trade_no'] . time());
            $paymentUrl = env('APP_URL') . '/alipay-payment/' . $paymentToken;

            // 将HTML内容存储到缓存中，有效期10分钟
            Cache::put('alipay_payment_' . $paymentToken, $htmlContent, 600);
            LaravelLog::info($htmlContent);

            return [
                'success' => true,
                'data' => [
                    'payment_type' => 'web',
                    'payment_url' => $paymentUrl,
                    'payment_token' => $paymentToken,
                    'out_trade_no' => $orderData['out_trade_no']
                ]
            ];

        } catch (Exception $e) {
            LaravelLog::error('创建支付宝支付订单失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '支付服务异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 创建支付宝手机网站支付订单
     */
    public function createWapOrder($orderData)
    {
        try {
            // 开发环境下使用模拟支付
            if (env('APP_ENV') === 'local' || env('ALIPAY_SANDBOX', true)) {
                return $this->createMockWapOrder($orderData);
            }

            $order = [
                'out_trade_no' => $orderData['out_trade_no'],
                'total_amount' => $orderData['total_amount'],
                'subject' => $orderData['subject'],
                'quit_url' => env('ALIPAY_RETURN_URL', ''),
                'return_url' => env('ALIPAY_RETURN_URL', ''),
                'notify_url' => env('ALIPAY_NOTIFY_URL', ''),
            ];

            $result = Pay::alipay($this->config)->wap($order);

            return [
                'success' => true,
                'data' => [
                    'payment_type' => 'wap',
                    'payment_url' => $result->getBody()->getContents(),
                    'out_trade_no' => $orderData['out_trade_no']
                ]
            ];

        } catch (Exception $e) {
            LaravelLog::error('创建支付宝手机支付订单失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '支付服务异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 查询支付订单状态
     */
    public function queryOrder($outTradeNo)
    {
        try {
            $result = Pay::alipay($this->config)->query([
                'out_trade_no' => $outTradeNo,
            ]);

            return [
                'success' => true,
                'data' => $result->toArray()
            ];

        } catch (Exception $e) {
            LaravelLog::error('查询支付宝订单状态失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '查询订单失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 验证支付宝回调签名
     */
    public function verifyCallback($params)
    {
        try {
            $result = Pay::alipay($this->config)->callback($params);
            return [
                'success' => true,
                'data' => $result->toArray()
            ];
        } catch (Exception $e) {
            LaravelLog::error('验证支付宝回调失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '回调验证失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 创建模拟网页支付订单（开发环境）
     */
    private function createMockWebOrder($orderData)
    {
        LaravelLog::info('开发环境：创建模拟支付宝网页支付订单', $orderData);

        $mockUrl = env('APP_URL') . '/mock-alipay-payment?' . http_build_query([
            'out_trade_no' => $orderData['out_trade_no'],
            'total_amount' => $orderData['total_amount'],
            'subject' => $orderData['subject'],
            'return_url' => env('ALIPAY_RETURN_URL', ''),
        ]);

        return [
            'success' => true,
            'data' => [
                'payment_type' => 'web',
                'payment_url' => $mockUrl,
                'out_trade_no' => $orderData['out_trade_no'],
                'is_mock' => true
            ]
        ];
    }

    /**
     * 创建模拟手机支付订单（开发环境）
     */
    private function createMockWapOrder($orderData)
    {
        LaravelLog::info('开发环境：创建模拟支付宝手机支付订单', $orderData);

        $mockUrl = env('APP_URL') . '/mock-alipay-payment?' . http_build_query([
            'out_trade_no' => $orderData['out_trade_no'],
            'total_amount' => $orderData['total_amount'],
            'subject' => $orderData['subject'],
            'return_url' => env('ALIPAY_RETURN_URL', ''),
            'is_mobile' => 1
        ]);

        return [
            'success' => true,
            'data' => [
                'payment_type' => 'wap',
                'payment_url' => $mockUrl,
                'out_trade_no' => $orderData['out_trade_no'],
                'is_mock' => true
            ]
        ];
    }
}
