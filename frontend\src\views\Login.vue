<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="login-background">
      <div class="bg-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
      </div>
    </div>

    <!-- 登录内容 -->
    <div class="login-content">
      <div class="login-card">
        <!-- 头部 -->
        <div class="login-header">
          <div class="logo">
            <div class="logo-icon">
              <el-icon size="40"><Promotion /></el-icon>
            </div>
            <h1>会议管理系统</h1>
          </div>
          <p class="subtitle">欢迎回来，请选择登录方式</p>
        </div>

        <!-- 登录方式选择 -->
        <div class="login-tabs">
          <div class="tab-buttons">
            <button
              :class="['tab-btn', { active: activeTab === 'email' }]"
              @click="activeTab = 'email'"
            >
              <el-icon><User /></el-icon>
              <span>邮箱登录</span>
            </button>
            <button
              :class="['tab-btn', { active: activeTab === 'sms' }]"
              @click="activeTab = 'sms'"
            >
              <el-icon><Message /></el-icon>
              <span>短信登录</span>
            </button>
          </div>
        </div>

        <!-- 邮箱登录表单 -->
        <div v-show="activeTab === 'email'" class="login-form">
          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="rules"
            label-width="0"
            size="large"
          >
            <el-form-item prop="email">
              <div class="input-wrapper">
                <el-icon class="input-icon"><User /></el-icon>
                <el-input
                  v-model="loginForm.email"
                  placeholder="请输入邮箱地址"
                  class="custom-input"
                />
              </div>
            </el-form-item>

            <el-form-item prop="password">
              <div class="input-wrapper">
                <el-icon class="input-icon"><Lock /></el-icon>
                <el-input
                  v-model="loginForm.password"
                  type="password"
                  placeholder="请输入密码"
                  class="custom-input"
                  show-password
                />
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                :loading="loading"
                @click="handleLogin"
                class="login-btn"
                size="large"
              >
                <span v-if="!loading">立即登录</span>
                <span v-else>登录中...</span>
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 短信登录表单 -->
        <div v-show="activeTab === 'sms'" class="login-form">
          <SmsLogin @login-success="handleSmsLoginSuccess" />
        </div>

        <!-- 第三方登录 -->
        <div v-if="false"lass="social-login">
          <div class="divider">
            <span>其他登录方式</span>
          </div>
          <div class="social-buttons">
            <button class="social-btn wechat" @click="showWechatLogin = true">
              <el-icon size="20"><ChatDotRound /></el-icon>
              <span>微信扫码</span>
            </button>
          </div>
        </div>

        <!-- 底部链接 -->
        <div class="login-footer">
          <p>还没有账号？<router-link to="/register" class="register-link">立即注册</router-link></p>
        </div>
      </div>
    </div>

    <!-- 微信扫码登录对话框 -->
    <el-dialog v-model="showWechatLogin" title="微信扫码登录" width="400px" align-center>
      <div class="wechat-login-content">
        <div v-if="wechatQrCode" class="qrcode-container">
          <img :src="wechatQrCode" alt="微信登录二维码" class="qrcode" crossorigin="anonymous" />
          <p class="qrcode-tip">请使用微信扫描二维码关注公众号登录</p>
          <div v-if="wechatLoginStatus === 'waiting'" class="status-waiting">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>等待扫码...</span>
          </div>
          <div v-else-if="wechatLoginStatus === 'success'" class="status-success">
            <el-icon color="#67c23a"><SuccessFilled /></el-icon>
            <span>登录成功！</span>
          </div>
          <div v-else-if="wechatLoginStatus === 'failed'" class="status-failed">
            <el-icon color="#f56c6c"><CircleClose /></el-icon>
            <span>登录失败</span>
            <el-button type="primary" size="small" @click="refreshQrCode" style="margin-top: 10px;">
              重新获取二维码
            </el-button>
          </div>
          <div v-else-if="wechatLoginStatus === 'expired'" class="status-expired">
            <el-icon color="#e6a23c"><Warning /></el-icon>
            <span>二维码已过期</span>
            <el-button type="primary" size="small" @click="refreshQrCode" style="margin-top: 10px;">
              刷新二维码
            </el-button>
          </div>
          <div v-else-if="wechatLoginStatus === 'timeout'" class="status-timeout">
            <el-icon color="#e6a23c"><Warning /></el-icon>
            <span>扫码超时</span>
            <el-button type="primary" size="small" @click="refreshQrCode" style="margin-top: 10px;">
              重新获取
            </el-button>
          </div>
        </div>
        <div v-else class="loading-qrcode">
          <el-icon class="is-loading"><Loading /></el-icon>
          <p>正在生成二维码...</p>
        </div>
      </div>
      <template #footer>
        <el-button @click="closeWechatLogin">关闭</el-button>
        <el-button type="primary" @click="refreshQrCode">刷新二维码</el-button>
      </template>
    </el-dialog>

    <!-- 短信验证码登录对话框 -->
    <el-dialog v-model="showSmsLogin" title="短信验证码登录" width="400px" align-center>
      <SmsLogin @login-success="handleSmsLoginSuccess" />
      <template #footer>
        <el-button @click="showSmsLogin = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, h } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ChatDotRound, Loading, SuccessFilled, Message, User, Lock, Promotion, CircleClose, Warning } from '@element-plus/icons-vue'
import * as authApi from '@/api/auth'
import SmsLogin from '@/components/SmsLogin.vue'

const router = useRouter()
const userStore = useUserStore()

// 当前激活的登录方式
const activeTab = ref('email')

const loginFormRef = ref()
const loading = ref(false)

// 微信登录相关
const showWechatLogin = ref(false)
const wechatQrCode = ref('')
const wechatSceneStr = ref('')
const wechatLoginStatus = ref('waiting')
let wechatPollingTimer = null

// 短信登录相关
const showSmsLogin = ref(false)

const loginForm = reactive({
  email: '',
  password: ''
})

const rules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const result = await userStore.login(loginForm)
      loading.value = false

      if (result.success) {
        ElMessage.success('登录成功')
        router.push('/')
      } else {
        ElMessage.error(result.message)
      }
    }
  })
}

// 微信登录相关方法
const getWechatQrCode = async () => {
  try {
    const response = await authApi.getWechatQrCode()
    // 使用base64图片数据
    wechatQrCode.value = response.data.qrcode_base64
    wechatSceneStr.value = response.data.scene_str
    wechatLoginStatus.value = 'waiting'

    // 开始轮询登录状态
    startPollingWechatLogin()
  } catch (error) {
    console.error('微信二维码获取失败:', error)

    // 处理不同类型的错误
    if (error.response && error.response.data) {
      const errorData = error.response.data

      // 显示详细的错误信息
      showWechatError(errorData)
    } else {
      ElMessage.error('获取微信二维码失败: 网络连接错误')
    }
  }
}

const startPollingWechatLogin = () => {
  if (wechatPollingTimer) {
    clearInterval(wechatPollingTimer)
  }

  let pollCount = 0
  const maxPollCount = 150 // 5分钟 (150 * 2秒)

  wechatPollingTimer = setInterval(async () => {
    pollCount++

    try {
      const response = await authApi.checkWechatLoginStatus(wechatSceneStr.value)
      const status = response.data.status

      if (status === 'success') {
        wechatLoginStatus.value = 'success'
        clearInterval(wechatPollingTimer)

        // 登录成功，保存用户信息
        userStore.user = response.data.user
        userStore.token = response.data.token
        localStorage.setItem('token', response.data.token)

        // 根据登录类型显示不同消息
        const loginType = response.data.login_type
        if (loginType === 'new_user') {
          ElMessage.success('欢迎！已为您自动创建账号并登录')
        } else {
          ElMessage.success('微信登录成功，欢迎回来！')
        }

        showWechatLogin.value = false
        router.push('/')
      } else if (status === 'failed') {
        wechatLoginStatus.value = 'failed'
        clearInterval(wechatPollingTimer)

        // 显示详细的错误信息
        const errorMessage = response.data.message || '微信登录失败'
        const errorDetail = response.data.error_detail

        console.error('微信登录失败:', {
          message: errorMessage,
          detail: errorDetail,
          response: response.data
        })

        if (errorDetail) {
          ElMessage.error(`微信登录失败: ${errorDetail}`)
        } else {
          ElMessage.error(errorMessage)
        }
      } else if (pollCount >= maxPollCount) {
        // 轮询超时
        clearInterval(wechatPollingTimer)
        wechatLoginStatus.value = 'timeout'
        ElMessage.warning('扫码超时，请重新获取二维码')
      }
    } catch (error) {
      console.error('轮询微信登录状态失败:', error)

      // 如果是二维码过期错误
      if (error.response && error.response.status === 400) {
        clearInterval(wechatPollingTimer)
        wechatLoginStatus.value = 'expired'
        ElMessage.warning('二维码已过期，请重新获取')
      } else if (pollCount >= maxPollCount) {
        // 达到最大轮询次数
        clearInterval(wechatPollingTimer)
        wechatLoginStatus.value = 'timeout'
        ElMessage.warning('网络连接超时，请重试')
      }
    }
  }, 2000)
}

const refreshQrCode = async () => {
  // 清除之前的轮询
  if (wechatPollingTimer) {
    clearInterval(wechatPollingTimer)
  }

  // 重置状态
  wechatQrCode.value = ''
  wechatLoginStatus.value = 'waiting'

  // 重新获取二维码
  await getWechatQrCode()
}

// 短信登录成功处理
const handleSmsLoginSuccess = (data) => {
  // 保存用户信息和token
  userStore.user = data.user
  userStore.token = data.token
  localStorage.setItem('token', data.token)

  // 关闭对话框
  showSmsLogin.value = false

  // 跳转到首页
  router.push('/')
}

// 模拟微信扫码登录（仅用于演示）
// 显示微信错误信息
const showWechatError = (errorData) => {
  // 根据错误类型显示不同的处理方式
  if (errorData.error_type === 'ip_whitelist_error' || errorData.error_code === 40164) {
    // IP白名单错误
    ElMessageBox.alert(
      h('div', [
        h('p', { style: 'margin-bottom: 10px; font-weight: bold; color: #e6a23c;' }, errorData.message),
        h('p', { style: 'margin-bottom: 10px;' }, '解决步骤：'),
        h('ol', { style: 'margin: 0; padding-left: 20px;' },
          errorData.suggestions.map(suggestion =>
            h('li', { style: 'margin: 5px 0;' }, suggestion)
          )
        )
      ]),
      '微信公众号配置错误',
      {
        confirmButtonText: '我知道了',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )
  } else if (errorData.error_type === 'auth_error') {
    // 认证错误
    ElMessageBox.alert(
      h('div', [
        h('p', { style: 'margin-bottom: 10px; font-weight: bold; color: #f56c6c;' }, errorData.message),
        h('p', { style: 'margin-bottom: 10px;' }, '请检查以下配置：'),
        h('ul', { style: 'margin: 0; padding-left: 20px;' },
          errorData.suggestions.map(suggestion =>
            h('li', { style: 'margin: 5px 0;' }, suggestion)
          )
        )
      ]),
      '微信认证失败',
      {
        confirmButtonText: '我知道了',
        type: 'error'
      }
    )
  } else {
    // 其他错误
    ElMessageBox.alert(
      h('div', [
        h('p', { style: 'margin-bottom: 10px; font-weight: bold;' }, errorData.message),
        h('p', { style: 'margin-bottom: 10px;' }, '建议解决方案：'),
        h('ul', { style: 'margin: 0; padding-left: 20px;' },
          errorData.suggestions.map(suggestion =>
            h('li', { style: 'margin: 5px 0;' }, suggestion)
          )
        )
      ]),
      '微信接口错误',
      {
        confirmButtonText: '我知道了',
        type: 'error'
      }
    )
  }
}

const mockWechatLogin = async () => {
  try {
    const response = await fetch('http://localhost:8000/api/wechat/mock-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        scene_str: wechatSceneStr.value
      })
    })

    const result = await response.json()

    if (result.success) {
      wechatLoginStatus.value = 'success'
      clearInterval(wechatPollingTimer)

      // 登录成功，保存用户信息
      userStore.user = result.data.user
      userStore.token = result.data.token
      localStorage.setItem('token', result.data.token)

      ElMessage.success('模拟登录成功')
      showWechatLogin.value = false
      router.push('/')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('模拟登录失败')
  }
}

const closeWechatLogin = () => {
  showWechatLogin.value = false
  if (wechatPollingTimer) {
    clearInterval(wechatPollingTimer)
    wechatPollingTimer = null
  }
}

// 监听微信登录对话框打开
const handleWechatLoginOpen = () => {
  getWechatQrCode()
}

// 监听showWechatLogin变化
import { watch } from 'vue'
watch(showWechatLogin, (newVal) => {
  if (newVal) {
    handleWechatLoginOpen()
  } else {
    closeWechatLogin()
  }
})
</script>

<style scoped>
.login-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  overflow: hidden;
}

/* 背景装饰 */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 1;
}

.bg-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 20%;
  right: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  bottom: 10%;
  right: 20%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* 登录内容 */
.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 420px;
  padding: 20px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 头部样式 */
.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.logo h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 标签页样式 */
.login-tabs {
  margin-bottom: 30px;
}

.tab-buttons {
  display: flex;
  background: #f5f7fa;
  border-radius: 12px;
  padding: 4px;
  gap: 4px;
}

.tab-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: white;
  color: #667eea;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-btn:hover:not(.active) {
  color: #333;
}

/* 表单样式 */
.login-form {
  margin-bottom: 30px;
}

.input-wrapper {
  position: relative;
  margin-bottom: 20px;
}

.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  z-index: 3;
}

.custom-input :deep(.el-input__wrapper) {
  padding-left: 45px;
  border-radius: 12px;
  border: 2px solid #e4e7ed;
  box-shadow: none;
  transition: all 0.3s ease;
}

.custom-input :deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

.custom-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.login-btn {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* 社交登录 */
.social-login {
  margin-bottom: 30px;
}

.divider {
  position: relative;
  text-align: center;
  margin: 20px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e4e7ed;
}

.divider span {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 15px;
  color: #999;
  font-size: 12px;
}

.social-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.social-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  background: white;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.social-btn:hover {
  border-color: #07c160;
  color: #07c160;
  transform: translateY(-2px);
}

.social-btn.wechat:hover {
  border-color: #07c160;
  color: #07c160;
  box-shadow: 0 5px 15px rgba(7, 193, 96, 0.2);
}

/* 底部链接 */
.login-footer {
  text-align: center;
}

.login-footer p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.register-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.register-link:hover {
  color: #764ba2;
}

/* 对话框样式 */
.wechat-login-content {
  text-align: center;
  padding: 20px 0;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.qrcode {
  width: 200px;
  height: 200px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.qrcode-tip {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.status-waiting,
.status-success {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.status-success {
  color: #67c23a;
}

.status-failed,
.status-expired,
.status-timeout {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
  margin-top: 15px;
}

.status-failed {
  color: #f56c6c;
}

.status-expired,
.status-timeout {
  color: #e6a23c;
}

.loading-qrcode {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 40px 0;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-content {
    padding: 10px;
  }

  .login-card {
    padding: 30px 20px;
  }

  .logo h1 {
    font-size: 20px;
  }

  .tab-btn {
    padding: 10px 12px;
    font-size: 13px;
  }

  .tab-btn span {
    display: none;
  }
}
</style>