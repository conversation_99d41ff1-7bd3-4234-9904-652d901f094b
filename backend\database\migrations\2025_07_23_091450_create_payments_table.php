<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade')->comment('用户ID');
            $table->foreignId('meeting_registration_id')->constrained()->onDelete('cascade')->comment('会议注册ID');
            $table->string('payment_method')->comment('支付方式');
            $table->string('transaction_id')->unique()->comment('交易ID');
            $table->decimal('amount', 8, 2)->comment('支付金额');
            $table->enum('status', ['pending', 'success', 'failed', 'cancelled', 'refunded'])->default('pending')->comment('支付状态');
            $table->json('payment_data')->nullable()->comment('支付平台返回数据');
            $table->datetime('paid_at')->nullable()->comment('支付时间');
            $table->text('failure_reason')->nullable()->comment('失败原因');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
