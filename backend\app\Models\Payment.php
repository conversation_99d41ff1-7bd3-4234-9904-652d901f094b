<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'meeting_registration_id',
        'payment_method',
        'transaction_id',
        'amount',
        'status',
        'payment_data',
        'paid_at',
        'failure_reason',
        'wechat_transaction_id',
        'wechat_prepay_id',
        'wechat_paid_at',
        'wechat_callback_data',
        'transfer_screenshot',
        'review_status',
        'review_notes',
        'reviewed_by',
        'reviewed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_data' => 'array',
        'paid_at' => 'datetime',
        'wechat_paid_at' => 'datetime',
        'wechat_callback_data' => 'array',
        'reviewed_at' => 'datetime',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联会议注册
     */
    public function meetingRegistration()
    {
        return $this->belongsTo(MeetingRegistration::class);
    }

    /**
     * 关联审核人员
     */
    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * 关联发票记录
     */
    public function invoice()
    {
        return $this->hasOne(\App\Models\Invoice::class);
    }
}
