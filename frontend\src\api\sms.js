import request from '@/utils/request'

/**
 * 发送短信验证码
 * @param {Object} data - 请求数据
 * @param {string} data.phone - 手机号
 * @param {string} data.type - 验证码类型 (login|register|bind)
 */
export const sendSmsCode = (data) => {
  return request({
    url: '/sms/send-code',
    method: 'post',
    data
  })
}

/**
 * 短信验证码登录
 * @param {Object} data - 登录数据
 * @param {string} data.phone - 手机号
 * @param {string} data.code - 验证码
 */
export const smsLogin = (data) => {
  return request({
    url: '/sms/login',
    method: 'post',
    data
  })
}

/**
 * 获取短信配置状态
 */
export const getSmsConfigStatus = () => {
  return request({
    url: '/sms/config-status',
    method: 'get'
  })
}

/**
 * 绑定手机号（需要登录）
 * @param {Object} data - 绑定数据
 * @param {string} data.phone - 手机号
 * @param {string} data.code - 验证码
 */
export const bindPhoneSms = (data) => {
  return request({
    url: '/user/bind-phone-sms',
    method: 'post',
    data
  })
}
