<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class InvoiceController extends Controller
{
    /**
     * 获取用户的发票列表（只包含已支付的订单）
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $invoices = Invoice::getByUser($user->id);

        // 转换数据格式以适配前端
        $invoiceList = $invoices->map(function ($invoice) {
            return [
                'id' => $invoice->id,
                'orderNumber' => $invoice->payment->transaction_id,
                'amount' => $invoice->amount,
                'invoiceTitle' => $invoice->invoice_title,
                'issueDate' => $invoice->issue_date ? $invoice->issue_date->format('Y-m-d') : '',
                'status' => $invoice->status_text,
                'downloadUrl' => $invoice->file_path ? "/api/invoices/{$invoice->id}/download" : '',
                'canDownload' => $invoice->canDownload(),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $invoiceList
        ]);
    }

    /**
     * 下载发票文件
     */
    public function download(Request $request, $id)
    {
        $user = $request->user();
        $invoice = Invoice::where('user_id', $user->id)->find($id);

        if (!$invoice) {
            return response()->json([
                'success' => false,
                'message' => '发票不存在'
            ], 404);
        }

        if (!$invoice->canDownload()) {
            return response()->json([
                'success' => false,
                'message' => '发票尚未开具或文件不存在'
            ], 400);
        }

        if (!Storage::disk('public')->exists($invoice->file_path)) {
            return response()->json([
                'success' => false,
                'message' => '发票文件不存在'
            ], 404);
        }

        $filename = "发票_{$invoice->payment->transaction_id}.pdf";
        return Storage::disk('public')->download($invoice->file_path, $filename);
    }
}
