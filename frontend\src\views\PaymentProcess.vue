<!--
  支付方式隐藏说明：
  - 微信支付和支付宝支付已临时隐藏（v-if="false"）
  - 所有相关代码都保留，如需恢复请将对应的 v-if="false" 改为 v-if="true" 或直接删除
  - 默认支付方式已改为线下转账
  - 恢复时需要同时修改 selectedPaymentMethod 的默认值
-->
<template>
  <div class="payment-process">
    <el-card>
      <template #header>
        <h2>支付订单</h2>
      </template>

      <!-- 加载状态 -->
      <div v-if="pageLoading" class="loading-container">
        <el-icon class="is-loading" size="40"><Loading /></el-icon>
        <p>正在加载订单信息...</p>
      </div>

      <!-- 订单信息 -->
      <div class="order-info" v-else-if="registrationData">
        <h3>订单详情</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="会议名称">
            {{ registrationData.meeting?.title }}
          </el-descriptions-item>
          <el-descriptions-item label="注册类型">
            {{ getRegistrationTypeLabel(registrationData.registration_type) }}
          </el-descriptions-item>
          <el-descriptions-item label="费用金额">
            ¥{{ registrationData.fee_amount }}
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">
            {{ formatDate(registrationData.registered_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 支付方式选择 -->
      <div class="payment-methods">
        <h3>选择支付方式</h3>
        <div class="payment-options">
          <!-- 微信支付选项 - 临时隐藏，代码保留 -->
          <div
            v-if="false"
            class="payment-option"
            :class="{ 'is-checked': selectedPaymentMethod === 'wechat_native' }"
            @click="selectPaymentMethod('wechat_native')"
          >
            <div class="payment-option-content">
              <div>
                <el-icon color="#07c160"><ChatDotRound /></el-icon>
                <span>微信扫码支付</span>
              </div>
              <div class="payment-desc">使用微信扫码完成支付</div>
            </div>
          </div>
          <!-- 支付宝支付选项 - 临时隐藏，代码保留 -->
          <div
            v-if="false"
            class="payment-option"
            :class="{ 'is-checked': selectedPaymentMethod === 'alipay_web' }"
            @click="selectPaymentMethod('alipay_web')"
          >
            <div class="payment-option-content">
              <div>
                <el-icon color="#1677ff"><Money /></el-icon>
                <span>支付宝支付</span>
              </div>
              <div class="payment-desc">使用支付宝完成支付</div>
            </div>
          </div>
          <div
            class="payment-option"
            :class="{ 'is-checked': selectedPaymentMethod === 'offline_transfer' }"
            @click="selectPaymentMethod('offline_transfer')"
          >
            <div class="payment-option-content">
              <div>
                <el-icon color="#409eff"><CreditCard /></el-icon>
                <span>线下对公转账</span>
              </div>
              <div class="payment-desc">通过银行转账到对公账户，上传转账凭证</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 对公账户信息 -->
      <div v-if="selectedPaymentMethod === 'offline_transfer'" class="bank-info">
        <h3>对公账户信息</h3>
        <el-card class="bank-card">
          <div class="bank-details">
            <div class="bank-item">
              <span class="label">收款单位：</span>
              <span class="value">{{ bankInfo.account_name }}</span>
            </div>
            <div class="bank-item">
              <span class="label">银行账号：</span>
              <span class="value">{{ bankInfo.account_number }}</span>
              <el-button size="small" @click="copyToClipboard(bankInfo.account_number)">复制</el-button>
            </div>
            <div class="bank-item">
              <span class="label">开户银行：</span>
              <span class="value">{{ bankInfo.bank_name }}</span>
            </div>
            <div class="bank-item">
              <span class="label">行号（市内）：</span>
              <span class="value">{{ bankInfo.bank_in_city }}</span>
            </div>
            <div class="bank-item">
              <span class="label">行号（市外）：</span>
              <span class="value">{{ bankInfo.bank_out_city }}</span>
            </div>
            <div class="bank-item">
              <span class="label">联系电话：</span>
              <span class="value">{{ bankInfo.contact_phone }}</span>
            </div>
          </div>
          <el-alert
            title="转账说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>1. 转账务必填写备注信息：第九届光谱会议+姓名+单位</p>
              <p>2. 请使用银行转账方式将费用转入上述对公账户</p>
              <p>3. 转账金额：¥{{ registrationData?.fee_amount }}</p>
              <p>4. 转账完成后请上传转账截图或凭证</p>
            </template>
          </el-alert>
        </el-card>
      </div>

      <!-- 支付按钮 -->
      <div class="payment-actions">
        <el-button
          type="primary"
          size="large"
          :loading="paymentLoading"
          :disabled="!selectedPaymentMethod"
          @click="createPayment"
        >
          {{ selectedPaymentMethod === 'offline_transfer' ? '确认转账信息' : '立即支付' }} ¥{{ registrationData?.fee_amount }}
        </el-button>
      </div>
    </el-card>

    <!-- 线下转账上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="上传转账凭证"
      width="500px"
      align-center
      :close-on-click-modal="false"
    >
      <div class="upload-container">
        <el-alert
          title="请上传转账截图或银行回单"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>支持格式：JPG、PNG、JPEG</p>
            <p>文件大小：不超过5MB</p>
            <p>请确保截图清晰，包含转账金额、时间等关键信息</p>
          </template>
        </el-alert>

        <div class="upload-area">
          <el-upload
            ref="uploadRef"
            :action="uploadAction"
            :headers="uploadHeaders"
            name="screenshot"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :show-file-list="false"
            :file-list="[]"
            accept="image/*"
            drag
            :auto-upload="true"
            :limit="1"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
          </el-upload>
        </div>

        <div v-if="uploadedScreenshot" class="uploaded-preview">
          <h4>已上传的凭证：</h4>
          <img :src="uploadedScreenshot" alt="转账凭证" class="screenshot-preview" />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showUploadDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmUpload"
            :disabled="!uploadedScreenshot"
          >
            确认提交
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 微信扫码支付对话框 -->
    <el-dialog 
      v-model="showQrCodeDialog" 
      title="微信扫码支付" 
      width="400px" 
      align-center
      :close-on-click-modal="false"
    >
      <div class="qrcode-payment">
        <div v-if="qrCodeUrl" class="qrcode-container">
          <!-- 模拟支付界面 -->
          <div v-if="isMockPayment" class="mock-payment">
            <div class="mock-qrcode">
              <el-icon size="80" color="#07c160"><ChatDotRound /></el-icon>
              <h3>模拟微信支付</h3>
              <p class="mock-tip">这是开发环境的模拟支付</p>
              <div class="payment-amount">支付金额：¥{{ registrationData?.fee_amount }}</div>
              <el-button type="success" size="large" @click="simulatePaymentSuccess" :loading="simulatingPayment">
                模拟支付成功
              </el-button>
              <p class="mock-note">点击按钮模拟支付成功，实际开发中请配置真实的微信支付</p>
            </div>
          </div>

          <!-- 真实支付二维码 -->
          <div v-else>
            <canvas ref="qrCodeCanvas" class="qrcode"></canvas>
            <p class="qrcode-tip">请使用微信扫描二维码完成支付</p>
            <div class="payment-amount">支付金额：¥{{ registrationData?.fee_amount }}</div>
          </div>
          
          <div class="payment-status">
            <div v-if="paymentStatus === 'pending'" class="status-waiting">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>等待支付...</span>
            </div>
            <div v-else-if="paymentStatus === 'success'" class="status-success">
              <el-icon color="#67c23a"><SuccessFilled /></el-icon>
              <span>支付成功！</span>
            </div>
            <div v-else-if="paymentStatus === 'failed'" class="status-failed">
              <el-icon color="#f56c6c"><CircleCloseFilled /></el-icon>
              <span>支付失败</span>
            </div>
          </div>
        </div>
        <div v-else class="loading-qrcode">
          <el-icon class="is-loading"><Loading /></el-icon>
          <p>正在生成支付二维码...</p>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="closePaymentDialog">取消支付</el-button>
        <el-button type="primary" @click="refreshPayment">刷新</el-button>
      </template>
    </el-dialog>

    <!-- 支付成功对话框 -->
    <el-dialog 
      v-model="showSuccessDialog" 
      title="支付成功" 
      width="400px" 
      align-center
      :close-on-click-modal="false"
    >
      <div class="success-content">
        <el-icon color="#67c23a" size="60"><SuccessFilled /></el-icon>
        <h3>支付成功！</h3>
        <p>🎉 恭喜您！会议注册已完成</p>
        <div class="success-details">
          <p>✅ 支付已确认</p>
          <p>✅ 注册状态已更新</p>
          <p>✅ 您已升级为付费用户</p>
          <!--
          <p>📧 确认邮件将发送至您的邮箱</p>-->
        </div>
      </div>
      
      <template #footer>
        <el-button type="primary" @click="goToMyRegistrations">查看我的注册</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick,computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ChatDotRound,
  Loading,
  SuccessFilled,
  CircleCloseFilled,
  CreditCard,
  Money,
  UploadFilled
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { useMeetingStore } from '@/stores/meeting'
import * as paymentApi from '@/api/payment'
import * as meetingApi from '@/api/meeting'
import QRCode from 'qrcode'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const meetingStore = useMeetingStore()

// 响应式数据
const registrationData = ref(null)
// 默认选择线下转账（微信和支付宝已隐藏）
const selectedPaymentMethod = ref('offline_transfer')
const paymentLoading = ref(false)
const pageLoading = ref(true)
const showQrCodeDialog = ref(false)
const showUploadDialog = ref(false)
const showSuccessDialog = ref(false)
const qrCodeUrl = ref('')
const paymentStatus = ref('pending')
const currentPayment = ref(null)
const qrCodeCanvas = ref(null)
const isMockPayment = ref(false)
const simulatingPayment = ref(false)
const uploadedScreenshot = ref('')
const uploadRef = ref(null)
let paymentPollingTimer = null

// 对公账户信息
const bankInfo = reactive({
  account_name: '中国地质大学（武汉）',
  account_number: '************',
  bank_name: '中国银行武汉地大支行',
  bank_branch: '徐东支行',
  bank_in_city:'846006',
  bank_out_city:'************',
  contact_phone: '***********'
})

// 上传配置
const uploadAction = computed(() => {
  return currentPayment.value ?
    `/api/payments/${currentPayment.value.id}/upload-screenshot` :
    ''
})
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${userStore.token}`
}))

// 初始化
onMounted(async () => {
  await loadRegistrationData()
})

onUnmounted(() => {
  if (paymentPollingTimer) {
    clearInterval(paymentPollingTimer)
  }
})

// 加载注册数据
const loadRegistrationData = async () => {
  const registrationId = route.query.registrationId
  if (!registrationId) {
    ElMessage.error('缺少注册信息')
    router.push('/meetings')
    return
  }

  try {
    pageLoading.value = true

    console.log('开始加载注册数据，registrationId:', registrationId)

    // 直接通过API获取注册记录，避免store缓存问题
    const response = await meetingApi.getMyRegistrations()
    console.log('API响应:', response)

    // 处理分页数据
    const registrations = response.data?.data || response.data
    console.log('注册记录列表:', registrations)

    registrationData.value = registrations.find(
      reg => reg.id == registrationId
    )

    console.log('找到的注册记录:', registrationData.value)

    if (!registrationData.value) {
      ElMessage.error('注册记录不存在，请重新注册')
      console.error('未找到注册记录，registrationId:', registrationId, '可用记录:', registrations)
      router.push('/meetings')
      return
    }

    // 如果已经支付成功，直接显示成功页面
    if (registrationData.value.payment_status === 'paid') {
      ElMessage.success('该会议已注册成功')
      showSuccessDialog.value = true
      return
    }

    console.log('注册数据加载成功:', registrationData.value)
  } catch (error) {
    console.error('加载注册数据失败:', error)
    console.error('错误详情:', error.response?.data || error.message)
    ElMessage.error(`加载注册数据失败：${error.response?.data?.message || error.message}`)
    router.push('/meetings')
  } finally {
    pageLoading.value = false
  }
}



// 生成二维码
const generateQRCode = async (url) => {
  if (!qrCodeCanvas.value || !url) return

  try {
    await QRCode.toCanvas(qrCodeCanvas.value, url, {
      width: 200,
      height: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
    console.log('二维码生成成功:', url)
  } catch (error) {
    console.error('二维码生成失败:', error)
    ElMessage.error('二维码生成失败')
  }
}

// 创建支付订单
const createPayment = async () => {
  if (!registrationData.value) return

  paymentLoading.value = true
  try {
    const paymentData = {
      meeting_registration_id: registrationData.value.id,
      payment_method: selectedPaymentMethod.value
    }

    const response = await paymentApi.createPayment(paymentData)
    currentPayment.value = response.data

    console.log('支付订单创建成功:', response.data)

    if (selectedPaymentMethod.value === 'wechat_native') {
      // 扫码支付
      const codeUrl = response.data.payment_data?.code_url
      console.log('获取到二维码URL:', codeUrl)

      if (codeUrl) {
        qrCodeUrl.value = codeUrl

        // 检测是否为模拟支付
        isMockPayment.value = codeUrl.startsWith('mock://')

        showQrCodeDialog.value = true

        if (!isMockPayment.value) {
          // 真实支付，生成二维码
          await nextTick()
          await generateQRCode(codeUrl)
          startPaymentPolling()
        } else {
          // 模拟支付，不需要轮询
          console.log('检测到模拟支付，显示模拟支付界面')
        }
      } else {
        ElMessage.error('获取支付二维码失败')
      }
    } else if (selectedPaymentMethod.value === 'alipay_web') {
      // 支付宝支付
      const paymentUrl = response.data.payment_data?.payment_url
      console.log('获取到支付宝支付URL:', paymentUrl)

      if (paymentUrl) {
        // 跳转到支付宝支付页面
        window.open(paymentUrl, '_blank')

        // 开始轮询支付状态
        startPaymentPolling()

        ElMessage.info('请在新窗口中完成支付宝支付')
      } else {
        ElMessage.error('获取支付宝支付链接失败')
      }
    } else if (selectedPaymentMethod.value === 'offline_transfer') {
      // 线下转账，获取银行账户信息并显示上传对话框
      const bankData = response.data.payment_data?.bank_info
      if (bankData) {
        Object.assign(bankInfo, bankData)
      }

      ElMessage.success('请按照提供的账户信息完成转账，然后上传转账凭证')
      showUploadDialog.value = true
    }

  } catch (error) {
    ElMessage.error('创建支付订单失败')
  } finally {
    paymentLoading.value = false
  }
}

// 选择支付方式
const selectPaymentMethod = (method) => {
  selectedPaymentMethod.value = method
  console.log('支付方式变更:', method)
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 文件上传前验证
const beforeUpload = (file) => {
  console.log('准备上传文件:', file)

  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5
  const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']

  if (!isImage || !allowedTypes.includes(file.type)) {
    ElMessage.error('只能上传 JPG、PNG、JPEG 格式的图片!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }

  console.log('文件验证通过，开始上传')
  return true
}

// 上传成功处理
const handleUploadSuccess = (response, file) => {
  console.log('上传响应:', response)
  try {
    if (response && response.success) {
      // 安全地获取截图URL
      const screenshotUrl = response.data?.screenshot_url
      if (screenshotUrl) {
        uploadedScreenshot.value = screenshotUrl
      } else if (file && file.raw) {
        uploadedScreenshot.value = URL.createObjectURL(file.raw)
      }

      ElMessage.success('转账截图上传成功，支付已确认！')

      // 如果支付已成功，直接显示成功对话框
      if (response.data?.payment_status === 'success') {
        showUploadDialog.value = false
        handlePaymentSuccess()
      }
    } else {
      ElMessage.error(response?.message || '上传失败')
    }
  } catch (error) {
    console.error('处理上传响应时出错:', error)
    ElMessage.error('处理上传结果时出错')
  }
}

// 上传失败处理
const handleUploadError = (error) => {
  console.error('上传失败:', error)
  ElMessage.error('文件上传失败，请重试')
}

// 确认上传
const confirmUpload = () => {
  if (!uploadedScreenshot.value) {
    ElMessage.warning('请先上传转账截图')
    return
  }

  showUploadDialog.value = false
  ElMessage.success('转账凭证已提交成功')

  // 刷新用户信息和注册状态
  userStore.getCurrentUser()
  handlePaymentSuccess()
}

// 开始轮询支付状态
const startPaymentPolling = () => {
  if (paymentPollingTimer) {
    clearInterval(paymentPollingTimer)
  }

  paymentPollingTimer = setInterval(async () => {
    try {
      const response = await paymentApi.getPaymentStatus(currentPayment.value.id)
      const status = response.data.status

      if (status === 'success') {
        paymentStatus.value = 'success'
        clearInterval(paymentPollingTimer)
        setTimeout(() => {
          showQrCodeDialog.value = false
          handlePaymentSuccess()
        }, 2000)
      } else if (status === 'failed') {
        paymentStatus.value = 'failed'
        clearInterval(paymentPollingTimer)
      }
    } catch (error) {
      console.error('轮询支付状态失败:', error)
    }
  }, 3000)
}

// 处理支付成功
const handlePaymentSuccess = () => {
  showSuccessDialog.value = true
  // 刷新注册数据
  meetingStore.fetchMyRegistrations()
}

// 关闭支付对话框
const closePaymentDialog = () => {
  showQrCodeDialog.value = false
  if (paymentPollingTimer) {
    clearInterval(paymentPollingTimer)
  }
}

// 刷新支付
const refreshPayment = () => {
  createPayment()
}

// 模拟支付成功
const simulatePaymentSuccess = async () => {
  if (!currentPayment.value) return

  simulatingPayment.value = true
  try {
    // 调用后端的模拟支付成功API
    const response = await fetch(`/api/test/simulate-payment-success/${currentPayment.value.id}`, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()

    if (result.success) {
      ElMessage.success('模拟支付成功！')
      paymentStatus.value = 'success'

      setTimeout(() => {
        showQrCodeDialog.value = false
        handlePaymentSuccess()
      }, 1500)
    } else {
      ElMessage.error(result.message || '模拟支付失败')
    }
  } catch (error) {
    console.error('模拟支付失败:', error)
    ElMessage.error('模拟支付失败，请重试')
  } finally {
    simulatingPayment.value = false
  }
}

// 跳转到我的注册
const goToMyRegistrations = () => {
  showSuccessDialog.value = false
  router.push('/profile')
}

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取注册类型标签
const getRegistrationTypeLabel = (type) => {
  const labels = {
    'expert': '正式代表',
    'student': '学生代表',
    'corporate': '企业代表'
  }
  return labels[type] || type
}
</script>

<style scoped>
.payment-process {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.order-info {
  margin-bottom: 30px;
}

.order-info h3 {
  margin-bottom: 15px;
  color: #303133;
}

.payment-methods {
  margin-bottom: 30px;
}

.payment-methods h3 {
  margin-bottom: 15px;
  color: #303133;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.payment-option {
  display: block;
  margin: 0;
  padding: 0;
  border: none;
  width: 100%;
  cursor: pointer;
}

.payment-option-content {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 500;
  flex-direction: column;
  align-items: flex-start;
}

.payment-option-content > div:first-child {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.payment-option:hover .payment-option-content {
  border-color: #409eff;
  background: #f0f9ff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.payment-option.is-checked .payment-option-content {
  border-color: #409eff;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f4ff 100%);
  color: #409eff;
}

.payment-option-content .el-icon {
  font-size: 24px;
}

.payment-option-content span {
  flex: 1;
  color: inherit;
}

.payment-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  font-weight: normal;
}

.mock-payment {
  text-align: center;
  padding: 20px;
}

.mock-qrcode {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 30px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f4ff 100%);
  border-radius: 12px;
  border: 2px dashed #409eff;
}

.mock-qrcode h3 {
  margin: 0;
  color: #303133;
  font-size: 20px;
}

.mock-tip {
  color: #409eff;
  font-size: 14px;
  margin: 0;
  font-weight: 500;
}

.mock-note {
  font-size: 12px;
  color: #909399;
  margin: 0;
  line-height: 1.4;
  max-width: 300px;
}

.payment-actions {
  text-align: center;
  padding: 20px 0;
}

.qrcode-payment {
  text-align: center;
  padding: 20px;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode {
  width: 200px;
  height: 200px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  margin-bottom: 15px;
  background: #fff;
}

.qrcode-tip {
  color: #606266;
  margin-bottom: 10px;
}

.payment-amount {
  font-size: 18px;
  font-weight: bold;
  color: #e6a23c;
  margin-bottom: 20px;
}

.payment-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 15px;
}

.status-waiting {
  color: #409eff;
}

.status-success {
  color: #67c23a;
}

.status-failed {
  color: #f56c6c;
}

.loading-qrcode {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  color: #909399;
}

.success-content {
  text-align: center;
  padding: 20px;
}

.success-content h3 {
  margin: 15px 0;
  color: #303133;
}

.success-details {
  margin-top: 20px;
  text-align: left;
  background: #f0f9ff;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #67c23a;
}

.success-details p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.loading-container {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.loading-container p {
  margin-top: 15px;
  font-size: 14px;
}

.success-content p {
  color: #606266;
  line-height: 1.6;
}
</style>
