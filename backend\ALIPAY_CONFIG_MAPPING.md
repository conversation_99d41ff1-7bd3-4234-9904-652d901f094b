# 支付宝配置对照表

## 官方Demo配置 vs 项目配置

### 支付宝官方Demo配置文件
```php
<?php
$config = array (	
    //应用ID,您的APPID。
    'app_id' => "2021005183661373",

    //商户私钥
    'merchant_private_key' => "你的应用私钥内容",
    
    //异步通知地址
    'notify_url' => "http://外网可访问网关地址/alipay.trade.page.pay-PHP-UTF-8/notify_url.php",
    
    //同步跳转
    'return_url' => "http://外网可访问网关地址/alipay.trade.page.pay-PHP-UTF-8/return_url.php",

    //编码格式
    'charset' => "UTF-8",

    //签名方式
    'sign_type'=>"RSA2",

    //支付宝网关
    'gatewayUrl' => "https://openapi.alipay.com/gateway.do",

    //支付宝公钥,查看地址：https://openhome.alipay.com/platform/keyManage.htm 对应APPID下的支付宝公钥。
    'alipay_public_key' => "你的支付宝公钥内容",

    //日志路径
    'log_path' => "",
);
```

### 项目环境变量配置
```env
# 支付宝支付配置
ALIPAY_APP_ID=2021005183661373
# 应用私钥（对应Demo中的merchant_private_key）
ALIPAY_PRIVATE_KEY=你的应用私钥内容
# 支付宝公钥（对应Demo中的alipay_public_key）
ALIPAY_PUBLIC_KEY=你的支付宝公钥内容
# 支付模式：sandbox=沙箱模式，normal=正式模式
ALIPAY_MODE=sandbox
ALIPAY_SANDBOX=true
# 回调地址（对应Demo中的notify_url和return_url）
ALIPAY_NOTIFY_URL=http://localhost:8000/api/payments/alipay/notify
ALIPAY_RETURN_URL=http://localhost:8000/api/payments/alipay/return
# 是否使用证书模式（false=普通公钥模式，true=证书模式）
ALIPAY_USE_CERT_MODE=false
# 接口内容加密配置（如果支付宝开启了接口内容加密）
ALIPAY_APP_SECRET_KEY=fO6tbDhBxoe4/4/MVIHDkw==
ALIPAY_AES_KEY=fO6tbDhBxoe4/4/MVIHDkw==
```

## 配置项对照表

| 官方Demo配置项 | 项目环境变量 | 说明 |
|---------------|-------------|------|
| `app_id` | `ALIPAY_APP_ID` | 支付宝分配的应用ID |
| `merchant_private_key` | `ALIPAY_PRIVATE_KEY` | 你的应用私钥 |
| `alipay_public_key` | `ALIPAY_PUBLIC_KEY` | 支付宝公钥 |
| `notify_url` | `ALIPAY_NOTIFY_URL` | 异步通知地址 |
| `return_url` | `ALIPAY_RETURN_URL` | 同步跳转地址 |
| `charset` | 固定UTF-8 | 编码格式 |
| `sign_type` | 固定RSA2 | 签名方式 |
| `gatewayUrl` | 自动处理 | 支付宝网关地址 |

## 配置步骤

### 1. 从支付宝开放平台获取信息
1. 登录 [支付宝开放平台](https://open.alipay.com/)
2. 进入你的应用详情页面
3. 在"开发设置"中找到：
   - **应用ID** (app_id)
   - **支付宝公钥** (alipay_public_key)

### 2. 生成应用密钥对
```bash
# 生成私钥
openssl genrsa -out app_private_key.pem 2048

# 生成公钥
openssl rsa -in app_private_key.pem -pubout -out app_public_key.pem
```

### 3. 上传公钥到支付宝
1. 将 `app_public_key.pem` 的内容复制
2. 在支付宝开放平台的"开发设置"中粘贴到"应用公钥"
3. 保存后获取"支付宝公钥"

### 4. 配置项目环境变量
将获取到的信息填入 `.env` 文件：
```env
ALIPAY_APP_ID=你的应用ID
ALIPAY_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----
你的私钥内容
-----END RSA PRIVATE KEY-----"
ALIPAY_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----
支付宝公钥内容
-----END PUBLIC KEY-----"
```

## 验证配置
访问以下URL检查配置是否正确：
```
http://localhost:8000/api/test/alipay-config
```

## 注意事项
1. **私钥安全**：私钥内容不要泄露，不要提交到版本控制系统
2. **公钥格式**：确保公钥包含完整的BEGIN和END标记
3. **回调地址**：生产环境需要配置外网可访问的回调地址
4. **沙箱测试**：开发阶段建议使用沙箱模式进行测试
