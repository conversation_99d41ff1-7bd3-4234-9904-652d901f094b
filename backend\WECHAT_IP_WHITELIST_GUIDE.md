# 微信公众号IP白名单配置指南

## 🚨 当前问题

微信API调用失败，错误信息：
```
invalid ip **************, not in whitelist
```

这表示您的服务器IP地址 `**************` 没有在微信公众号的IP白名单中。

## 📋 解决步骤

### 1. 登录微信公众平台
- 访问：https://mp.weixin.qq.com/
- 使用管理员账号登录

### 2. 进入IP白名单设置
- 左侧菜单：**开发** → **基本配置**
- 找到 **IP白名单** 部分
- 点击 **修改**

### 3. 添加服务器IP
将以下IP地址添加到白名单：
```
**************
```

### 4. 保存配置
- 点击 **确定** 保存
- 等待配置生效（通常几分钟内）

## 🔍 如何获取服务器IP

### 方法1：通过API查询
访问：`http://ai.whlxyc.cn/api/wechat/test-config`

### 方法2：命令行查询
```bash
curl ifconfig.me
```

### 方法3：在线查询
访问：https://www.whatismyip.com/

## ⚠️ 注意事项

### IP白名单限制
- 最多可以添加 **5个IP地址**
- 支持IPv4和IPv6格式
- 不支持IP段，必须是具体IP

### 安全建议
- 只添加必要的服务器IP
- 定期检查和更新IP白名单
- 避免添加动态IP地址

### 常见问题
1. **配置后仍然失败**：等待5-10分钟让配置生效
2. **IP地址变化**：如果使用动态IP，需要及时更新白名单
3. **多服务器部署**：每个服务器IP都需要添加到白名单

## 🧪 测试验证

### 配置完成后测试
1. 等待5-10分钟
2. 访问：`http://ai.whlxyc.cn/api/wechat/qrcode`
3. 检查是否返回真实的微信二维码

### 成功标志
- API返回 `"type": "real"`
- 二维码URL包含 `mp.weixin.qq.com`
- 日志显示 "微信二维码生成成功"

## 🔄 备用方案

在IP白名单配置完成之前，系统会：
1. 自动降级到演示模式
2. 显示提示二维码
3. 提供模拟登录功能
4. 保持所有其他功能正常

## 📞 技术支持

如果配置过程中遇到问题：
1. 检查微信公众号管理员权限
2. 确认IP地址格式正确
3. 等待足够的配置生效时间
4. 查看Laravel日志获取详细错误信息

---

**配置完成后，您的系统将支持真实的微信扫码关注登录功能！**
