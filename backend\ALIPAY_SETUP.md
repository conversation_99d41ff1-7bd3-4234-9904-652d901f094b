# 支付宝支付配置指南

## 1. 申请支付宝开放平台账号

### 1.1 注册开发者账号
1. 访问 [支付宝开放平台](https://open.alipay.com/)
2. 注册开发者账号并完成实名认证
3. 创建应用并获取 APPID

### 1.2 配置应用信息
1. 在支付宝开放平台控制台中配置应用
2. 添加功能包：手机网站支付、电脑网站支付
3. 配置应用网关和授权回调地址

## 2. 配置支付宝密钥

支付宝支付有两种配置模式：**普通公钥模式**（推荐）和**证书模式**

### 2.1 普通公钥模式（推荐，与官方Demo一致）

#### 2.1.1 生成RSA密钥对
```bash
# 生成私钥
openssl genrsa -out app_private_key.pem 2048

# 生成公钥
openssl rsa -in app_private_key.pem -pubout -out app_public_key.pem
```

#### 2.1.2 配置支付宝开放平台
1. 登录支付宝开放平台
2. 进入应用详情页面
3. 在"开发设置"中选择"普通公钥"模式
4. 将生成的公钥内容粘贴到"应用公钥"框中
5. 保存后，复制"支付宝公钥"内容

### 2.2 证书模式（可选）

#### 2.2.1 生成RSA密钥对
同普通公钥模式

#### 2.2.2 上传公钥到支付宝
1. 将生成的公钥内容复制到支付宝开放平台
2. 获取支付宝公钥证书

## 3. 下载证书文件

### 3.1 下载必要证书
从支付宝开放平台下载以下证书：
- 应用公钥证书 (appCertPublicKey.crt)
- 支付宝公钥证书 (alipayCertPublicKey_RSA2.crt)
- 支付宝根证书 (alipayRootCert.crt)

### 3.2 放置证书文件
将证书文件放置到以下目录：
```
backend/storage/cert/
├── appCertPublicKey.crt
├── alipayCertPublicKey_RSA2.crt
└── alipayRootCert.crt
```

## 4. 配置环境变量

### 4.1 修改 .env 文件
```env
# 支付宝支付配置
ALIPAY_APP_ID=你的应用APPID
ALIPAY_PRIVATE_KEY=你的应用私钥内容
ALIPAY_MODE=normal
ALIPAY_SANDBOX=false
ALIPAY_NOTIFY_URL=https://yourdomain.com/api/payments/alipay/notify
ALIPAY_RETURN_URL=https://yourdomain.com/api/payments/alipay/return
FRONTEND_URL=https://yourdomain.com

# 接口内容加密配置（如果支付宝开启了接口内容加密）
ALIPAY_APP_SECRET_KEY=你的接口内容加密密钥
ALIPAY_AES_KEY=你的AES密钥
```

### 4.2 沙箱环境配置
开发测试时可以使用沙箱环境：
```env
ALIPAY_MODE=sandbox
ALIPAY_SANDBOX=true
```

### 4.3 接口内容加密配置
如果支付宝应用开启了接口内容加密，需要额外配置：

#### 4.3.1 获取加密密钥
1. 登录支付宝开放平台
2. 进入应用详情页面
3. 在"开发设置"中找到"接口内容加密方式"
4. 如果已开启，会显示接口内容加密密钥和AES密钥

#### 4.3.2 配置加密密钥
```env
# 接口内容加密密钥（从支付宝开放平台获取）
ALIPAY_APP_SECRET_KEY=你的接口内容加密密钥

# AES密钥（从支付宝开放平台获取）
ALIPAY_AES_KEY=你的AES密钥
```

#### 4.3.3 注意事项
- 接口内容加密是可选功能，如果未开启可以不配置
- 加密密钥非常重要，请妥善保管
- 开启加密后，所有接口请求和响应都会被加密

## 5. 配置回调地址

### 5.1 在支付宝开放平台配置
1. 登录支付宝开放平台
2. 进入应用详情页面
3. 配置网关地址：`https://yourdomain.com/api/payments/alipay/notify`
4. 配置授权回调地址：`https://yourdomain.com/api/payments/alipay/return`

## 6. 测试支付功能

### 6.1 开发环境测试
- 系统会自动使用模拟支付页面
- 可以测试完整的支付流程
- 无需真实的支付宝配置

### 6.2 生产环境测试
- 确保所有证书文件正确配置
- 确保回调地址可以正常访问
- 使用真实的支付宝账号进行测试

## 7. 常见问题

### 7.1 证书配置问题
- 确保证书文件路径正确
- 确保证书文件权限正确
- 确保私钥格式正确

### 7.2 回调问题
- 确保回调地址可以公网访问
- 确保回调地址返回正确的响应
- 检查签名验证是否正确

### 7.3 支付失败问题
- 检查应用配置是否正确
- 检查功能包是否已开通
- 检查商户资质是否完整

### 7.4 接口内容加密问题
- 确认是否开启了接口内容加密
- 检查加密密钥配置是否正确
- 确保AES密钥格式正确
- 如果未开启加密，不需要配置加密相关参数

## 8. 安全注意事项

### 8.1 私钥安全
- 私钥文件不要提交到版本控制系统
- 设置合适的文件权限
- 定期更换密钥

### 8.2 回调验证
- 必须验证回调签名
- 验证订单金额和状态
- 防止重复处理

## 9. 支持的支付方式

### 9.1 电脑网站支付
- 适用于PC端浏览器
- 跳转到支付宝收银台页面

### 9.2 手机网站支付
- 适用于移动端浏览器
- 自动适配移动端界面

## 10. 开发模式

### 10.1 模拟支付
在开发环境下，系统会自动使用模拟支付：
- 显示模拟支付页面
- 可以模拟支付成功/失败
- 无需真实的支付宝配置

### 10.2 真实支付
在生产环境下，使用真实的支付宝支付：
- 跳转到真实的支付宝页面
- 需要完整的支付宝配置
- 支持真实的资金流转
