<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Meeting;
use App\Models\MeetingRegistration;

class MeetingController extends Controller
{
    /**
     * 获取会议列表
     */
    public function index(Request $request)
    {
        try {
            $meetings = Meeting::where('status', 'published')->get();

            return response()->json([
                'success' => true,
                'data' => $meetings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取会议详情
     */
    public function show($id)
    {
        $meeting = Meeting::find($id);

        if (!$meeting) {
            return response()->json([
                'success' => false,
                'message' => '会议不存在'
            ], 404);
        }

        $meeting->registered_count = $meeting->registrations()->where('payment_status', 'paid')->count();
        $meeting->can_register = $meeting->canRegister();

        return response()->json([
            'success' => true,
            'data' => $meeting
        ]);
    }

    /**
     * 注册会议
     */
    public function register(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'registration_type' => 'required|in:expert,student,corporate',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $meeting = Meeting::find($id);
        if (!$meeting) {
            return response()->json([
                'success' => false,
                'message' => '会议不存在'
            ], 404);
        }

        if (!$meeting->canRegister()) {
            return response()->json([
                'success' => false,
                'message' => '会议不可注册'
            ], 400);
        }

        $user = $request->user();

        // 检查个人信息是否完整
        if (!$user->hasCompleteProfile()) {
            $missingFields = $user->getMissingProfileFields();
            return response()->json([
                'success' => false,
                'message' => '请先完善个人信息：' . implode('、', $missingFields),
                'error_code' => 'INCOMPLETE_PROFILE',
                'missing_fields' => $missingFields,
                'redirect_url' => '/profile'
            ], 400);
        }

        // 检查是否已经注册
        $existingRegistration = MeetingRegistration::where('user_id', $user->id)
            ->where('meeting_id', $meeting->id)
            ->first();

        if ($existingRegistration) {
            switch ($existingRegistration->payment_status) {
                case 'paid':
                    // 已支付，不允许重复注册
                    return response()->json([
                        'success' => false,
                        'message' => '您已经注册过此会议'
                    ], 400);

                case 'pending':
                    // 待支付，允许更新注册类型和费用
                    $newRegistrationType = $request->registration_type;
                    $newFeeAmount = $meeting->getCurrentFee($newRegistrationType);

                    // 更新现有注册记录
                    $existingRegistration->update([
                        'registration_type' => $newRegistrationType,
                        'fee_amount' => $newFeeAmount,
                        'notes' => $request->notes,
                        'registered_at' => now(), // 更新注册时间
                    ]);

                    \Log::info("用户 {$user->id} 更新会议 {$meeting->id} 的注册类型为 {$newRegistrationType}，费用: {$newFeeAmount}");

                    return response()->json([
                        'success' => true,
                        'message' => '注册信息已更新',
                        'data' => $existingRegistration->fresh()->load('meeting')
                    ], 200);

                case 'failed':
                case 'refunded':
                    // 支付失败或已退款，删除旧记录，允许重新注册
                    \Log::info("用户 {$user->id} 重新注册会议 {$meeting->id}，删除旧的{$existingRegistration->payment_status}记录");
                    $existingRegistration->delete();
                    break;
            }
        }

        // 计算费用
        $registrationType = $request->registration_type;
        $feeAmount = $meeting->getCurrentFee($registrationType);

        // 创建注册记录
        $registration = MeetingRegistration::create([
            'user_id' => $user->id,
            'meeting_id' => $meeting->id,
            'registration_type' => $registrationType,
            'fee_amount' => $feeAmount,
            'payment_status' => 'pending',
            'registered_at' => now(),
            'notes' => $request->notes,
        ]);

        \Log::info("用户 {$user->id} 成功注册会议 {$meeting->id}，注册类型: {$registrationType}，费用: {$feeAmount}");

        return response()->json([
            'success' => true,
            'message' => '会议注册成功',
            'data' => $registration->load('meeting')
        ], 201);
    }

    /**
     * 获取我的会议注册
     */
    public function myRegistrations(Request $request)
    {
        $user = $request->user();

        $registrations = MeetingRegistration::where('user_id', $user->id)
            ->with(['meeting', 'latestPayment'])
            ->orderBy('registered_at', 'desc')
            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $registrations
        ]);
    }
}
