#!/bin/bash

# 前端部署脚本
# 使用方法: ./deploy.sh [环境] [服务器地址]
# 例如: ./deploy.<NAME_EMAIL>

set -e

# 默认参数
ENVIRONMENT=${1:-production}
SERVER=${2:-""}
REMOTE_PATH="/var/www/html"
BACKUP_PATH="/var/www/html.backup.$(date +%Y%m%d_%H%M%S)"

echo "🚀 开始部署前端项目..."
echo "环境: $ENVIRONMENT"

# 1. 清理旧的构建文件
echo "📦 清理旧的构建文件..."
rm -rf dist

# 2. 安装依赖
echo "📦 安装依赖..."
npm ci --only=production

# 3. 构建项目
echo "🔨 构建项目..."
if [ "$ENVIRONMENT" = "production" ]; then
    npm run build:prod
else
    npm run build
fi

# 4. 检查构建结果
if [ ! -d "dist" ]; then
    echo "❌ 构建失败，dist目录不存在"
    exit 1
fi

echo "✅ 构建完成"

# 5. 如果提供了服务器地址，则部署到服务器
if [ -n "$SERVER" ]; then
    echo "🚀 部署到服务器: $SERVER"
    
    # 备份现有文件
    echo "📦 备份现有文件..."
    ssh $SERVER "if [ -d $REMOTE_PATH ]; then cp -r $REMOTE_PATH $BACKUP_PATH; fi"
    
    # 上传新文件
    echo "📤 上传新文件..."
    rsync -avz --delete dist/ $SERVER:$REMOTE_PATH/
    
    # 设置权限
    echo "🔧 设置文件权限..."
    ssh $SERVER "chown -R www-data:www-data $REMOTE_PATH && chmod -R 755 $REMOTE_PATH"
    
    # 重启nginx (可选)
    echo "🔄 重启nginx..."
    ssh $SERVER "sudo systemctl reload nginx"
    
    echo "✅ 部署完成!"
    echo "🌐 网站地址: http://$SERVER"
    echo "📦 备份位置: $BACKUP_PATH"
else
    echo "✅ 构建完成，静态文件位于 dist/ 目录"
    echo "📁 可以将 dist/ 目录内容上传到你的web服务器"
fi

echo "🎉 部署流程完成!"
