<template>
  <div class="submission-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑投稿' : '新建投稿' }}</h2>
      <el-button @click="goBack">返回列表</el-button>
    </div>

    <el-card>
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="120px"
        v-loading="loading"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h3>基本信息</h3>
          
          <el-form-item label="论文标题" prop="title">
            <el-input 
              v-model="form.title" 
              placeholder="请输入论文标题"
              maxlength="255"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="论文摘要" prop="abstract">
            <el-input 
              v-model="form.abstract" 
              type="textarea" 
              :rows="6"
              placeholder="请输入论文摘要"
              maxlength="2000"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="会议主题" prop="meeting_theme">
            <el-select v-model="form.meeting_theme" placeholder="请选择会议主题">
              <el-option label="1. 成像光谱机理与前沿技术" value="1. 成像光谱机理与前沿技术" />
              <el-option label="2. 成像光谱仪器与系统技术" value="2. 成像光谱仪器与系统技术" />
              <el-option label="3.成像光谱与智能处理技术（I）" value="3.成像光谱与智能处理技术（I）" />
              <el-option label="4.成像光谱与智能处理技术（II）" value="4.成像光谱与智能处理技术（II）" />
              <el-option label="5.成像光谱与深空探测技术" value="5.成像光谱与深空探测技术" />
              <el-option label="6.成像光谱与生态环境监测" value="6.成像光谱与生态环境监测" />
              <el-option label="7.成像光谱与资源能源调查" value="7.成像光谱与资源能源调查" />
              <el-option label="8.成像光谱与农林田草监测" value="8.成像光谱与农林田草监测" />
              <el-option label="9.成像光谱与交叉学科发展" value="9.成像光谱与交叉学科发展" />
              <el-option label="10.青年论坛" value="10.青年论坛" />
            </el-select>
          </el-form-item>

          <el-form-item label="投稿类型" prop="submission_type">
            <el-radio-group v-model="form.submission_type">
              <el-radio value="oral">口头报告</el-radio>
              <el-radio value="poster">壁报展示</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="研究领域" prop="research_field">
            <el-input 
              v-model="form.research_field" 
              placeholder="请输入研究领域"
              maxlength="255"
            />
          </el-form-item>

          <el-form-item label="关键词" prop="keywords" required>
            <el-tag
              v-for="(keyword, index) in form.keywords"
              :key="index"
              closable
              @close="removeKeyword(index)"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ keyword }}
            </el-tag>
            <el-input
              v-if="keywordInputVisible"
              ref="keywordInputRef"
              v-model="keywordInputValue"
              size="small"
              style="width: 120px;"
              @keyup.enter="handleKeywordInputConfirm"
              @blur="handleKeywordInputConfirm"
            />
            <el-button v-else size="small" @click="showKeywordInput">
              + 添加关键词
            </el-button>
          </el-form-item>
        </div>

        <!-- 作者信息 -->
        <div class="form-section">
          <h3>作者信息</h3>
          
          <div v-for="(author, index) in form.authors" :key="index" class="author-item">
            <div class="author-header">
              <span>作者 {{ index + 1 }}</span>
              <el-button 
                v-if="form.authors.length > 1" 
                size="small" 
                type="danger" 
                text
                @click="removeAuthor(index)"
              >
                删除
              </el-button>
            </div>
            
            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item 
                  :prop="`authors.${index}.name`" 
                  :rules="{ required: true, message: '请输入作者姓名', trigger: 'blur' }"
                >
                  <el-input v-model="author.name" placeholder="作者姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item 
                  :prop="`authors.${index}.institution`" 
                  :rules="{ required: true, message: '请输入所属机构', trigger: 'blur' }"
                >
                  <el-input v-model="author.institution" placeholder="所属机构" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :prop="`authors.${index}.email`">
                  <el-input v-model="author.email" placeholder="邮箱（可选）" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <el-button @click="addAuthor" type="primary" plain>
            <el-icon><Plus /></el-icon>
            添加作者
          </el-button>
        </div>

        <!-- 通讯作者信息 -->
        <div class="form-section">
          <h3>通讯作者信息</h3>
          
          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item label="通讯作者" prop="corresponding_author">
                <el-input v-model="form.corresponding_author" placeholder="请输入通讯作者姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="通讯邮箱" prop="corresponding_email">
                <el-input v-model="form.corresponding_email" placeholder="请输入通讯作者邮箱" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系电话" prop="corresponding_phone">
                <el-input v-model="form.corresponding_phone" placeholder="请输入联系电话" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 机构信息 -->
        <div class="form-section">
          <h3>机构信息</h3>
          
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="所属机构" prop="institution">
                <el-input v-model="form.institution" placeholder="请输入所属机构" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="科室/部门" prop="department">
                <el-input v-model="form.department" placeholder="请输入科室或部门（可选）" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 其他信息 -->
        <div class="form-section">
          <h3>其他信息</h3>
          
          <el-form-item label="基金资助" prop="funding_info">
            <el-input 
              v-model="form.funding_info" 
              type="textarea" 
              :rows="3"
              placeholder="请输入基金资助信息（可选）"
            />
          </el-form-item>

          <el-form-item label="论文文件" prop="file">
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :limit="1"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :file-list="fileList"
              accept=".pdf,.doc,.docx"
            >
              <el-button type="primary">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传 PDF、DOC、DOCX 文件，且不超过 10MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" @click="handleSave" :loading="saving">
            {{ isEdit ? '更新' : '保存' }}
          </el-button>
          <el-button 
            v-if="!isEdit || (submissionData && submissionData.status === 'draft')"
            type="success" 
            @click="handleSaveAndSubmit" 
            :loading="saving"
          >
            保存并提交
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import {
  createSubmission,
  updateSubmission,
  getSubmissionDetail,
  submitSubmission
} from '@/api/submission'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const formRef = ref(null)
const uploadRef = ref(null)
const keywordInputRef = ref(null)

const loading = ref(false)
const saving = ref(false)
const keywordInputVisible = ref(false)
const keywordInputValue = ref('')
const fileList = ref([])
const submissionData = ref(null)

// 判断是否为编辑模式
const isEdit = computed(() => route.name === 'SubmissionEdit')

// 表单数据
const form = reactive({
  title: '',
  abstract: '',
  authors: [{ name: '', institution: '', email: '' }],
  corresponding_author: '',
  corresponding_email: '',
  corresponding_phone: '',
  institution: '',
  department: '',
  keywords: [],
  submission_type: 'oral',
  research_field: '',
  funding_info: '',
  file: null,
  meeting_theme:''
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入论文标题', trigger: 'blur' }
  ],
  abstract: [
    { required: true, message: '请输入论文摘要', trigger: 'blur' }
  ],
  corresponding_author: [
    { required: true, message: '请输入通讯作者', trigger: 'blur' }
  ],
  corresponding_email: [
    { required: true, message: '请输入通讯邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  corresponding_phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  institution: [
    { required: true, message: '请输入所属机构', trigger: 'blur' }
  ],
  submission_type: [
    { required: true, message: '请选择投稿类型', trigger: 'change' }
  ],
  research_field: [
    { required: true, message: '请输入研究领域', trigger: 'blur' }
  ],
  keywords: [
    {
      type: 'array',
      min: 1,
      message: '请至少添加一个关键词',
      trigger: 'change'
    }
  ],
  meeting_theme: [
    { required: true, message: '请选择会议主题', trigger: 'change' }
  ]
}

// 检查付费用户权限
const checkPaidUser = () => {
  if (!userStore.isPaidUser) {
    ElMessage.warning('投稿功能仅限付费用户使用，请先完成会议注册并支付费用')
    router.push('/meeting-registration')
    return false
  }
  return true
}

// 加载投稿详情（编辑模式）
const loadSubmissionDetail = async () => {
  if (!checkPaidUser()) return
  if (!isEdit.value) return

  loading.value = true
  try {
    const response = await getSubmissionDetail(route.params.id)
    if (response.success) {
      submissionData.value = response.data
      
      // 填充表单数据
      Object.assign(form, {
        title: response.data.title,
        abstract: response.data.abstract,
        authors: response.data.authors || [{ name: '', institution: '', email: '' }],
        corresponding_author: response.data.corresponding_author,
        corresponding_email: response.data.corresponding_email,
        corresponding_phone: response.data.corresponding_phone,
        institution: response.data.institution,
        department: response.data.department || '',
        keywords: response.data.keywords || [],
        submission_type: response.data.submission_type,
        research_field: response.data.research_field,
        funding_info: response.data.funding_info || ''
      })

      // 如果有文件，显示在文件列表中
      if (response.data.original_filename) {
        fileList.value = [{
          name: response.data.original_filename,
          url: response.data.file_url
        }]
      }
    }
  } catch (error) {
    console.error('获取投稿详情失败:', error)
    ElMessage.error('获取投稿详情失败')
    goBack()
  } finally {
    loading.value = false
  }
}

// 添加作者
const addAuthor = () => {
  form.authors.push({ name: '', institution: '', email: '' })
}

// 删除作者
const removeAuthor = (index) => {
  form.authors.splice(index, 1)
}

// 显示关键词输入框
const showKeywordInput = () => {
  keywordInputVisible.value = true
  nextTick(() => {
    keywordInputRef.value?.focus()
  })
}

// 确认添加关键词
const handleKeywordInputConfirm = () => {
  if (keywordInputValue.value && !form.keywords.includes(keywordInputValue.value)) {
    form.keywords.push(keywordInputValue.value)
  }
  keywordInputVisible.value = false
  keywordInputValue.value = ''
}

// 删除关键词
const removeKeyword = (index) => {
  form.keywords.splice(index, 1)
}

// 处理文件选择
const handleFileChange = (file) => {
  form.file = file.raw
}

// 处理文件删除
const handleFileRemove = () => {
  form.file = null
}

// 保存投稿
const handleSave = async () => {
  if (!checkPaidUser()) return

  try {
    await formRef.value.validate()
    
    saving.value = true
    
    const formData = new FormData()
    
    // 添加表单数据
    Object.keys(form).forEach(key => {
      if (key === 'authors' || key === 'keywords') {
        formData.append(key, JSON.stringify(form[key]))
      } else if (key === 'file' && form[key]) {
        formData.append(key, form[key])
      } else if (form[key] !== null && form[key] !== undefined) {
        formData.append(key, form[key])
      }
    })

    let response
    if (isEdit.value) {
      response = await updateSubmission(route.params.id, formData)
    } else {
      response = await createSubmission(formData)
    }

    if (response.success) {
      ElMessage.success(isEdit.value ? '投稿更新成功' : '投稿创建成功')
      goBack()
    }
  } catch (error) {
    if (error !== 'validation failed') {
      console.error('保存投稿失败:', error)
      ElMessage.error('保存投稿失败')
    }
  } finally {
    saving.value = false
  }
}

// 保存并提交投稿
const handleSaveAndSubmit = async () => {
  try {
    await handleSave()
    
    // 如果是新建，需要获取创建后的ID
    if (!isEdit.value) {
      // 这里需要从保存响应中获取ID，或者重新设计流程
      ElMessage.info('请在投稿列表中提交您的投稿')
      return
    }

    // 提交投稿
    const submitResponse = await submitSubmission(route.params.id)
    if (submitResponse.success) {
      ElMessage.success('投稿提交成功')
    }
  } catch (error) {
    console.error('保存并提交失败:', error)
  }
}

// 返回列表
const goBack = () => {
  router.push('/submissions')
}

onMounted(() => {
  loadSubmissionDetail()
})
</script>

<style scoped>
.submission-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.form-section:last-of-type {
  border-bottom: none;
}

.form-section h3 {
  margin: 0 0 20px 0;
  color: #409eff;
  font-size: 16px;
  font-weight: 600;
}

.author-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.author-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: 600;
  color: #606266;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 10px;
}
</style>
