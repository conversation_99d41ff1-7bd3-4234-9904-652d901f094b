#!/usr/bin/env node

/**
 * 部署检查脚本
 * 检查构建产物和配置是否正确
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DIST_DIR = path.join(__dirname, '..', 'dist');
const ENV_FILE = path.join(__dirname, '..', '.env.production');

console.log('🔍 检查部署配置...\n');

let hasErrors = false;

// 检查构建目录
function checkBuildDir() {
    console.log('📁 检查构建目录...');
    
    if (!fs.existsSync(DIST_DIR)) {
        console.log('❌ dist目录不存在，请先运行构建命令');
        hasErrors = true;
        return;
    }
    
    const indexFile = path.join(DIST_DIR, 'index.html');
    if (!fs.existsSync(indexFile)) {
        console.log('❌ index.html文件不存在');
        hasErrors = true;
        return;
    }
    
    const assetsDir = path.join(DIST_DIR, 'assets');
    if (!fs.existsSync(assetsDir)) {
        console.log('❌ assets目录不存在');
        hasErrors = true;
        return;
    }
    
    console.log('✅ 构建目录检查通过');
}

// 检查环境配置
function checkEnvConfig() {
    console.log('\n🔧 检查环境配置...');
    
    if (!fs.existsSync(ENV_FILE)) {
        console.log('⚠️  .env.production文件不存在，将使用默认配置');
        return;
    }
    
    const envContent = fs.readFileSync(ENV_FILE, 'utf8');
    
    // 检查API地址
    if (envContent.includes('localhost') || envContent.includes('127.0.0.1')) {
        console.log('⚠️  API地址仍然指向本地，请修改为生产环境地址');
    }
    
    // 检查必要的配置项
    const requiredVars = ['VITE_API_BASE_URL'];
    for (const varName of requiredVars) {
        if (!envContent.includes(varName)) {
            console.log(`⚠️  缺少环境变量: ${varName}`);
        }
    }
    
    console.log('✅ 环境配置检查完成');
}

// 检查文件大小
function checkFileSize() {
    console.log('\n📊 检查文件大小...');
    
    const assetsDir = path.join(DIST_DIR, 'assets');
    if (!fs.existsSync(assetsDir)) return;
    
    const jsDir = path.join(assetsDir, 'js');
    const cssDir = path.join(assetsDir, 'css');
    
    let totalSize = 0;
    let largeFiles = [];
    
    function checkDir(dir, type) {
        if (!fs.existsSync(dir)) return;
        
        const files = fs.readdirSync(dir);
        for (const file of files) {
            const filePath = path.join(dir, file);
            const stats = fs.statSync(filePath);
            const sizeKB = Math.round(stats.size / 1024);
            totalSize += stats.size;
            
            if (sizeKB > 500) {
                largeFiles.push({ file, size: sizeKB, type });
            }
        }
    }
    
    checkDir(jsDir, 'JS');
    checkDir(cssDir, 'CSS');
    
    console.log(`📦 总文件大小: ${Math.round(totalSize / 1024)} KB`);
    
    if (largeFiles.length > 0) {
        console.log('\n⚠️  发现较大的文件:');
        for (const { file, size, type } of largeFiles) {
            console.log(`   ${type}: ${file} (${size} KB)`);
        }
        console.log('   建议检查是否可以进一步优化');
    }
    
    console.log('✅ 文件大小检查完成');
}

// 生成部署报告
function generateReport() {
    console.log('\n📋 部署报告:');
    console.log('=====================================');
    
    if (hasErrors) {
        console.log('❌ 发现错误，请修复后重新构建');
        process.exit(1);
    } else {
        console.log('✅ 所有检查通过，可以进行部署');
        
        console.log('\n📤 部署步骤:');
        console.log('1. 将 dist/ 目录内容上传到web服务器');
        console.log('2. 配置web服务器支持SPA路由');
        console.log('3. 确保API服务器正常运行');
        console.log('4. 测试主要功能页面');
        
        console.log('\n🔗 相关文件:');
        console.log('- Nginx配置: deploy/nginx.conf');
        console.log('- Docker配置: Dockerfile');
        console.log('- 部署文档: DEPLOY.md');
    }
}

// 执行检查
checkBuildDir();
checkEnvConfig();
checkFileSize();
generateReport();
