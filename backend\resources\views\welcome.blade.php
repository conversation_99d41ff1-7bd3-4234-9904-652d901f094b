<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第九届全国成像光谱对地观测学术研讨会</title>
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/css/all.min.css">
    <style>
        :root {
            --dark-blue-1: #0a1f3d;
            --dark-blue-2: #1a3b6d;
            --light-blue: #a8d0ff;
            --icon-bg: rgba(168, 208, 255, 0.15);
        }
        body {
           	background: url(./images/bg.jpg) ;
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .header-section {
            padding: 3rem 0 1.5rem;
            text-align: center;
            flex: 0 0 auto;
        }
        .header-section h1 {
            font-weight: 700;
            margin-bottom: 0.8rem;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .header-section .subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            letter-spacing: 0.5px;
        }
        .location-marquee {
            background: rgba(0,0,0,0.3);
            padding: 0.8rem 0;
            margin: 1rem auto;
            border-radius: 8px;
            max-width: 900px;
            overflow: hidden;
            position: relative;
            border: 1px solid rgba(255,255,255,0.1);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .location-marquee::before,
        .location-marquee::after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 50px;
            z-index: 2;
            pointer-events: none;
        }

        .location-marquee::before {
            left: 0;
            background: linear-gradient(to right, rgba(0,0,0,0.3), transparent);
        }

        .location-marquee::after {
            right: 0;
            background: linear-gradient(to left, rgba(0,0,0,0.3), transparent);
        }

        .location-content {
            display: flex;
            white-space: nowrap;
            animation: marquee 30s linear infinite;
            font-size: 0.95rem;
            line-height: 1.4;
        }

        .location-content:hover {
            animation-play-state: paused;
        }

        .marquee-text {
            display: inline-block;
            padding: 0 2rem;
            flex-shrink: 0;
        }

        @keyframes marquee {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-50%);
            }
        }

        .conference-info {
            max-width: 1000px;
            margin: 2rem auto 0;
            padding: 0 1rem;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .info-card {
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid rgba(255,255,255,0.1);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .info-card h3 {
            color: var(--light-blue);
            font-size: 1.1rem;
            margin-bottom: 1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .info-card ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .info-card li {
            padding: 0.3rem 0;
            font-size: 0.9rem;
            line-height: 1.4;
            opacity: 0.9;
        }

        .info-card li:before {
            content: "•";
            color: var(--light-blue);
            margin-right: 0.5rem;
        }

        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .conference-info {
                margin-top: 1.5rem;
            }

            .info-card {
                padding: 1rem;
            }

            .info-card h3 {
                font-size: 1rem;
            }

            .info-card li {
                font-size: 0.85rem;
            }
        }

        .icon-section {
            flex: 1 0 auto;
            padding: 1rem 0;
        }
        .icon-grid {
            max-width: 1000px;
            margin: 0 auto;
        }
        .icon-item {
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        .icon-wrapper {
            width: 90px;
            height: 90px;
            margin: 0 auto 0.8rem;
            background: var(--icon-bg);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(255,255,255,0.1);
            transition: all 0.3s ease;
        }
        .icon-wrapper i {
            font-size: 2.2rem;
            color: var(--light-blue);
        }
        .icon-item:hover .icon-wrapper {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            background: rgba(168, 208, 255, 0.25);
        }
        .icon-label {
            font-size: 0.95rem;
            font-weight: 500;
        }
        .conference-info-section {
            flex: 0 0 auto;
            padding: 2rem 0 1rem;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .conference-info-section .conference-info {
            margin-top: 0;
        }

        .footer-section {
            flex: 0 0 auto;
            padding: 1.5rem 0;
            text-align: center;
            color: rgba(255,255,255,0.7);
            font-size: 0.9rem;
            border-top: 1px solid rgba(255,255,255,0.1);
        }
        @media (max-width: 767px) {
            .icon-item {
                width: 33.33%;
                flex: 0 0 33.33%;
                max-width: 33.33%;
            }
            .icon-wrapper {
                width: 80px;
                height: 80px;
            }
            .icon-wrapper i {
                font-size: 2rem;
            }
        }
        .content-text strong{
            color:while;
        }
    </style>
</head>
<body>
    <section class="header-section">
        <div class="container">
            <h1>第九届光谱成像研讨会</h1>
            <p class="subtitle">2025年10月24日-27日</p>
            <div class="location-marquee">
                <div class="location-content">
                    <span class="marquee-text">
                        <i class="fas fa-map-marker-alt"></i>
                        第九届全国成像光谱对地观测学术研讨会 （第十五届成像光谱技术与应用研讨会暨交叉学科论坛）
                    </span>
                    <span class="marquee-text">
                        <i class="fas fa-map-marker-alt"></i>
                        第九届全国成像光谱对地观测学术研讨会 （第十五届成像光谱技术与应用研讨会暨交叉学科论坛）
                    </span>
                </div>
            </div>
        </div>
    </section>

    <section class="icon-section">
        <div class="container">
            <div class="row icon-grid">
                <div class="col-md-3 col-sm-4 col-4 icon-item">
                    <div class="icon-wrapper"><i class="fas fa-door-open"></i></div>
                    <div class="icon-label" href="{{ url('announcements', ['id' => 1]) }}">欢迎词</div>
                </div>
                <div class="col-md-3 col-sm-4 col-4 icon-item">
                    <div class="icon-wrapper"><i class="fas fa-info-circle"></i></div>
                    <div class="icon-label" href="{{ url('announcements', ['id' => 2]) }}">会议信息</div>
                </div>
                <div class="col-md-3 col-sm-4 col-4 icon-item">
                    <div class="icon-wrapper"><i class="fas fa-users"></i></div>
                    <div class="icon-label" href="{{ url('announcements', ['id' => 3]) }}">会议组织</div>
                </div>
                <div class="col-md-3 col-sm-4 col-4 icon-item">
                    <div class="icon-wrapper"><i class="fas fa-money-bill-wave"></i></div>
                    <div class="icon-label" href="{{ url('announcements', ['id' => 4]) }}">注册缴费</div>
                </div>
                <div class="col-md-3 col-sm-4 col-4 icon-item">
                    <div class="icon-wrapper"><i class="fas fa-file-upload"></i></div>
                    <div class="icon-label" href="https://metting.whlxyc.cn/h5">摘要投稿</div>
                </div>
                <div class="col-md-3 col-sm-4 col-4 icon-item">
                    <div class="icon-wrapper"><i class="fas fa-calendar-alt"></i></div>
                    <div class="icon-label" href="{{ url('announcements', ['id' => 5]) }}">大会议程</div>
                </div>
                <div class="col-md-3 col-sm-4 col-4 icon-item">
                    <div class="icon-wrapper"><i class="fas fa-hotel"></i></div>
                    <div class="icon-label" href="{{ url('announcements', ['id' => 8]) }}">酒店预定</div>
                </div>
                <div class="col-md-3 col-sm-4 col-4 icon-item">
                    <div class="icon-wrapper"><i class="fas fa-comments"></i></div>
                    <div class="icon-label"  href="{{ url('announcements', ['id' => 9]) }}">会议交流群</div>
                </div>
                <div class="col-md-3 col-sm-4 col-4 icon-item">
                    <div class="icon-wrapper"><i class="fas fa-envelope"></i></div>
                    <div class="icon-label" href="{{ url('announcements', ['id' => 6]) }}">联系我们</div>
                </div>
                <div class="col-md-3 col-sm-4 col-4 icon-item">
                    <div class="icon-wrapper"><i class="fas fa-map-marked-alt"></i></div>
                    <div class="icon-label" href="{{ url('announcements', ['id' => 7]) }}">会场交通</div>
                </div>
                <div class="col-md-3 col-sm-4 col-4 icon-item">
                    <div class="icon-wrapper"><i class="fas fa-file-download"></i></div>
                    <div class="icon-label" href="https://metting.whlxyc.cn/h5">资料下载</div>
                </div>
                <div class="col-md-3 col-sm-4 col-4 icon-item">
                    <div class="icon-wrapper"><i class="fas fa-user-circle"></i></div>
                    <div class="icon-label" href="https://metting.whlxyc.cn/h5">个人中心</div>
                </div>
            </div>
        </div>
    </section>

    <section class="conference-info-section">
        <!--<div class="container">
            <div class="conference-info">
                <div class="info-grid">
                    <div class="info-card">
                        <h3><i class="fas fa-users"></i> 主办单位</h3>
                        <ul>
                            <li>中国空间科学学会空间遥感专业委员会</li>
                            <li>中国遥感应用协会高光谱遥感技术与应用专业委员会</li>
                            <li>国际数字地球学会中国国家委员会成像光谱对地观测专业委员会</li>
                            <li>中国遥感委员会</li>
                        </ul>
                    </div>

                    <div class="info-card">
                        <h3><i class="fas fa-building"></i> 承办单位</h3>
                        <ul>
                            <li>中国地质大学（武汉）</li>
                            <li>教育部自然资源信息管理与数字孪生工程软件工程研究中心</li>
                            <li>区域生态过程与环境演变湖北省重点实验室</li>
                            <li>科技部国家遥感中心地壳运动与深空探测部</li>
                        </ul>
                    </div>

                    <div class="info-card">
                        <h3><i class="fas fa-handshake"></i> 协办单位</h3>
                        <ul>
                            <li>中国科学院上海技术物理研究所</li>
                            <li>中国科学院空天信息创新研究院</li>
                            <li>中国科学院空间主动光电技术重点实验室</li>
                            <li>中国科学院计算光学成像技术重点实验室</li>
                            <li>上海市光谱信息感知工程研究中心</li>
                            <li>武汉大学测绘遥感信息工程全国重点实验室</li>
                            <li>武汉大学遥感信息工程学院</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>-->
    </section>

    <section class="footer-section">
        <div class="container">
            <p>&copy; 2025 第九届光谱成像研讨会组委会 版权所有</p>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.querySelectorAll('.icon-item').forEach(item => {
            item.addEventListener('click', function() {
                // console.log(this.querySelector('.icon-label').attributes.href.nodeValue)
                // const title = this.querySelector('.icon-label').textContent;
                // alert(`即将跳转到${title}页面`);
                window.location.href = this.querySelector('.icon-label').attributes.href.nodeValue
            });
        });
    </script>
</body>
</html>
