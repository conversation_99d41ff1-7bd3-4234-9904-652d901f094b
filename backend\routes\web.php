<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AnnouncementController;
use \Illuminate\Support\Facades\Cache;

Route::get('/', function () {
    return view('welcome');
});

// 模拟支付宝支付页面（开发环境）
Route::get('/mock-alipay-payment', function (Illuminate\Http\Request $request) {
    if (env('APP_ENV') !== 'local') {
        abort(404);
    }

    $params = $request->all();
    return view('mock-alipay-payment', compact('params'));
});

// 支付宝支付页面（生产环境）
Route::get('/alipay-payment/{token}', function ($token) {
    $htmlContent = Cache::get('alipay_payment_' . $token);

    if (!$htmlContent) {
        return response('支付链接已过期或无效', 404);
    }

    // 删除缓存，确保链接只能使用一次
    cache()->forget('alipay_payment_' . $token);

    return response($htmlContent)->header('Content-Type', 'text/html; charset=utf-8');
});

// 公告相关路由
Route::resource('announcements', AnnouncementController::class);

// 附件下载路由
Route::get('announcements/{id}/attachment/{filename}', [AnnouncementController::class, 'downloadAttachment'])
    ->name('announcements.download-attachment');

// 测试路由
Route::get('/test-announcement', function () {
    try {
        // 创建测试数据
        $announcement = new \App\Models\Announcement();
        $announcement->id = 1;
        $announcement->title = '测试公告标题';
        $announcement->content = '这是一个测试公告的内容，用于验证模板渲染是否正常工作。';
        $announcement->user_id = 1;
        $announcement->attachments = [
            [
                'name' => '测试文件.pdf',
                'path' => 'announcements/attachments/test.pdf',
                'url' => '/storage/announcements/attachments/test.pdf',
                'size' => 1024000,
                'type' => 'application/pdf'
            ]
        ];
        $announcement->created_at = now();
        $announcement->updated_at = now();

        // 模拟发布者
        $publisher = new \App\Models\User();
        $publisher->username = '系统管理员';
        $publisher->name = '管理员';
        $publisher->email = '<EMAIL>';

        return view('announcement-detail', compact('announcement', 'publisher'));
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ], 500);
    }
});
