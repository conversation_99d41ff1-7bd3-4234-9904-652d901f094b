<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meeting_registrations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade')->comment('用户ID');
            $table->foreignId('meeting_id')->constrained()->onDelete('cascade')->comment('会议ID');
            $table->enum('registration_type', ['expert', 'student'])->comment('注册类型');
            $table->decimal('fee_amount', 8, 2)->comment('费用金额');
            $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded'])->default('pending')->comment('支付状态');
            $table->datetime('registered_at')->comment('注册时间');
            $table->text('notes')->nullable()->comment('备注');
            $table->timestamps();

            $table->unique(['user_id', 'meeting_id'], 'unique_user_meeting');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meeting_registrations');
    }
};
