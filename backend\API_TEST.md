# 会议管理系统 API 测试文档

## 服务器信息
- 后端服务器：http://localhost:8000
- 前端服务器：http://localhost:3000 (需要启动)

## 基础测试

### 1. 测试API连通性
```bash
curl -X GET "http://localhost:8000/api/test"
```
预期响应：
```json
{"message":"API is working!"}
```

### 2. 测试会议数据
```bash
curl -X GET "http://localhost:8000/api/test-meetings"
```

## 用户认证API

### 1. 用户注册
```bash
curl -X POST "http://localhost:8000/api/register" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试用户",
    "email": "<EMAIL>",
    "password": "123456",
    "password_confirmation": "123456",
    "real_name": "张三",
    "phone": "13800138000",
    "member_type": "expert"
  }'
```

### 2. 用户登录
```bash
curl -X POST "http://localhost:8000/api/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "123456"
  }'
```

### 3. 获取用户信息（需要token）
```bash
curl -X GET "http://localhost:8000/api/me" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 微信登录API

### 1. 获取微信扫码二维码
```bash
curl -X GET "http://localhost:8000/api/wechat/qrcode"
```

### 2. 检查扫码登录状态
```bash
curl -X GET "http://localhost:8000/api/wechat/login-status?scene_str=SCENE_STRING"
```

## 会议管理API

### 1. 获取会议列表
```bash
curl -X GET "http://localhost:8000/api/meetings-simple"
```

### 2. 获取会议详情
```bash
curl -X GET "http://localhost:8000/api/meetings/1"
```

## 用户管理API（需要认证）

### 1. 更新个人信息
```bash
curl -X PUT "http://localhost:8000/api/user/profile" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "real_name": "李四",
    "phone": "13900139000"
  }'
```

### 2. 修改密码
```bash
curl -X POST "http://localhost:8000/api/user/change-password" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "old_password": "123456",
    "new_password": "654321",
    "new_password_confirmation": "654321"
  }'
```

### 3. 发送短信验证码
```bash
curl -X POST "http://localhost:8000/api/user/send-sms" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138000"
  }'
```

### 4. 绑定手机号
```bash
curl -X POST "http://localhost:8000/api/user/bind-phone" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138000",
    "code": "123456"
  }'
```

## 数据库配置
- 主机：rm-bp1288c4giu5xojmm7o.mysql.rds.aliyuncs.com
- 端口：3306
- 数据库：ai_test
- 用户名：ai_test
- 密码：Lb881127

## 微信公众号配置
- APPID：wx899ffb8f21781145
- SECRET：5afc16e76a4c1eac87fcc6d468a9059c
- TOKEN：W5utUPj43Nz2
- AES_KEY：cVrcj31dbIfZfNSJElMbQXI4Sea8iOZDshmUiWSgYdh

## 微信支付API（需要认证）

### 1. 创建微信Native支付订单（扫码支付）
```bash
curl -X POST "http://localhost:8000/api/payments/create" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "meeting_registration_id": 1,
    "payment_method": "wechat_native"
  }'
```

### 2. 创建微信JSAPI支付订单（公众号支付）
```bash
curl -X POST "http://localhost:8000/api/payments/create" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "meeting_registration_id": 1,
    "payment_method": "wechat_jsapi",
    "openid": "USER_OPENID"
  }'
```

### 3. 查询支付状态
```bash
curl -X GET "http://localhost:8000/api/payments/status/1" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 4. 获取我的支付记录
```bash
curl -X GET "http://localhost:8000/api/payments/my-payments" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 5. 微信支付回调（系统内部调用）
```bash
curl -X POST "http://localhost:8000/api/payments/wechat/notify" \
  -H "Content-Type: application/json" \
  -d '{微信支付回调数据}'
```

## 微信支付配置
- 商户号：1234567890（需要替换为真实商户号）
- 应用ID：wx899ffb8f21781145
- 证书路径：storage/cert/
- 回调URL：http://localhost:8000/api/payments/wechat/notify

## 注意事项
1. 所有需要认证的API都需要在请求头中包含 `Authorization: Bearer TOKEN`
2. POST请求需要设置 `Content-Type: application/json`
3. 微信登录功能需要配置微信公众号回调URL
4. 短信验证码功能目前为模拟实现，实际使用需要集成真实短信服务
5. 微信支付需要配置真实的商户证书和密钥
6. 生产环境需要配置正确的回调URL和域名
7. 支付金额单位为元，系统会自动转换为分
