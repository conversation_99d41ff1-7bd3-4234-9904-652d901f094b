<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Services\OssService;
use App\Models\Payment;
use App\Models\MeetingRegistration;
use App\Services\WechatPayService;
use App\Services\AlipayService;

class PaymentController extends Controller
{
    private $wechatPayService;
    private $alipayService;

    public function __construct()
    {
        // 延迟初始化支付服务，只在需要时创建
    }

    /**
     * 获取微信支付服务实例
     */
    private function getWechatPayService()
    {
        if (!$this->wechatPayService) {
            $this->wechatPayService = app(WechatPayService::class);
        }
        return $this->wechatPayService;
    }

    /**
     * 获取支付宝支付服务实例
     */
    private function getAlipayService()
    {
        if (!$this->alipayService) {
            $this->alipayService = app(AlipayService::class);
        }
        return $this->alipayService;
    }

    /**
     * 创建支付订单
     */
    public function create(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'meeting_registration_id' => 'required|exists:meeting_registrations,id',
            'payment_method' => 'required|in:wechat_native,wechat_jsapi,alipay_web,alipay_wap,offline_transfer',
            'openid' => 'required_if:payment_method,wechat_jsapi|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $registrationId = $request->meeting_registration_id;

        // 验证注册记录是否属于当前用户
        $registration = MeetingRegistration::where('id', $registrationId)
            ->where('user_id', $user->id)
            ->first();

        if (!$registration) {
            return response()->json([
                'success' => false,
                'message' => '注册记录不存在或无权限'
            ], 404);
        }

        if ($registration->payment_status === 'paid') {
            return response()->json([
                'success' => false,
                'message' => '该注册已支付'
            ], 400);
        }

        /*
        // 检查是否已有待支付的订单
        $existingPayment = Payment::where('meeting_registration_id', $registrationId)
            ->where('status', 'pending')
            ->first();

        if ($existingPayment) {
            // 如果支付方式相同，直接返回现有订单
            if ($existingPayment->payment_method === $request->payment_method) {
                return response()->json([
                    'success' => true,
                    'message' => '支付订单已存在',
                    'data' => $existingPayment
                ]);
            } else {
                // 如果支付方式不同，取消现有订单，创建新订单
                $existingPayment->update([
                    'status' => 'cancelled',
                    'failure_reason' => '用户切换支付方式'
                ]);
                Log::info("取消现有支付订单: {$existingPayment->transaction_id}, 原因: 用户切换支付方式从 {$existingPayment->payment_method} 到 {$request->payment_method}");
            }
        }*/

        // 生成商户订单号
        $outTradeNo = 'MEETING_' . date('YmdHis') . '_' . Str::upper(Str::random(8));

        // 创建支付订单
        $payment = Payment::create([
            'user_id' => $user->id,
            'meeting_registration_id' => $registrationId,
            'payment_method' => $request->payment_method,
            'transaction_id' => $outTradeNo,
            'amount' => $registration->fee_amount,
            'status' => 'pending',
        ]);

        // 根据支付方式处理订单
        if ($request->payment_method === 'offline_transfer') {
            // 线下对公转账，设置为待审核状态
            $payment->update([
                'review_status' => 'pending_review',
                'payment_data' => [
                    'payment_type' => 'offline_transfer',
                    'bank_info' => config('payment.bank_account')
                ]
            ]);

            return response()->json([
                'success' => true,
                'message' => '线下支付订单创建成功，请按照提供的账户信息完成转账并上传凭证',
                'data' => $payment->fresh()
            ], 201);
        } elseif (str_starts_with($request->payment_method, 'alipay')) {
            // 调用支付宝支付接口
            $paymentResult = $this->createAlipayOrder($payment, $registration, $request);

            if (!$paymentResult['success']) {
                $payment->update([
                    'status' => 'failed',
                    'failure_reason' => $paymentResult['message']
                ]);

                return response()->json([
                    'success' => false,
                    'message' => $paymentResult['message']
                ], 400);
            }

            $payment->update(['payment_data' => $paymentResult['data']]);

            return response()->json([
                'success' => true,
                'message' => '支付订单创建成功',
                'data' => $payment->fresh()
            ], 201);
        } else {
            // 调用微信支付接口
            $paymentResult = $this->createWechatPayOrder($payment, $registration, $request);

            if (!$paymentResult['success']) {
                $payment->update([
                    'status' => 'failed',
                    'failure_reason' => $paymentResult['message']
                ]);

                return response()->json([
                    'success' => false,
                    'message' => $paymentResult['message']
                ], 400);
            }

            $payment->update(['payment_data' => $paymentResult['data']]);

            return response()->json([
                'success' => true,
                'message' => '支付订单创建成功',
                'data' => $payment->fresh()
            ], 201);
        }
    }

    /**
     * 创建微信支付订单
     */
    private function createWechatPayOrder($payment, $registration, $request)
    {
        try {
            $orderData = [
                'out_trade_no' => $payment->transaction_id,
                'description' => '会议注册费用 - ' . $registration->meeting->title,
                'total_fee' => intval($payment->amount * 100), // 转换为分
            ];

            if ($request->payment_method === 'wechat_native') {
                // Native支付（扫码支付）
                $result = $this->getWechatPayService()->createNativeOrder($orderData);

                if ($result['success']) {
                    return [
                        'success' => true,
                        'data' => [
                            'payment_type' => 'native',
                            'code_url' => $result['code_url'],
                            'out_trade_no' => $result['out_trade_no']
                        ]
                    ];
                }
            } elseif ($request->payment_method === 'wechat_jsapi') {
                // JSAPI支付（公众号支付）
                $openid = $request->openid;
                $result = $this->getWechatPayService()->createJsapiOrder($orderData, $openid);

                if ($result['success']) {
                    return [
                        'success' => true,
                        'data' => [
                            'payment_type' => 'jsapi',
                            'prepay_id' => $result['prepay_id'],
                            'pay_params' => $result['pay_params']
                        ]
                    ];
                }
            }

            return [
                'success' => false,
                'message' => '不支持的支付方式'
            ];

        } catch (\Exception $e) {
            Log::error('创建微信支付订单失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '支付服务异常'
            ];
        }
    }

    /**
     * 创建支付宝支付订单
     */
    private function createAlipayOrder($payment, $registration, $request)
    {
        try {
            $orderData = [
                'out_trade_no' => $payment->transaction_id,
                'subject' => '会议注册费用 - ' . $registration->meeting->title,
                'total_amount' => number_format($payment->amount, 2, '.', ''), // 支付宝金额格式为元
            ];
            $orderData['total_amount'] = 0.1;
            if ($request->payment_method === 'alipay_web') {
                // 网页支付
                $result = $this->getAlipayService()->createWebOrder($orderData);
            } elseif ($request->payment_method === 'alipay_wap') {
                // 手机网站支付
                $result = $this->getAlipayService()->createWapOrder($orderData);
            } else {
                return [
                    'success' => false,
                    'message' => '不支持的支付宝支付方式'
                ];
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('创建支付宝支付订单失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '支付服务异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 查询支付状态
     */
    public function status($id)
    {
        $payment = Payment::find($id);

        if (!$payment) {
            return response()->json([
                'success' => false,
                'message' => '支付记录不存在'
            ], 404);
        }

        // 查询微信支付状态
        if ($payment->status === 'pending' && str_starts_with($payment->payment_method, 'wechat')) {
            $queryResult = $this->getWechatPayService()->queryOrder($payment->transaction_id);

            if ($queryResult['success']) {
                $orderData = $queryResult['data'];

                if (isset($orderData['trade_state'])) {
                    switch ($orderData['trade_state']) {
                        case 'SUCCESS':
                            $this->updatePaymentSuccess($payment, $orderData);
                            break;
                        case 'CLOSED':
                        case 'REVOKED':
                        case 'PAYERROR':
                            $payment->update([
                                'status' => 'failed',
                                'failure_reason' => $orderData['trade_state_desc'] ?? '支付失败'
                            ]);
                            break;
                    }
                }
            }
        }

        // 查询支付宝支付状态
        if ($payment->status === 'pending' && str_starts_with($payment->payment_method, 'alipay')) {
            $queryResult = $this->getAlipayService()->queryOrder($payment->transaction_id);

            if ($queryResult['success']) {
                $orderData = $queryResult['data'];

                if (isset($orderData['trade_status'])) {
                    switch ($orderData['trade_status']) {
                        case 'TRADE_SUCCESS':
                        case 'TRADE_FINISHED':
                            $this->updatePaymentSuccess($payment, $orderData);
                            break;
                        case 'TRADE_CLOSED':
                            $payment->update([
                                'status' => 'failed',
                                'failure_reason' => '交易已关闭'
                            ]);
                            break;
                    }
                }
            }
        }

        return response()->json([
            'success' => true,
            'data' => $payment->fresh()
        ]);
    }

    /**
     * 获取我的支付记录（只显示已支付的记录）
     */
    public function myPayments(Request $request)
    {
        $user = $request->user();

        $payments = Payment::where('user_id', $user->id)
            ->where('status', 'success')
            ->with(['meetingRegistration.meeting'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $payments
        ]);
    }

    /**
     * 微信支付回调处理
     */
    public function wechatNotify(Request $request)
    {
        try {
            $headers = $request->headers->all();
            $body = $request->getContent();

            // 验证签名
            if (!$this->getWechatPayService()->verifyNotify($headers, $body)) {
                Log::error('微信支付回调签名验证失败');
                return response('FAIL', 400);
            }

            $data = json_decode($body, true);

            if (!isset($data['resource']['ciphertext'])) {
                Log::error('微信支付回调数据格式错误');
                return response('FAIL', 400);
            }

            // 解密回调数据
            $decryptedData = $this->decryptWechatNotifyData($data['resource']);

            if (!$decryptedData) {
                Log::error('微信支付回调数据解密失败');
                return response('FAIL', 400);
            }

            // 处理支付结果
            $this->handleWechatPaymentResult($decryptedData);

            return response('SUCCESS');

        } catch (\Exception $e) {
            Log::error('处理微信支付回调失败: ' . $e->getMessage());
            return response('FAIL', 500);
        }
    }

    /**
     * 处理微信支付结果
     */
    private function handleWechatPaymentResult($paymentData)
    {
        $outTradeNo = $paymentData['out_trade_no'];
        $tradeState = $paymentData['trade_state'];

        $payment = Payment::where('transaction_id', $outTradeNo)->first();

        if (!$payment) {
            Log::error("未找到支付记录: {$outTradeNo}");
            return;
        }

        if ($tradeState === 'SUCCESS') {
            $this->updatePaymentSuccess($payment, $paymentData);
        } else {
            $payment->update([
                'status' => 'failed',
                'failure_reason' => $paymentData['trade_state_desc'] ?? '支付失败'
            ]);
        }
    }

    /**
     * 更新支付成功状态
     */
    private function updatePaymentSuccess($payment, $paymentData)
    {
        $payment->update([
            'status' => 'success',
            'paid_at' => now(),
            'payment_data' => array_merge($payment->payment_data ?? [], $paymentData)
        ]);

        // 更新注册状态
        $payment->meetingRegistration->update([
            'payment_status' => 'paid'
        ]);

        // 升级用户为付费用户，并根据注册类型更新会员类型
        $user = $payment->user;
        $registrationType = $payment->meetingRegistration->registration_type;
        $user->upgradeToPaidUser($payment->amount, $registrationType);

        Log::info("支付成功: {$payment->transaction_id}, 用户 {$user->id} 已升级为付费用户，会员类型: {$registrationType}");
    }

    /**
     * 上传转账截图
     */
    public function uploadTransferScreenshot(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'screenshot' => 'required|image|mimes:jpeg,png,jpg|max:5120', // 最大5MB
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '文件验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $payment = Payment::find($id);
        if (!$payment) {
            return response()->json([
                'success' => false,
                'message' => '支付记录不存在'
            ], 404);
        }

        // 验证支付记录属于当前用户
        if ($payment->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => '无权限操作此支付记录'
            ], 403);
        }

        // 验证是否为线下支付
        if ($payment->payment_method !== 'offline_transfer') {
            return response()->json([
                'success' => false,
                'message' => '此支付方式不支持上传截图'
            ], 400);
        }

        try {
            // 使用OSS服务上传文件
            $ossService = new OssService();
            $file = $request->file('screenshot');
            $filename = 'transfer_' . $payment->id . '_' . time() . '.' . $file->getClientOriginalExtension();

            $uploadResult = $ossService->uploadFile($file, 'transfer_screenshots', $filename);

            if (!$uploadResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $uploadResult['message']
                ], 500);
            }

            // 更新支付记录 - 上传成功后自动审核通过
            $payment->update([
                'transfer_screenshot' => $uploadResult['data']['object_key'],
                'review_status' => 'approved',
                'status' => 'success',
                'reviewed_by' => null, // 系统自动审核
                'reviewed_at' => now(),
                'paid_at' => now(),
                'review_notes' => '截图上传成功，系统自动审核通过'
            ]);

            // 更新注册状态
            $payment->meetingRegistration->update([
                'payment_status' => 'paid'
            ]);

            // 升级用户为付费用户
            $user = $payment->user;
            $registrationType = $payment->meetingRegistration->registration_type;
            $user->upgradeToPaidUser($payment->amount, $registrationType);

            Log::info("转账截图上传成功并自动审核通过: {$payment->transaction_id}, 用户 {$user->id} 已升级为付费用户");

            return response()->json([
                'success' => true,
                'message' => '转账截图上传成功，支付已确认！',
                'data' => [
                    'screenshot_url' => $uploadResult['data']['url'],
                    'review_status' => 'approved',
                    'payment_status' => 'success'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('上传转账截图失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '文件上传失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 审核线下支付
     */
    public function reviewOfflinePayment(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:approve,reject',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $payment = Payment::find($id);
        if (!$payment) {
            return response()->json([
                'success' => false,
                'message' => '支付记录不存在'
            ], 404);
        }

        // 验证是否为线下支付且待审核
        if ($payment->payment_method !== 'offline_transfer' || $payment->review_status !== 'pending_review') {
            return response()->json([
                'success' => false,
                'message' => '此支付记录不可审核'
            ], 400);
        }

        $user = $request->user();
        $action = $request->action;

        try {
            if ($action === 'approve') {
                // 审核通过
                $payment->update([
                    'status' => 'success',
                    'review_status' => 'approved',
                    'review_notes' => $request->notes,
                    'reviewed_by' => $user->id,
                    'reviewed_at' => now(),
                    'paid_at' => now()
                ]);

                // 更新注册状态
                $payment->meetingRegistration->update([
                    'payment_status' => 'paid'
                ]);

                // 升级用户为付费用户
                $paymentUser = $payment->user;
                $registrationType = $payment->meetingRegistration->registration_type;
                $paymentUser->upgradeToPaidUser($payment->amount, $registrationType);

                Log::info("线下支付审核通过: {$payment->transaction_id}, 审核人: {$user->id}");

                return response()->json([
                    'success' => true,
                    'message' => '审核通过，支付成功'
                ]);

            } else {
                // 审核拒绝
                $payment->update([
                    'status' => 'failed',
                    'review_status' => 'rejected',
                    'review_notes' => $request->notes,
                    'reviewed_by' => $user->id,
                    'reviewed_at' => now(),
                    'failure_reason' => '审核未通过: ' . ($request->notes ?? '无备注')
                ]);

                Log::info("线下支付审核拒绝: {$payment->transaction_id}, 审核人: {$user->id}, 原因: {$request->notes}");

                return response()->json([
                    'success' => true,
                    'message' => '审核已拒绝'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('审核线下支付失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '审核操作失败'
            ], 500);
        }
    }

    /**
     * 解密微信支付回调数据
     */
    private function decryptWechatNotifyData($resource)
    {
        try {
            // 这里需要实现AES-256-GCM解密
            // 由于涉及到微信支付的APIv3密钥，这里提供基础框架
            // 实际使用时需要配置APIv3密钥

            $ciphertext = $resource['ciphertext'];
            $nonce = $resource['nonce'];
            $associatedData = $resource['associated_data'];

            // TODO: 实现AES-256-GCM解密
            // $apiV3Key = config('wechat.payment.default.api_v3_key');
            // $decrypted = openssl_decrypt($ciphertext, 'aes-256-gcm', $apiV3Key, OPENSSL_RAW_DATA, $nonce, $tag, $associatedData);

            // 临时返回模拟数据用于测试
            return json_decode($ciphertext, true);

        } catch (\Exception $e) {
            Log::error('解密微信支付回调数据失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 支付宝支付回调处理
     */
    public function alipayNotify(Request $request)
    {
        Log::info('收到支付宝支付回调', $request->all());

        try {
            // 验证回调签名
            $verifyResult = $this->getAlipayService()->verifyCallback($request->all());

            if (!$verifyResult['success']) {
                Log::error('支付宝回调签名验证失败');
                return response('fail');
            }

            $callbackData = $verifyResult['data'];
            $outTradeNo = $callbackData['out_trade_no'];
            $tradeStatus = $callbackData['trade_status'];

            // 查找支付记录
            $payment = Payment::where('transaction_id', $outTradeNo)->first();

            if (!$payment) {
                Log::error('支付宝回调：找不到支付记录', ['out_trade_no' => $outTradeNo]);
                return response('fail');
            }

            // 处理支付状态
            if (in_array($tradeStatus, ['TRADE_SUCCESS', 'TRADE_FINISHED'])) {
                if ($payment->status !== 'success') {
                    $this->updatePaymentSuccess($payment, $callbackData);
                    Log::info("支付宝支付成功: {$outTradeNo}");
                }
            } elseif ($tradeStatus === 'TRADE_CLOSED') {
                $payment->update([
                    'status' => 'failed',
                    'failure_reason' => '交易已关闭'
                ]);
                Log::info("支付宝支付关闭: {$outTradeNo}");
            }

            return response('success');

        } catch (\Exception $e) {
            Log::error('处理支付宝回调异常: ' . $e->getMessage());
            return response('fail');
        }
    }

    /**
     * 支付宝支付返回处理
     */
    public function alipayReturn(Request $request)
    {
        Log::info('支付宝支付返回', $request->all());

        try {
            // 验证返回参数
            $verifyResult = $this->getAlipayService()->verifyCallback($request->all());

            if (!$verifyResult['success']) {
                return redirect()->to(env('FRONTEND_URL') . '/payment-result?status=failed&message=验证失败');
            }

            $returnData = $verifyResult['data'];
            $outTradeNo = $returnData['out_trade_no'];

            // 查找支付记录
            $payment = Payment::where('transaction_id', $outTradeNo)->first();

            if (!$payment) {
                return redirect()->to(env('FRONTEND_URL') . '/payment-result?status=failed&message=订单不存在');
            }

            // 重定向到前端支付结果页面
            return redirect()->to(env('FRONTEND_URL') . '/payment-result?status=success&payment_id=' . $payment->id);

        } catch (\Exception $e) {
            Log::error('处理支付宝返回异常: ' . $e->getMessage());
            return redirect()->to(env('FRONTEND_URL') . '/payment-result?status=failed&message=处理异常');
        }
    }
}
