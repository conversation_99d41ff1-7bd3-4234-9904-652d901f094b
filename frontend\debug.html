<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/h5/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Debug - 会议管理系统</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .debug-info {
        background: white;
        padding: 15px;
        margin: 10px 0;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      }
      .loading {
        text-align: center;
        padding: 50px;
        font-size: 18px;
        color: #666;
      }
      .error {
        color: #f44336;
        background-color: #ffebee;
        border: 1px solid #f44336;
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
      }
      .success {
        color: #4caf50;
        background-color: #e8f5e8;
        border: 1px solid #4caf50;
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
      }
      #app {
        min-height: 200px;
        border: 2px dashed #ddd;
        padding: 20px;
        margin: 20px 0;
      }
    </style>
  </head>
  <body>
    <div class="debug-info">
      <h2>🔧 Vue应用调试信息</h2>
      <div id="debug-log"></div>
    </div>

    <div class="debug-info">
      <h3>📱 Vue应用容器</h3>
      <div id="app">
        <div class="loading">⏳ Vue应用加载中...</div>
      </div>
    </div>

    <script>
      // 调试日志函数
      function debugLog(message, type = 'info') {
        const logDiv = document.getElementById('debug-log');
        const timestamp = new Date().toLocaleTimeString();
        const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : '');
        logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
        console.log(`[DEBUG] ${message}`);
      }

      // 监听错误
      window.addEventListener('error', function(e) {
        debugLog(`JavaScript错误: ${e.message} (${e.filename}:${e.lineno})`, 'error');
      });

      window.addEventListener('unhandledrejection', function(e) {
        debugLog(`Promise错误: ${e.reason}`, 'error');
      });

      // 检查基础环境
      debugLog('开始调试检查...');
      debugLog(`当前URL: ${window.location.href}`);
      debugLog(`Base URL: ${window.location.origin}/h5/`);
      
      // 检查DOM
      debugLog('检查DOM元素...');
      const appElement = document.getElementById('app');
      if (appElement) {
        debugLog('✅ #app 元素存在', 'success');
      } else {
        debugLog('❌ #app 元素不存在', 'error');
      }

      // 检查资源加载
      debugLog('检查静态资源...');
      
      // 等待一段时间后检查Vue是否挂载
      setTimeout(() => {
        const appContent = document.getElementById('app').innerHTML;
        if (appContent.includes('Vue应用加载中')) {
          debugLog('❌ Vue应用未成功挂载 - 仍显示加载中', 'error');
          debugLog('可能的原因: 1) JavaScript执行错误 2) 路由配置问题 3) 环境变量问题');
        } else {
          debugLog('✅ Vue应用已挂载', 'success');
        }
      }, 3000);

      // 检查环境变量
      debugLog('准备加载Vue应用...');
    </script>

    <!-- 加载Vue应用的资源 -->
    <link rel="stylesheet" crossorigin href="/h5/assets/css/index-5GF16OIB.css">
    <script>
      debugLog('CSS文件已加载');
    </script>
    
    <script type="module" crossorigin>
      debugLog('开始加载主JavaScript文件...');
      import('/h5/assets/js/index-BfhMU0Gw.js')
        .then(() => {
          debugLog('✅ 主JavaScript文件加载成功', 'success');
        })
        .catch(error => {
          debugLog(`❌ 主JavaScript文件加载失败: ${error.message}`, 'error');
        });
    </script>
  </body>
</html>
