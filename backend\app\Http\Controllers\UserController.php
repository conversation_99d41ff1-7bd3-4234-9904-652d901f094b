<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class UserController extends Controller
{
    /**
     * 获取用户个人信息
     */
    public function profile(Request $request)
    {
        return response()->json([
            'success' => true,
            'user' => $request->user()
        ]);
    }

    /**
     * 更新用户个人信息
     */
    public function updateProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            //'name' => 'sometimes|string|max:255',
            'real_name' => 'required|string|max:255',
            'gender' => 'required|in:male,female',
            'organization' => 'required|string|max:255',
            'will_present' => 'sometimes|boolean',
            'job_title' => 'required|string|max:255',
            'phone' => 'sometimes|string|max:20',
            //'avatar' => 'sometimes|string|max:500',
            'member_type' => 'sometimes|in:expert,student',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $user->update($request->only(['real_name','gender', 'organization', 'will_present', 'job_title', 'phone',  'member_type']));

        return response()->json([
            'success' => true,
            'message' => '个人信息更新成功',
            'user' => $user->fresh()
        ]);
    }

    /**
     * 修改密码
     */
    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'old_password' => 'required|string',
            'new_password' => 'required|string|min:6|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        if (!Hash::check($request->old_password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => '原密码错误'
            ], 400);
        }

        $user->update([
            'password' => Hash::make($request->new_password)
        ]);

        return response()->json([
            'success' => true,
            'message' => '密码修改成功'
        ]);
    }

    /**
     * 发送短信验证码
     */
    public function sendSms(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '手机号格式错误',
                'errors' => $validator->errors()
            ], 422);
        }

        $phone = $request->phone;
        $code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        // 检查发送频率限制
        $cacheKey = "sms_sent:{$phone}";
        if (Cache::has($cacheKey)) {
            return response()->json([
                'success' => false,
                'message' => '发送过于频繁，请稍后再试'
            ], 429);
        }

        // 存储验证码
        Cache::put("sms_code:{$phone}", $code, 300); // 5分钟有效
        Cache::put($cacheKey, true, 60); // 1分钟内不能重复发送

        // TODO: 这里应该调用真实的短信服务
        // 现在只是模拟发送成功
        \Log::info("发送短信验证码到 {$phone}: {$code}");

        return response()->json([
            'success' => true,
            'message' => '验证码发送成功'
        ]);
    }

    /**
     * 绑定手机号
     */
    public function bindPhone(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'code' => 'required|string|size:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $phone = $request->phone;
        $code = $request->code;

        // 验证验证码
        $cachedCode = Cache::get("sms_code:{$phone}");
        if (!$cachedCode || $cachedCode !== $code) {
            return response()->json([
                'success' => false,
                'message' => '验证码错误或已过期'
            ], 400);
        }

        // 检查手机号是否已被其他用户绑定
        $existingUser = \App\Models\User::where('phone', $phone)
            ->where('id', '!=', $request->user()->id)
            ->first();

        if ($existingUser) {
            return response()->json([
                'success' => false,
                'message' => '该手机号已被其他用户绑定'
            ], 400);
        }

        $user = $request->user();
        $user->update([
            'phone' => $phone,
            'phone_verified_at' => now()
        ]);

        // 清除验证码缓存
        Cache::forget("sms_code:{$phone}");

        return response()->json([
            'success' => true,
            'message' => '手机号绑定成功',
            'user' => $user->fresh()
        ]);
    }

    /**
     * 绑定微信
     */
    public function bindWechat(Request $request)
    {
        // 这个功能通常通过微信授权流程实现
        // 这里提供一个简化的实现
        return response()->json([
            'success' => true,
            'message' => '微信绑定功能需要通过微信授权流程实现'
        ]);
    }
}
