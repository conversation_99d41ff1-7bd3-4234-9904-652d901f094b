# 快速部署指南

## 🚀 一键部署

### Windows用户
```bash
# 1. 构建项目
npm run build

# 2. 检查部署
npm run deploy:check

# 3. 使用批处理脚本部署
deploy.bat production
```

### Linux/Mac用户
```bash
# 1. 构建项目
npm run build

# 2. 检查部署
npm run deploy:check

# 3. 使用shell脚本部署
chmod +x deploy.sh
./deploy.sh production
```

## 📋 部署前检查清单

- [ ] 修改 `.env.production` 中的API地址
- [ ] 确保后端API服务正常运行
- [ ] 检查构建是否成功 (`dist/` 目录存在)
- [ ] 运行 `npm run deploy:check` 检查配置

## 🌐 常用部署方案

### 1. 静态文件服务器
将 `dist/` 目录内容上传到：
- Nginx: `/var/www/html/`
- Apache: `/var/www/html/`
- IIS: `C:\inetpub\wwwroot\`

### 2. Docker部署
```bash
# 构建镜像
docker build -t frontend-app .

# 运行容器
docker run -d -p 80:80 frontend-app
```

### 3. 云平台部署
- **Vercel**: 连接GitHub自动部署
- **Netlify**: 拖拽 `dist/` 目录到网站
- **阿里云OSS**: 上传到静态网站托管

## ⚙️ 服务器配置

### Nginx配置要点
```nginx
# 支持Vue Router
location / {
    try_files $uri $uri/ /index.html;
}

# 静态资源缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
}
```

### Apache配置要点
```apache
# .htaccess文件
RewriteEngine On
RewriteRule ^index\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]
```

## 🔧 环境变量配置

### 生产环境 (.env.production)
```env
# 必须修改为实际的API地址
VITE_API_BASE_URL=https://your-api-domain.com/api

# 如果部署在子目录，需要修改
VITE_BASE_URL=/

# 应用标题
VITE_APP_TITLE=会议管理系统
```

## 🐛 常见问题

### 1. 页面刷新404
**原因**: 服务器不支持SPA路由
**解决**: 配置服务器将所有请求重定向到index.html

### 2. API请求失败
**原因**: API地址配置错误或CORS问题
**解决**: 检查 `.env.production` 中的API地址

### 3. 静态资源加载失败
**原因**: 基础路径配置错误
**解决**: 检查 `VITE_BASE_URL` 配置

### 4. 白屏问题
**原因**: JavaScript错误或资源路径问题
**解决**: 检查浏览器控制台错误信息

## 📞 技术支持

如遇到部署问题，请：
1. 运行 `npm run deploy:check` 检查配置
2. 查看浏览器控制台错误信息
3. 检查服务器错误日志
4. 联系技术支持团队

## 🎯 性能优化建议

1. **启用Gzip压缩** - 减少传输大小
2. **设置缓存策略** - 提高加载速度
3. **使用CDN** - 加速静态资源访问
4. **监控性能** - 定期检查加载时间

---

**提示**: 详细的部署文档请参考 `DEPLOY.md` 文件。
