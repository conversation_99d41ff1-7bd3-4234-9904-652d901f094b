<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Meeting;
use App\Models\MeetingRegistration;
use App\Models\Payment;
use Illuminate\Support\Facades\Hash;

class PaymentTestController extends Controller
{
    /**
     * 创建测试用户和会议注册
     */
    public function createTestData()
    {
        try {
            // 创建测试用户
            $user = User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => '支付测试用户',
                    'password' => Hash::make('123456'),
                    'real_name' => '张三',
                    'phone' => '13800138000',
                    'member_type' => 'expert'
                ]
            );

            // 获取第一个会议
            $meeting = Meeting::first();
            if (!$meeting) {
                return response()->json([
                    'success' => false,
                    'message' => '没有可用的会议'
                ], 404);
            }

            // 创建会议注册
            $registration = MeetingRegistration::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'meeting_id' => $meeting->id
                ],
                [
                    'registration_type' => 'expert',
                    'fee_amount' => $meeting->getCurrentFee('expert'),
                    'payment_status' => 'pending',
                    'registered_at' => now(),
                    'notes' => '测试注册'
                ]
            );

            // 生成用户token
            $token = $user->createToken('test_token')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => '测试数据创建成功',
                'data' => [
                    'user' => $user,
                    'meeting' => $meeting,
                    'registration' => $registration,
                    'token' => $token
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '创建测试数据失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 模拟支付成功回调
     */
    public function mockPaymentSuccess(Request $request)
    {
        $paymentId = $request->input('payment_id');
        
        $payment = Payment::find($paymentId);
        if (!$payment) {
            return response()->json([
                'success' => false,
                'message' => '支付记录不存在'
            ], 404);
        }

        if ($payment->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => '支付状态不正确'
            ], 400);
        }

        // 模拟支付成功
        $payment->update([
            'status' => 'success',
            'paid_at' => now(),
            'wechat_transaction_id' => 'MOCK_' . time(),
            'wechat_paid_at' => now(),
            'wechat_callback_data' => [
                'trade_state' => 'SUCCESS',
                'trade_state_desc' => '支付成功',
                'transaction_id' => 'MOCK_' . time(),
                'out_trade_no' => $payment->transaction_id,
                'amount' => [
                    'total' => intval($payment->amount * 100),
                    'currency' => 'CNY'
                ]
            ]
        ]);

        // 更新注册状态
        $payment->meetingRegistration->update([
            'payment_status' => 'paid'
        ]);

        return response()->json([
            'success' => true,
            'message' => '支付成功模拟完成',
            'data' => $payment->fresh()
        ]);
    }

    /**
     * 获取支付测试指南
     */
    public function getTestGuide()
    {
        return response()->json([
            'success' => true,
            'message' => '微信支付测试指南',
            'data' => [
                'steps' => [
                    '1. 调用 /api/test/create-test-data 创建测试数据',
                    '2. 使用返回的token进行API认证',
                    '3. 调用 /api/payments/create 创建支付订单',
                    '4. 调用 /api/test/mock-payment-success 模拟支付成功',
                    '5. 调用 /api/payments/status/{id} 查询支付状态'
                ],
                'apis' => [
                    'create_test_data' => 'GET /api/test/create-test-data',
                    'create_payment' => 'POST /api/payments/create',
                    'mock_success' => 'POST /api/test/mock-payment-success',
                    'check_status' => 'GET /api/payments/status/{id}'
                ],
                'example_payment_request' => [
                    'meeting_registration_id' => 1,
                    'payment_method' => 'wechat_native'
                ]
            ]
        ]);
    }

    /**
     * 清理测试数据
     */
    public function cleanTestData()
    {
        try {
            // 删除测试用户的相关数据
            $testUser = User::where('email', '<EMAIL>')->first();
            
            if ($testUser) {
                // 删除支付记录
                Payment::where('user_id', $testUser->id)->delete();
                
                // 删除会议注册
                MeetingRegistration::where('user_id', $testUser->id)->delete();
                
                // 删除用户token
                $testUser->tokens()->delete();
                
                // 删除用户
                $testUser->delete();
            }

            return response()->json([
                'success' => true,
                'message' => '测试数据清理完成'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '清理测试数据失败: ' . $e->getMessage()
            ], 500);
        }
    }
}
