<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MeetingMaterial;
use App\Models\Meeting;

class MeetingMaterialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 获取第一个会议ID（如果存在）
        $meeting = Meeting::first();
        $meetingId = $meeting ? $meeting->id : null;

        $materials = [
            [
                'title' => '会议议程安排',
                'description' => '详细的会议日程安排，包含各个时间段的议题和演讲者信息',
                'category' => 'meeting_materials',
                'file_path' => 'materials/meeting_agenda.pdf',
                'original_filename' => '会议议程安排.pdf',
                'file_size' => 2048576, // 2MB
                'file_type' => 'pdf',
                'download_count' => 156,
                'is_public' => false,
                'meeting_id' => $meetingId,
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'title' => '主题演讲PPT合集',
                'description' => '包含所有主题演讲的PPT文件，涵盖最新的学术研究成果',
                'category' => 'presentations',
                'file_path' => 'materials/keynote_presentations.zip',
                'original_filename' => '主题演讲PPT合集.zip',
                'file_size' => 15728640, // 15MB
                'file_type' => 'zip',
                'download_count' => 89,
                'is_public' => false,
                'meeting_id' => $meetingId,
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'title' => '会议手册',
                'description' => '会议参与者手册，包含会议介绍、参会指南、联系方式等重要信息',
                'category' => 'documents',
                'file_path' => 'materials/conference_handbook.pdf',
                'original_filename' => '会议手册.pdf',
                'file_size' => 5242880, // 5MB
                'file_type' => 'pdf',
                'download_count' => 234,
                'is_public' => true, // 公开资料
                'meeting_id' => $meetingId,
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'title' => '学术论文集',
                'description' => '本次会议收录的优秀学术论文合集，PDF格式',
                'category' => 'documents',
                'file_path' => 'materials/academic_papers.pdf',
                'original_filename' => '学术论文集.pdf',
                'file_size' => 25165824, // 24MB
                'file_type' => 'pdf',
                'download_count' => 67,
                'is_public' => false,
                'meeting_id' => $meetingId,
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'title' => '会议录像精选',
                'description' => '重要演讲和讨论环节的录像资料，高清MP4格式',
                'category' => 'videos',
                'file_path' => 'materials/conference_videos.mp4',
                'original_filename' => '会议录像精选.mp4',
                'file_size' => 104857600, // 100MB
                'file_type' => 'mp4',
                'download_count' => 45,
                'is_public' => false,
                'meeting_id' => null, // 通用资料
                'sort_order' => 5,
                'is_active' => true,
            ],
            [
                'title' => '参会证书模板',
                'description' => '参会证书的Word模板文件，可供参会者下载使用',
                'category' => 'documents',
                'file_path' => 'materials/certificate_template.docx',
                'original_filename' => '参会证书模板.docx',
                'file_size' => 1048576, // 1MB
                'file_type' => 'docx',
                'download_count' => 123,
                'is_public' => true, // 公开资料
                'meeting_id' => null,
                'sort_order' => 6,
                'is_active' => true,
            ],
        ];

        foreach ($materials as $material) {
            MeetingMaterial::create($material);
        }
    }
}
