<template>
  <div class="invoice-download">
    <el-card>
      <template #header>
        <h2>发票下载</h2>
      </template>

      <el-table :data="invoiceList" :loading="loading" style="width: 100%">
        <el-table-column prop="orderNumber" label="订单号" width="180" />
        <el-table-column prop="amount" label="金额" width="120">
          <template #default="scope">
            <span class="amount">¥{{ scope.row.amount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceTitle" label="发票抬头" />
        <el-table-column prop="issueDate" label="开票日期" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button
              v-if="scope.row.status === '已开票'"
              type="primary"
              size="small"
              @click="downloadInvoiceFile(scope.row)"
            >
              下载
            </el-button>
            <el-button 
              v-else-if="scope.row.status === '待开票'" 
              type="warning" 
              size="small"
              disabled
            >
              处理中
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { getInvoiceList, downloadInvoice } from '@/api/invoice'

const invoiceList = ref([])
const loading = ref(false)

// 获取发票列表
const fetchInvoiceList = async () => {
  loading.value = true
  try {
    const response = await getInvoiceList()
    if (response.success) {
      invoiceList.value = response.data
    } else {
      ElMessage.error('获取发票列表失败')
    }
  } catch (error) {
    console.error('获取发票列表失败:', error)
    ElMessage.error('获取发票列表失败')
  } finally {
    loading.value = false
  }
}

const getStatusType = (status) => {
  switch (status) {
    case '已开票': return 'success'
    case '待开票': return 'warning'
    case '开票失败': return 'danger'
    default: return 'info'
  }
}

const downloadInvoiceFile = async (invoice) => {
  if (!invoice.canDownload) {
    ElMessage.warning('发票尚未开具，无法下载')
    return
  }

  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在下载发票...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    const response = await downloadInvoice(invoice.id)

    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response]))
    const link = document.createElement('a')
    link.href = url
    link.download = `发票_${invoice.orderNumber}.pdf`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('发票下载成功')
  } catch (error) {
    console.error('下载发票失败:', error)
    ElMessage.error('下载发票失败')
  } finally {
    loadingInstance.close()
  }
}

// 页面加载时获取发票列表
onMounted(() => {
  fetchInvoiceList()
})
</script>

<style scoped>
.invoice-download {
  padding: 20px;
  min-height: calc(100vh - 60px);
  background-color: #f5f7fa;
}

.amount {
  font-weight: bold;
  color: #e6a23c;
}
</style>