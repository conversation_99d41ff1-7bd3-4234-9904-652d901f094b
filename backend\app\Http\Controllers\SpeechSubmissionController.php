<?php

namespace App\Http\Controllers;

use App\Models\SpeechSubmission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class SpeechSubmissionController extends Controller
{
    /**
     * 检查用户是否为付费用户
     */
    private function checkPaidUser($user)
    {
        if (!$user->is_paid_user) {
            return response()->json([
                'success' => false,
                'message' => '会议发言投稿功能仅限付费用户使用，请先完成会议注册并支付费用',
                'error_code' => 'UNPAID_USER',
                'redirect_url' => '/meeting-registration'
            ], 403);
        }
        return null;
    }

    /**
     * 获取用户的会议发言投稿列表
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // 检查是否为付费用户
        $paidCheck = $this->checkPaidUser($user);
        if ($paidCheck) return $paidCheck;

        $speechSubmissions = $user->speechSubmissions()
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($submission) {
                return [
                    'id' => $submission->id,
                    'title' => $submission->title,
                    'speaker_name' => $submission->speaker_name,
                    'speech_type' => $submission->speech_type,
                    'speech_type_label' => $submission->speech_type_label,
                    'duration' => $submission->duration,
                    'status' => $submission->status,
                    'status_label' => $submission->status_label,
                    'submitted_at' => $submission->submitted_at?->format('Y-m-d H:i:s'),
                    'created_at' => $submission->created_at->format('Y-m-d H:i:s'),
                    'can_edit' => $submission->canEdit(),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $speechSubmissions
        ]);
    }

    /**
     * 获取会议发言投稿详情
     */
    public function show(Request $request, $id)
    {
        $user = $request->user();

        // 检查是否为付费用户
        $paidCheck = $this->checkPaidUser($user);
        if ($paidCheck) return $paidCheck;

        $submission = $user->speechSubmissions()->find($id);

        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => '会议发言投稿不存在'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $submission->id,
                'title' => $submission->title,
                'abstract' => $submission->abstract,
                'speaker_name' => $submission->speaker_name,
                'speaker_title' => $submission->speaker_title,
                'speaker_institution' => $submission->speaker_institution,
                'speaker_email' => $submission->speaker_email,
                'speaker_phone' => $submission->speaker_phone,
                'speaker_bio' => $submission->speaker_bio,
                'speech_type' => $submission->speech_type,
                'speech_type_label' => $submission->speech_type_label,
                'duration' => $submission->duration,
                'topic_category' => $submission->topic_category,
                'keywords' => $submission->keywords,
                'outline' => $submission->outline,
                'target_audience' => $submission->target_audience,
                'special_requirements' => $submission->special_requirements,
                'presentation_file' => $submission->presentation_file,
                'file_url' => $submission->file_url,
                'original_filename' => $submission->original_filename,
                'file_size' => $submission->file_size,
                'status' => $submission->status,
                'status_label' => $submission->status_label,
                'review_comments' => $submission->review_comments,
                'submitted_at' => $submission->submitted_at?->format('Y-m-d H:i:s'),
                'reviewed_at' => $submission->reviewed_at?->format('Y-m-d H:i:s'),
                'created_at' => $submission->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $submission->updated_at->format('Y-m-d H:i:s'),
                'can_edit' => $submission->canEdit(),
            ]
        ]);
    }

    /**
     * 创建会议发言投稿
     */
    public function store(Request $request)
    {
        $user = $request->user();

        // 检查是否为付费用户
        $paidCheck = $this->checkPaidUser($user);
        if ($paidCheck) return $paidCheck;

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'abstract' => 'required|string',
            'speaker_name' => 'required|string|max:100',
            'speaker_title' => 'required|string|max:255',
            'speaker_institution' => 'required|string|max:255',
            'speaker_email' => 'required|email|max:255',
            'speaker_phone' => 'required|string|max:50',
            'speaker_bio' => 'required|string',
            'speech_type' => 'required|in:keynote,invited,regular',
            'duration' => 'required|integer|min:5|max:180',
            'topic_category' => 'required|string|max:255',
            'keywords' => 'required|array|min:1',
            'keywords.*' => 'required|string|max:50',
            'outline' => 'required|string',
            'target_audience' => 'nullable|string',
            'special_requirements' => 'nullable|string',
            'file' => 'nullable|file|mimes:pdf,ppt,pptx,doc,docx|max:20480', // 20MB
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['user_id'] = $user->id;

        // 处理文件上传
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('speech-submissions', $filename, 'public');

            $data['presentation_file'] = $path;
            $data['original_filename'] = $file->getClientOriginalName();
            $data['file_size'] = $file->getSize();
        }

        $submission = SpeechSubmission::create($data);

        return response()->json([
            'success' => true,
            'message' => '会议发言投稿创建成功',
            'data' => [
                'id' => $submission->id,
                'title' => $submission->title,
                'status' => $submission->status,
                'status_label' => $submission->status_label,
            ]
        ], 201);
    }

    /**
     * 更新会议发言投稿
     */
    public function update(Request $request, $id)
    {
        $user = $request->user();

        // 检查是否为付费用户
        $paidCheck = $this->checkPaidUser($user);
        if ($paidCheck) return $paidCheck;

        $submission = $user->speechSubmissions()->find($id);

        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => '会议发言投稿不存在'
            ], 404);
        }

        if (!$submission->canEdit()) {
            return response()->json([
                'success' => false,
                'message' => '当前状态下不能编辑投稿'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'abstract' => 'required|string',
            'speaker_name' => 'required|string|max:100',
            'speaker_title' => 'required|string|max:255',
            'speaker_institution' => 'required|string|max:255',
            'speaker_email' => 'required|email|max:255',
            'speaker_phone' => 'required|string|max:50',
            'speaker_bio' => 'required|string',
            'speech_type' => 'required|in:keynote,invited,regular',
            'duration' => 'required|integer|min:5|max:180',
            'topic_category' => 'required|string|max:255',
            'keywords' => 'required|array|min:1',
            'keywords.*' => 'required|string|max:50',
            'outline' => 'required|string',
            'target_audience' => 'nullable|string',
            'special_requirements' => 'nullable|string',
            'file' => 'nullable|file|mimes:pdf,ppt,pptx,doc,docx|max:20480',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        // 处理文件上传
        if ($request->hasFile('file')) {
            // 删除旧文件
            if ($submission->presentation_file) {
                Storage::disk('public')->delete($submission->presentation_file);
            }

            $file = $request->file('file');
            $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('speech-submissions', $filename, 'public');

            $data['presentation_file'] = $path;
            $data['original_filename'] = $file->getClientOriginalName();
            $data['file_size'] = $file->getSize();
        }

        $submission->update($data);

        return response()->json([
            'success' => true,
            'message' => '会议发言投稿更新成功'
        ]);
    }

    /**
     * 删除会议发言投稿
     */
    public function destroy(Request $request, $id)
    {
        $user = $request->user();

        // 检查是否为付费用户
        $paidCheck = $this->checkPaidUser($user);
        if ($paidCheck) return $paidCheck;

        $submission = $user->speechSubmissions()->find($id);

        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => '会议发言投稿不存在'
            ], 404);
        }

        if (!$submission->canEdit()) {
            return response()->json([
                'success' => false,
                'message' => '当前状态下不能删除投稿'
            ], 403);
        }

        // 删除文件
        if ($submission->presentation_file) {
            Storage::disk('public')->delete($submission->presentation_file);
        }

        $submission->delete();

        return response()->json([
            'success' => true,
            'message' => '会议发言投稿删除成功'
        ]);
    }

    /**
     * 提交会议发言投稿
     */
    public function submit(Request $request, $id)
    {
        $user = $request->user();

        // 检查是否为付费用户
        $paidCheck = $this->checkPaidUser($user);
        if ($paidCheck) return $paidCheck;

        $submission = $user->speechSubmissions()->find($id);

        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => '会议发言投稿不存在'
            ], 404);
        }

        if ($submission->status !== SpeechSubmission::STATUS_DRAFT) {
            return response()->json([
                'success' => false,
                'message' => '只有草稿状态的投稿才能提交'
            ], 403);
        }

        $submission->submit();

        return response()->json([
            'success' => true,
            'message' => '会议发言投稿提交成功'
        ]);
    }

    /**
     * 下载演示文件
     */
    public function download(Request $request, $id)
    {
        $user = $request->user();

        // 检查是否为付费用户
        $paidCheck = $this->checkPaidUser($user);
        if ($paidCheck) return $paidCheck;

        $submission = $user->speechSubmissions()->find($id);

        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => '会议发言投稿不存在'
            ], 404);
        }

        if (!$submission->presentation_file || !Storage::disk('public')->exists($submission->presentation_file)) {
            return response()->json([
                'success' => false,
                'message' => '文件不存在'
            ], 404);
        }

        return Storage::disk('public')->download($submission->presentation_file, $submission->original_filename);
    }
}
