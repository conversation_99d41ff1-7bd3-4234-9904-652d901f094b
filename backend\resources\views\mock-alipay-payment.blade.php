<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模拟支付宝支付</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #1677ff 0%, #69c0ff 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .payment-container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }
        .alipay-logo {
            width: 80px;
            height: 80px;
            background: #1677ff;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }
        .payment-title {
            font-size: 24px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }
        .payment-subtitle {
            color: #8c8c8c;
            margin-bottom: 30px;
        }
        .order-info {
            background: #f5f5f5;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }
        .order-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
        }
        .order-row:last-child {
            margin-bottom: 0;
            font-weight: 600;
            color: #1677ff;
            font-size: 18px;
        }
        .order-label {
            color: #8c8c8c;
        }
        .order-value {
            color: #262626;
        }
        .payment-buttons {
            display: flex;
            gap: 12px;
        }
        .btn {
            flex: 1;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #1677ff;
            color: white;
        }
        .btn-primary:hover {
            background: #0958d9;
        }
        .btn-secondary {
            background: #f5f5f5;
            color: #595959;
        }
        .btn-secondary:hover {
            background: #e6f4ff;
        }
        .loading {
            display: none;
            margin-top: 20px;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1677ff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="alipay-logo">支</div>
        <h1 class="payment-title">支付宝支付</h1>
        <p class="payment-subtitle">模拟支付环境</p>
        
        <div class="order-info">
            <div class="order-row">
                <span class="order-label">商品名称：</span>
                <span class="order-value">{{ $params['subject'] ?? '会议注册费用' }}</span>
            </div>
            <div class="order-row">
                <span class="order-label">订单号：</span>
                <span class="order-value">{{ $params['out_trade_no'] ?? '' }}</span>
            </div>
            <div class="order-row">
                <span class="order-label">支付金额：</span>
                <span class="order-value">¥{{ $params['total_amount'] ?? '0.00' }}</span>
            </div>
        </div>
        
        <div class="payment-buttons">
            <button class="btn btn-secondary" onclick="cancelPayment()">取消支付</button>
            <button class="btn btn-primary" onclick="confirmPayment()">确认支付</button>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>支付处理中...</p>
        </div>
    </div>

    <script>
        function confirmPayment() {
            document.getElementById('loading').style.display = 'block';
            document.querySelector('.payment-buttons').style.display = 'none';
            
            // 模拟支付处理时间
            setTimeout(() => {
                // 构造支付成功的返回参数
                const returnUrl = '{{ $params["return_url"] ?? "" }}';
                const params = new URLSearchParams({
                    out_trade_no: '{{ $params["out_trade_no"] ?? "" }}',
                    trade_no: 'MOCK_' + Date.now(),
                    total_amount: '{{ $params["total_amount"] ?? "0.00" }}',
                    trade_status: 'TRADE_SUCCESS',
                    timestamp: new Date().toISOString()
                });
                
                if (returnUrl) {
                    window.location.href = returnUrl + (returnUrl.includes('?') ? '&' : '?') + params.toString();
                } else {
                    // 如果没有返回地址，跳转到前端支付结果页面
                    window.location.href = '{{ env("FRONTEND_URL") }}/payment-result?status=success&out_trade_no={{ $params["out_trade_no"] ?? "" }}';
                }
            }, 2000);
        }
        
        function cancelPayment() {
            const returnUrl = '{{ $params["return_url"] ?? "" }}';
            if (returnUrl) {
                window.location.href = returnUrl + '?status=cancel';
            } else {
                window.location.href = '{{ env("FRONTEND_URL") }}/payment-result?status=cancel';
            }
        }
    </script>
</body>
</html>
