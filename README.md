# 会议管理系统

一个基于Vue.js前端和Laravel后端的会议管理系统，支持用户注册、会议报名、微信扫码登录和在线支付功能。

## 项目结构

```
huiyi/
├── frontend/          # Vue.js 前端项目
│   ├── src/
│   │   ├── components/
│   │   ├── views/
│   │   ├── router/
│   │   └── stores/
│   └── package.json
├── backend/           # Laravel 后端API
│   ├── app/
│   │   ├── Http/Controllers/
│   │   └── Models/
│   ├── database/
│   │   └── migrations/
│   ├── routes/
│   └── config/
└── README.md
```

## 功能特性

### 前端功能
- ✅ 用户登录/注册
- ✅ 个人信息管理
- ✅ 会议注册流程
- ✅ 支付页面
- ✅ 设置页面（密码修改、手机绑定）
- ✅ 响应式设计

### 后端功能
- ✅ Laravel 11 API框架
- ✅ MySQL数据库集成
- ✅ Laravel Sanctum认证
- ✅ 微信公众号集成（EasyWeChat）
- ✅ 用户管理API
- ✅ 会议管理API
- ✅ 支付系统API
- ✅ CORS配置

## 技术栈

### 前端
- Vue.js 3
- Element Plus UI组件库
- Vue Router 4
- Pinia状态管理
- Axios HTTP客户端
- Vite构建工具

### 后端
- Laravel 11
- PHP 8.x
- MySQL数据库
- Laravel Sanctum认证
- EasyWeChat微信SDK
- Composer包管理

## 快速开始

### 环境要求
- Node.js 16+
- PHP 8.1+
- MySQL 5.7+
- Composer

### 前端启动
```bash
cd frontend
npm install
npm run dev
```
前端服务器：http://localhost:5173

### 后端启动
```bash
cd backend
composer install
php artisan migrate
php artisan db:seed --class=MeetingSeeder
php artisan serve
```
后端服务器：http://localhost:8000

## 数据库配置

### MySQL连接信息
- 主机：rm-bp1288c4giu5xojmm7o.mysql.rds.aliyuncs.com
- 端口：3306
- 数据库：ai_test
- 用户名：ai_test
- 密码：Lb881127

### 数据表结构
- `users` - 用户表
- `meetings` - 会议表
- `meeting_registrations` - 会议注册表
- `payments` - 支付记录表

## 微信配置

### 公众号信息
- APPID：wx899ffb8f21781145
- SECRET：5afc16e76a4c1eac87fcc6d468a9059c
- TOKEN：W5utUPj43Nz2
- AES_KEY：cVrcj31dbIfZfNSJElMbQXI4Sea8iOZDshmUiWSgYdh

## API文档

详细的API测试文档请参考：`backend/API_TEST.md`

### 主要API端点
- `POST /api/register` - 用户注册
- `POST /api/login` - 用户登录
- `GET /api/meetings` - 获取会议列表
- `GET /api/wechat/qrcode` - 获取微信扫码二维码
- `POST /api/user/profile` - 更新用户信息

## 开发状态

### 已完成功能 ✅
- [x] Laravel后端API框架搭建
- [x] 数据库设计和迁移
- [x] 用户认证系统
- [x] 微信登录集成
- [x] 会议管理功能
- [x] 支付系统基础
- [x] 前端页面开发
- [x] API路由配置

### 待完善功能 🚧
- [ ] 微信支付集成
- [ ] 短信验证码服务
- [ ] 邮件通知功能
- [ ] 管理后台
- [ ] 数据统计报表
- [ ] 单元测试

## 部署说明

### 生产环境配置
1. 配置Web服务器（Nginx/Apache）
2. 设置SSL证书
3. 配置环境变量
4. 运行数据库迁移
5. 配置微信回调URL

### 注意事项
- 确保服务器支持PHP 8.1+
- 配置适当的文件权限
- 设置定时任务（如需要）
- 配置日志轮转

## 联系信息

如有问题或建议，请联系开发团队。

## 许可证

本项目采用MIT许可证。
