<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->string('wechat_transaction_id')->nullable()->after('transaction_id')->comment('微信交易号');
            $table->string('wechat_prepay_id')->nullable()->after('wechat_transaction_id')->comment('微信预支付ID');
            $table->timestamp('wechat_paid_at')->nullable()->after('paid_at')->comment('微信支付时间');
            $table->text('wechat_callback_data')->nullable()->after('payment_data')->comment('微信回调数据');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropColumn([
                'wechat_transaction_id',
                'wechat_prepay_id',
                'wechat_paid_at',
                'wechat_callback_data'
            ]);
        });
    }
};
