import request from '@/utils/request'

/**
 * 获取资料列表
 * @param {Object} params - 查询参数
 * @param {string} params.category - 资料分类
 * @param {number} params.meeting_id - 会议ID
 * @param {number} params.per_page - 每页数量
 */
export const getMaterials = (params = {}) => {
  return request.get('/materials', { params })
}

/**
 * 获取资料详情
 * @param {number} id - 资料ID
 */
export const getMaterialDetail = (id) => {
  return request.get(`/materials/${id}`)
}

/**
 * 下载资料
 * @param {number} id - 资料ID
 */
export const downloadMaterial = (id) => {
  return request.get(`/materials/${id}/download`, {
    responseType: 'blob'
  })
}

/**
 * 获取资料分类列表
 */
export const getMaterialCategories = () => {
  return request.get('/materials/categories')
}

/**
 * 获取我的下载历史
 * @param {Object} params - 查询参数
 * @param {number} params.per_page - 每页数量
 */
export const getMyDownloads = (params = {}) => {
  return request.get('/materials/my-downloads', { params })
}
