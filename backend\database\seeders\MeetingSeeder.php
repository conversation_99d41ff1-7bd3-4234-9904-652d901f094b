<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Meeting;

class MeetingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Meeting::create([
            'title' => '高血压学术会议：最新医学研究支持',
            'description' => '本次会议将邀请国内外知名专家学者，分享高血压领域的最新研究成果和临床经验。',
            'start_time' => '2025-11-15 09:00:00',
            'end_time' => '2025-11-17 17:00:00',
            'location' => '天津威海会议中心',
            'type' => 'hybrid',
            'expert_fee_early' => 2000.00,
            'expert_fee_normal' => 2800.00,
            'student_fee_early' => 1200.00,
            'student_fee_normal' => 1600.00,
            'corporate_fee_early' => 5000.00,
            'corporate_fee_normal' => 6000.00,
            'early_deadline' => '2025-09-30 23:59:59',
            'registration_deadline' => '2025-11-10 23:59:59',
            'max_participants' => 500,
            'status' => 'published',
        ]);

        Meeting::create([
            'title' => '心血管疾病防治研讨会',
            'description' => '聚焦心血管疾病的预防、诊断和治疗，探讨最新的医疗技术和治疗方案。',
            'start_time' => '2025-12-20 09:00:00',
            'end_time' => '2025-12-22 17:00:00',
            'location' => '北京国际会议中心',
            'type' => 'offline',
            'expert_fee_early' => 2000.00,
            'expert_fee_normal' => 2800.00,
            'student_fee_early' => 1200.00,
            'student_fee_normal' => 1600.00,
            'corporate_fee_early' => 5000.00,
            'corporate_fee_normal' => 6000.00,
            'early_deadline' => '2025-09-30 23:59:59',
            'registration_deadline' => '2025-12-15 23:59:59',
            'max_participants' => 300,
            'status' => 'published',
        ]);
    }
}
