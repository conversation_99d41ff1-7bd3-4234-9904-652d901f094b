<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dy_announcements_info', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->softDeletes(); // deleted_at字段
            $table->string('title')->nullable()->comment('公告标题');
            $table->text('content')->nullable()->comment('公告内容');
            $table->bigInteger('user_id')->nullable()->comment('发布者');
            $table->json('attachments')->nullable()->comment('相关附件');

            // 添加索引
            $table->index('user_id');
            $table->index('created_at');
            $table->index('deleted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dy_announcements_info');
    }
};
