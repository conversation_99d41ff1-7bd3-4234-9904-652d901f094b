<template>
  <div class="settings">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <h3>修改密码</h3>
          </template>
          
          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="100px"
          >
            <el-form-item label="原密码" prop="oldPassword">
              <el-input
                v-model="passwordForm.oldPassword"
                type="password"
                placeholder="请输入原密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                placeholder="请确认新密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" :loading="passwordLoading" @click="handleChangePassword">
                修改密码
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <h3>绑定手机号</h3>
          </template>
          
          <el-form
            ref="phoneFormRef"
            :model="phoneForm"
            :rules="phoneRules"
            label-width="100px"
          >
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="phoneForm.phone" placeholder="请输入手机号" />
            </el-form-item>
            
            <el-form-item label="验证码" prop="code">
              <div class="code-input">
                <el-input v-model="phoneForm.code" placeholder="请输入验证码" />
                <el-button 
                  :disabled="codeDisabled" 
                  @click="sendCode"
                  style="margin-left: 10px"
                >
                  {{ codeText }}
                </el-button>
              </div>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" :loading="phoneLoading" @click="handleBindPhone">
                绑定手机号
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row style="margin-top: 20px">
      <el-col :span="24">
        <el-card>
          <template #header>
            <h3>账户安全</h3>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="登录密码">
              <el-tag type="success">已设置</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="手机号">
              <span v-if="userStore.user?.phone">
                {{ userStore.user.phone }}
                <el-tag type="success" style="margin-left: 10px">已绑定</el-tag>
              </span>
              <el-tag v-else type="warning">未绑定</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="微信">
              <span v-if="userStore.user?.wechat">
                {{ userStore.user.wechat }}
                <el-tag type="success" style="margin-left: 10px">已绑定</el-tag>
              </span>
              <el-tag v-else type="warning">未绑定</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
              <span v-if="userStore.user?.email">
                {{ userStore.user.email }}
                <el-tag type="success" style="margin-left: 10px">已验证</el-tag>
              </span>
              <el-tag v-else type="warning">未验证</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()
const passwordFormRef = ref()
const phoneFormRef = ref()
const passwordLoading = ref(false)
const phoneLoading = ref(false)
const codeDisabled = ref(false)
const codeText = ref('发送验证码')
const countdown = ref(0)

const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const phoneForm = reactive({
  phone: '',
  code: ''
})

const validateConfirmPassword = (rule, value, callback) => {
  if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

const phoneRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ]
}

const handleChangePassword = async () => {
  if (!passwordFormRef.value) return
  
  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      passwordLoading.value = true
      const result = await userStore.changePassword(passwordForm)
      passwordLoading.value = false
      
      if (result.success) {
        ElMessage.success(result.message)
        passwordForm.oldPassword = ''
        passwordForm.newPassword = ''
        passwordForm.confirmPassword = ''
      } else {
        ElMessage.error(result.message)
      }
    }
  })
}

const sendCode = () => {
  if (!phoneForm.phone) {
    ElMessage.warning('请先输入手机号')
    return
  }
  
  if (!/^1[3-9]\d{9}$/.test(phoneForm.phone)) {
    ElMessage.error('请输入正确的手机号')
    return
  }
  
  // 模拟发送验证码
  codeDisabled.value = true
  countdown.value = 60
  
  const timer = setInterval(() => {
    countdown.value--
    codeText.value = `${countdown.value}s后重发`
    
    if (countdown.value <= 0) {
      clearInterval(timer)
      codeDisabled.value = false
      codeText.value = '发送验证码'
    }
  }, 1000)
  
  ElMessage.success('验证码已发送')
}

const handleBindPhone = async () => {
  if (!phoneFormRef.value) return
  
  await phoneFormRef.value.validate(async (valid) => {
    if (valid) {
      phoneLoading.value = true
      const result = await userStore.bindPhone(phoneForm)
      phoneLoading.value = false
      
      if (result.success) {
        ElMessage.success(result.message)
        phoneForm.phone = ''
        phoneForm.code = ''
      } else {
        ElMessage.error(result.message)
      }
    }
  })
}
</script>

<style scoped>
.settings {
  padding: 20px;
}

.code-input {
  display: flex;
  align-items: center;
}
</style>
