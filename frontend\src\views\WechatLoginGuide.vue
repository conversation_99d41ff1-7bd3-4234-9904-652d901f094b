<template>
  <div class="wechat-guide-container">
    <el-card>
      <template #header>
        <h2>微信扫码登录配置指南</h2>
      </template>

      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="服务器验证" description="微信公众号后台验证"></el-step>
        <el-step title="IP白名单" description="配置服务器IP白名单"></el-step>
        <el-step title="功能测试" description="测试扫码登录功能"></el-step>
      </el-steps>

      <div class="step-content">
        <!-- 步骤1：服务器验证 -->
        <div v-if="currentStep === 0" class="step-panel">
          <h3>步骤1：微信公众号服务器验证</h3>
          <el-alert type="success" :closable="false" show-icon>
            <template #title>✅ 服务器验证已完成</template>
            <p>您的微信公众号后台服务器配置已经验证成功并开启。</p>
          </el-alert>
          
          <div class="config-info">
            <h4>当前配置信息：</h4>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="服务器地址">
                http://ai.whlxyc.cn/api/wechat/callback
              </el-descriptions-item>
              <el-descriptions-item label="Token">
                W5utUPj43Nz2
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag type="success">已验证</el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>

        <!-- 步骤2：IP白名单配置 -->
        <div v-if="currentStep === 1" class="step-panel">
          <h3>步骤2：配置IP白名单</h3>
          <el-alert type="warning" :closable="false" show-icon>
            <template #title>⚠️ 需要配置IP白名单</template>
            <p>当前服务器IP需要添加到微信公众号的IP白名单中。</p>
          </el-alert>

          <div class="ip-config">
            <h4>服务器IP信息：</h4>
            <el-input
              v-model="serverIp"
              readonly
              size="large"
              style="margin: 10px 0;"
            >
              <template #prepend>服务器IP</template>
              <template #append>
                <el-button @click="copyIp">复制</el-button>
              </template>
            </el-input>

            <h4>配置步骤：</h4>
            <ol class="config-steps">
              <li>
                <strong>登录微信公众平台</strong>
                <br>访问：<a href="https://mp.weixin.qq.com/" target="_blank">https://mp.weixin.qq.com/</a>
              </li>
              <li>
                <strong>进入基本配置</strong>
                <br>左侧菜单：开发 → 基本配置
              </li>
              <li>
                <strong>修改IP白名单</strong>
                <br>找到"IP白名单"部分，点击"修改"
              </li>
              <li>
                <strong>添加服务器IP</strong>
                <br>将 <code>{{ serverIp }}</code> 添加到白名单
              </li>
              <li>
                <strong>保存配置</strong>
                <br>点击"确定"保存，等待5-10分钟生效
              </li>
            </ol>
          </div>

          <div class="test-section">
            <h4>测试IP白名单配置：</h4>
            <el-button type="primary" @click="testIpWhitelist" :loading="testing">
              测试配置
            </el-button>
            <p class="test-note">配置完成后，点击此按钮测试是否生效</p>
          </div>
        </div>

        <!-- 步骤3：功能测试 -->
        <div v-if="currentStep === 2" class="step-panel">
          <h3>步骤3：测试扫码登录功能</h3>
          <el-alert type="success" :closable="false" show-icon>
            <template #title>🎉 配置完成</template>
            <p>IP白名单配置成功，现在可以使用真实的微信扫码登录功能。</p>
          </el-alert>

          <div class="test-login">
            <h4>测试登录功能：</h4>
            <el-button type="success" size="large" @click="testWechatLogin">
              <el-icon><ChatDotRound /></el-icon>
              测试微信扫码登录
            </el-button>
          </div>

          <div class="feature-list">
            <h4>支持的功能：</h4>
            <ul>
              <li>✅ 微信扫码关注登录</li>
              <li>✅ 自动创建用户账号</li>
              <li>✅ 获取微信用户信息</li>
              <li>✅ 会议注册和支付</li>
              <li>✅ 个人信息管理</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="step-actions">
        <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
        <el-button v-if="currentStep < 2" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-if="currentStep === 2" type="success" @click="goToLogin">
          开始使用
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ChatDotRound } from '@element-plus/icons-vue'

const router = useRouter()

const currentStep = ref(1) // 从IP白名单步骤开始
const serverIp = ref('**************')
const testing = ref(false)

onMounted(() => {
  // 获取服务器IP
  fetchServerInfo()
})

const fetchServerInfo = async () => {
  try {
    const response = await fetch('/api/wechat/test-config')
    const data = await response.json()
    if (data.success) {
      serverIp.value = data.data.server_ip || '**************'
    }
  } catch (error) {
    console.error('获取服务器信息失败:', error)
  }
}

const copyIp = () => {
  navigator.clipboard.writeText(serverIp.value)
  ElMessage.success('IP地址已复制到剪贴板')
}

const testIpWhitelist = async () => {
  testing.value = true
  try {
    const response = await fetch('/api/wechat/qrcode')
    const data = await response.json()
    
    if (data.success && data.data.type === 'real') {
      ElMessage.success('IP白名单配置成功！')
      currentStep.value = 2
    } else {
      ElMessage.warning('IP白名单尚未生效，请等待5-10分钟后重试')
    }
  } catch (error) {
    ElMessage.error('测试失败，请检查配置')
  } finally {
    testing.value = false
  }
}

const testWechatLogin = () => {
  router.push('/login')
}

const nextStep = () => {
  if (currentStep.value < 2) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.wechat-guide-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.step-content {
  margin: 30px 0;
  min-height: 400px;
}

.step-panel {
  padding: 20px;
}

.config-info {
  margin: 20px 0;
}

.ip-config {
  margin: 20px 0;
}

.config-steps {
  margin: 15px 0;
  padding-left: 20px;
}

.config-steps li {
  margin: 15px 0;
  line-height: 1.6;
}

.config-steps code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
}

.test-section {
  margin: 30px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.test-note {
  margin: 10px 0;
  color: #666;
  font-size: 14px;
}

.test-login {
  margin: 20px 0;
  text-align: center;
}

.feature-list {
  margin: 20px 0;
}

.feature-list ul {
  list-style: none;
  padding: 0;
}

.feature-list li {
  margin: 8px 0;
  padding: 5px 0;
}

.step-actions {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #eee;
}
</style>
