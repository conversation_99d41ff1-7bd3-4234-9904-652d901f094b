{"name": "manager", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build --mode production", "preview": "vite preview", "preview:prod": "vite preview --mode production", "lint": "echo 'Linting...' && exit 0", "type-check": "echo 'Type checking...' && exit 0", "deploy:build": "npm run build:prod", "deploy:docker": "docker build -t frontend-app .", "deploy:docker-run": "docker run -p 80:80 frontend-app", "deploy:docker-compose": "docker-compose up -d", "deploy:check": "node deploy/check-deployment.js", "clean": "rm -rf dist node_modules/.vite", "analyze": "vite build --mode production --analyze"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "element-plus": "^2.4.4", "path": "^0.12.7", "pinia": "^2.1.7", "qrcode": "^1.5.4", "vue": "^3.5.17", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "vite": "^7.0.4", "terser": "^5.19.2"}}