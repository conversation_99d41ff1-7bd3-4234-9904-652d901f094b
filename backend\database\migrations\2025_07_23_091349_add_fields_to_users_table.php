<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('real_name')->nullable()->after('name')->comment('真实姓名');
            $table->string('phone')->nullable()->after('email')->comment('手机号');
            $table->string('wechat_openid')->nullable()->after('phone')->comment('微信OpenID');
            $table->string('wechat_unionid')->nullable()->after('wechat_openid')->comment('微信UnionID');
            $table->string('avatar')->nullable()->after('wechat_unionid')->comment('头像');
            $table->enum('member_type', ['expert', 'student'])->default('expert')->after('avatar')->comment('会员类型');
            $table->timestamp('phone_verified_at')->nullable()->after('email_verified_at')->comment('手机验证时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'real_name',
                'phone',
                'wechat_openid',
                'wechat_unionid',
                'avatar',
                'member_type',
                'phone_verified_at'
            ]);
        });
    }
};
