<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('meetings', function (Blueprint $table) {
            $table->decimal('corporate_fee_early', 8, 2)->default(5000.00)->after('student_fee_normal')->comment('企业代表早鸟价');
            $table->decimal('corporate_fee_normal', 8, 2)->default(6000.00)->after('corporate_fee_early')->comment('企业代表正常价');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('meetings', function (Blueprint $table) {
            $table->dropColumn(['corporate_fee_early', 'corporate_fee_normal']);
        });
    }
};
