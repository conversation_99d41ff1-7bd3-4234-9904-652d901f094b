version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    volumes:
      - ./deploy/nginx.conf:/etc/nginx/conf.d/default.conf:ro
    restart: unless-stopped
    networks:
      - app-network

  # 如果需要同时部署后端，可以添加backend服务
  # backend:
  #   image: your-backend-image
  #   ports:
  #     - "8000:8000"
  #   environment:
  #     - APP_ENV=production
  #   networks:
  #     - app-network

networks:
  app-network:
    driver: bridge
