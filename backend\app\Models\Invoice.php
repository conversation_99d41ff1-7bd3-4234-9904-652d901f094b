<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Invoice extends Model
{
    protected $fillable = [
        'user_id',
        'payment_id',
        'invoice_number',
        'invoice_title',
        'tax_number',
        'amount',
        'status',
        'issue_date',
        'file_path',
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'issue_date' => 'date',
    ];

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联支付记录
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * 获取发票状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            'pending' => '待开票',
            'processing' => '开票中',
            'issued' => '已开票',
            'failed' => '开票失败',
            default => '未知状态'
        };
    }

    /**
     * 检查发票是否可以下载
     */
    public function canDownload(): bool
    {
        return $this->status === 'issued' && !empty($this->file_path);
    }

    /**
     * 生成发票号码
     */
    public function generateInvoiceNumber(): string
    {
        $date = now()->format('Ymd');
        $sequence = str_pad($this->id, 6, '0', STR_PAD_LEFT);
        return "INV{$date}{$sequence}";
    }

    /**
     * 获取用户的发票列表（只包含已支付的订单）
     */
    public static function getByUser(int $userId)
    {
        return static::where('user_id', $userId)
            ->whereHas('payment', function ($query) {
                $query->where('status', 'success');
            })
            ->with(['payment'])
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
