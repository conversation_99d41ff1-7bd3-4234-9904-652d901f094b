<?php

namespace App\Http\Controllers;

use App\Models\InvoiceInfo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class InvoiceInfoController extends Controller
{
    /**
     * 获取用户的开票信息列表
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $invoiceInfos = InvoiceInfo::getByUser($user->id);

        return response()->json([
            'success' => true,
            'data' => $invoiceInfos
        ]);
    }

    /**
     * 创建开票信息
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'invoice_type' => 'required|in:personal,company',
            'invoice_title' => 'required|string|max:255',
            'tax_number' => 'required_if:invoice_type,company|nullable|string|max:255',
            'company_phone' => 'nullable|string|max:50',
            'bank_name' => 'nullable|string|max:255',
            'bank_account' => 'nullable|string|max:255',
            'contact_name' => 'nullable|string|max:100',
            'contact_phone' => 'required|string|max:50',
            'contact_email' => 'nullable|email|max:255',
            'is_default' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $data = $validator->validated();
        $data['user_id'] = $user->id;

        // 如果是个人发票，清空企业相关字段
        if ($data['invoice_type'] === 'personal') {
            $data['tax_number'] = null;
            $data['company_phone'] = null;
            $data['bank_name'] = null;
            $data['bank_account'] = null;
        }

        $invoiceInfo = InvoiceInfo::create($data);

        // 如果设置为默认，更新其他记录
        if ($data['is_default'] ?? false) {
            $invoiceInfo->setAsDefault();
        }

        return response()->json([
            'success' => true,
            'message' => '开票信息保存成功',
            'data' => $invoiceInfo
        ], 201);
    }

    /**
     * 更新开票信息
     */
    public function update(Request $request, $id)
    {
        $user = $request->user();
        $invoiceInfo = InvoiceInfo::where('user_id', $user->id)->find($id);

        if (!$invoiceInfo) {
            return response()->json([
                'success' => false,
                'message' => '开票信息不存在'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'invoice_type' => 'required|in:personal,company',
            'invoice_title' => 'required|string|max:255',
            'tax_number' => 'required_if:invoice_type,company|nullable|string|max:255',
            'company_phone' => 'nullable|string|max:50',
            'bank_name' => 'nullable|string|max:255',
            'bank_account' => 'nullable|string|max:255',
            'contact_name' => 'nullable|string|max:100',
            'contact_phone' => 'required|string|max:50',
            'contact_email' => 'nullable|email|max:255',
            'is_default' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        // 如果是个人发票，清空企业相关字段
        if ($data['invoice_type'] === 'personal') {
            $data['tax_number'] = null;
            $data['company_phone'] = null;
            $data['bank_name'] = null;
            $data['bank_account'] = null;
        }

        $invoiceInfo->update($data);

        // 如果设置为默认，更新其他记录
        if ($data['is_default'] ?? false) {
            $invoiceInfo->setAsDefault();
        }

        return response()->json([
            'success' => true,
            'message' => '开票信息更新成功',
            'data' => $invoiceInfo
        ]);
    }

    /**
     * 删除开票信息
     */
    public function destroy(Request $request, $id)
    {
        $user = $request->user();
        $invoiceInfo = InvoiceInfo::where('user_id', $user->id)->find($id);

        if (!$invoiceInfo) {
            return response()->json([
                'success' => false,
                'message' => '开票信息不存在'
            ], 404);
        }

        $invoiceInfo->delete();

        return response()->json([
            'success' => true,
            'message' => '开票信息删除成功'
        ]);
    }

    /**
     * 设置默认开票信息
     */
    public function setDefault(Request $request, $id)
    {
        $user = $request->user();
        $invoiceInfo = InvoiceInfo::where('user_id', $user->id)->find($id);

        if (!$invoiceInfo) {
            return response()->json([
                'success' => false,
                'message' => '开票信息不存在'
            ], 404);
        }

        $invoiceInfo->setAsDefault();

        return response()->json([
            'success' => true,
            'message' => '默认开票信息设置成功'
        ]);
    }
}
