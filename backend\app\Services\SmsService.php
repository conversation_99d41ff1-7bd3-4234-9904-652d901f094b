<?php

namespace App\Services;

use AlibabaCloud\SDK\Dysmsapi\V20170525\Dysmsapi;
use AlibabaCloud\SDK\Dysmsapi\V20170525\Models\SendSmsRequest;
use Darabonba\OpenApi\Models\Config;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SmsService
{
    private $client;
    private $templateCode;
    private $signName;

    public function __construct()
    {
        $config = new Config([
            'accessKeyId' => env('ALI_ACCESS_KEY_ID'),
            'accessKeySecret' => env('ALI_ACCESS_KEY_SECRET'),
            'endpoint' => 'dysmsapi.aliyuncs.com'
        ]);

        $this->client = new Dysmsapi($config);
        $this->templateCode = env('ALI_SMS_TEMPLATE');
        $this->signName = env('ALI_SMS_SIGN_NAME');
    }

    /**
     * 发送短信验证码
     */
    public function sendVerificationCode($phone, $code)
    {
        // 开发环境或测试手机号直接返回成功
        if (app()->environment('local') || $this->isTestPhone($phone)) {
            Log::info('短信发送（开发模式）', [
                'phone' => $phone,
                'code' => $code,
                'mode' => 'development'
            ]);

            return [
                'success' => true,
                'message' => '短信发送成功（开发模式）',
                'request_id' => 'dev_' . time()
            ];
        }

        try {
            $request = new SendSmsRequest([
                'phoneNumbers' => $phone,
                'signName' => $this->signName,
                'templateCode' => $this->templateCode,
                'templateParam' => json_encode(['code' => $code])
            ]);

            $response = $this->client->sendSms($request);

            Log::info('短信发送请求', [
                'phone' => $phone,
                'code' => $code,
                'response' => $response->toMap()
            ]);

            if ($response->body->code === 'OK') {
                return [
                    'success' => true,
                    'message' => '短信发送成功',
                    'request_id' => $response->body->requestId
                ];
            } else {
                Log::error('短信发送失败', [
                    'phone' => $phone,
                    'error_code' => $response->body->code,
                    'error_message' => $response->body->message
                ]);

                return [
                    'success' => false,
                    'message' => $this->getErrorMessage($response->body->code),
                    'error_code' => $response->body->code
                ];
            }

        } catch (\Exception $e) {
            Log::error('短信发送异常', [
                'phone' => $phone,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '短信发送失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 检查是否是测试手机号
     */
    private function isTestPhone($phone)
    {
        $testPhones = [
            '13800138000',
            '13800138001',
            '13800138002',
            '13800138003',
            '13800138004'
        ];

        return in_array($phone, $testPhones);
    }

    /**
     * 生成验证码
     */
    public function generateCode($length = 6)
    {
        return str_pad(random_int(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    }

    /**
     * 存储验证码到缓存
     */
    public function storeCode($phone, $code, $expireMinutes = 5)
    {
        $key = "sms_code:{$phone}";
        Cache::put($key, $code, $expireMinutes * 60);
        
        // 同时存储发送时间，用于限制发送频率
        $timeKey = "sms_time:{$phone}";
        Cache::put($timeKey, now(), 60); // 1分钟内不能重复发送
    }

    /**
     * 验证验证码
     */
    public function verifyCode($phone, $code)
    {
        $key = "sms_code:{$phone}";
        $storedCode = Cache::get($key);

        if (!$storedCode) {
            return [
                'success' => false,
                'message' => '验证码已过期或不存在'
            ];
        }

        if ($storedCode !== $code) {
            return [
                'success' => false,
                'message' => '验证码错误'
            ];
        }

        // 验证成功后删除验证码
        Cache::forget($key);

        return [
            'success' => true,
            'message' => '验证码验证成功'
        ];
    }

    /**
     * 检查是否可以发送短信（防止频繁发送）
     */
    public function canSendSms($phone)
    {
        $timeKey = "sms_time:{$phone}";
        $lastSendTime = Cache::get($timeKey);

        if ($lastSendTime && now()->diffInSeconds($lastSendTime) < 60) {
            return [
                'can_send' => false,
                'message' => '发送过于频繁，请稍后再试',
                'wait_seconds' => 60 - now()->diffInSeconds($lastSendTime)
            ];
        }

        return [
            'can_send' => true,
            'message' => '可以发送'
        ];
    }

    /**
     * 获取错误信息
     */
    private function getErrorMessage($errorCode)
    {
        $errorMessages = [
            'InvalidParameter' => '参数错误',
            'InvalidTemplateCode' => '模板代码无效',
            'InvalidSignName' => '签名名称无效',
            'InvalidPhoneNumbers' => '手机号码格式错误',
            'SignNameNotMatch' => '签名不匹配',
            'TemplateNotMatch' => '模板不匹配',
            'OutOfService' => '业务停机',
            'ProductUnsubscribe' => '产品未开通',
            'ProductNotOpen' => '产品未开通',
            'BusinessLimitControl' => '业务限流',
            'InvalidJSON' => 'JSON格式错误',
            'IspRAMPermissionDeny' => 'RAM权限不足',
            'SystemError' => '系统错误',
            'isv.PHONE_NUMBER_ILLEGAL' => '手机号码格式错误',
            'isv.TEMPLATE_MISSING_PARAMETERS' => '模板缺少参数',
            'isv.BUSINESS_LIMIT_CONTROL' => '短信发送过于频繁',
            'isv.INVALID_PARAMETERS' => '参数异常',
            'isv.SYSTEM_ERROR' => '系统错误',
            'isv.MOBILE_NUMBER_ILLEGAL' => '手机号码格式错误',
            'isv.MOBILE_COUNT_OVER_LIMIT' => '手机号码数量超过限制',
            'isv.TEMPLATE_OVER_LIMIT' => '模板变量超过限制',
            'isv.SIGN_OVER_LIMIT' => '签名超过限制',
            'isv.EXTEND_CODE_ERROR' => '扩展码错误'
        ];

        return $errorMessages[$errorCode] ?? "短信发送失败 (错误码: {$errorCode})";
    }

    /**
     * 获取短信配置状态
     */
    public function getConfigStatus()
    {
        return [
            'access_key_id' => env('ALI_ACCESS_KEY_ID') ? '已配置' : '未配置',
            'access_key_secret' => env('ALI_ACCESS_KEY_SECRET') ? '已配置' : '未配置',
            'template_code' => $this->templateCode ?: '未配置',
            'sign_name' => $this->signName ?: '未配置',
            'is_configured' => env('ALI_ACCESS_KEY_ID') && env('ALI_ACCESS_KEY_SECRET') && $this->templateCode && $this->signName
        ];
    }
}
