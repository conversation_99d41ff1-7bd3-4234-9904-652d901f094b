import request from '@/utils/request'

// 获取投稿列表
export function getSubmissionList() {
  return request({
    url: '/submissions',
    method: 'get'
  })
}

// 获取投稿详情
export function getSubmissionDetail(id) {
  return request({
    url: `/submissions/${id}`,
    method: 'get'
  })
}

// 创建投稿
export function createSubmission(data) {
  return request({
    url: '/submissions',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 更新投稿
export function updateSubmission(id, data) {
  return request({
    url: `/submissions/${id}`,
    method: 'put',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除投稿
export function deleteSubmission(id) {
  return request({
    url: `/submissions/${id}`,
    method: 'delete'
  })
}

// 提交投稿
export function submitSubmission(id) {
  return request({
    url: `/submissions/${id}/submit`,
    method: 'post'
  })
}

// 下载投稿文件
export function downloadSubmissionFile(id) {
  return request({
    url: `/submissions/${id}/download`,
    method: 'get',
    responseType: 'blob'
  })
}
