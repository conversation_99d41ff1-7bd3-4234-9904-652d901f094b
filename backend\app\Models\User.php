<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'real_name',
        'gender',
        'organization',
        'will_present',
        'job_title',
        'phone',
        'wechat_openid',
        'wechat_unionid',
        'avatar',
        'member_type',
        'is_paid_user',
        'paid_at',
        'total_paid_amount',
        'paid_meetings_count',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'phone_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_paid_user' => 'boolean',
            'paid_at' => 'datetime',
            'total_paid_amount' => 'decimal:2',
        ];
    }

    /**
     * 获取用户的会议注册
     */
    public function meetingRegistrations()
    {
        return $this->hasMany(\App\Models\MeetingRegistration::class);
    }

    /**
     * 获取用户的支付记录
     */
    public function payments()
    {
        return $this->hasMany(\App\Models\Payment::class);
    }

    /**
     * 获取用户的开票信息
     */
    public function invoiceInfo()
    {
        return $this->hasMany(\App\Models\InvoiceInfo::class);
    }

    /**
     * 获取用户的默认开票信息
     */
    public function defaultInvoiceInfo()
    {
        return $this->hasOne(\App\Models\InvoiceInfo::class)->where('is_default', true);
    }

    /**
     * 获取用户的投稿
     */
    public function submissions()
    {
        return $this->hasMany(\App\Models\Submission::class);
    }

    /**
     * 获取用户的会议发言投稿
     */
    public function speechSubmissions()
    {
        return $this->hasMany(\App\Models\SpeechSubmission::class);
    }

    /**
     * 获取用户的发票记录
     */
    public function invoices()
    {
        return $this->hasMany(\App\Models\Invoice::class);
    }

    /**
     * 升级为付费用户
     */
    public function upgradeToPaidUser($paymentAmount = 0, $memberType = null)
    {
        if (!$this->is_paid_user) {
            $updateData = [
                'is_paid_user' => true,
                'paid_at' => now(),
                'total_paid_amount' => $paymentAmount,
                'paid_meetings_count' => 1,
            ];

            // 如果提供了会员类型，则更新会员类型
            if ($memberType && in_array($memberType, ['expert', 'student', 'corporate'])) {
                $updateData['member_type'] = $memberType;
            }

            $this->update($updateData);
        } else {
            // 如果已经是付费用户，更新累计金额和会议数量
            $this->increment('total_paid_amount', $paymentAmount);
            $this->increment('paid_meetings_count');

            // 如果提供了会员类型且当前会员类型为空，则更新会员类型
            if ($memberType && !$this->member_type && in_array($memberType, ['expert', 'student', 'corporate'])) {
                $this->update(['member_type' => $memberType]);
            }
        }
    }

    /**
     * 检查是否为付费用户
     */
    public function isPaidUser(): bool
    {
        return $this->is_paid_user;
    }

    /**
     * 检查个人信息是否完整
     */
    public function hasCompleteProfile(): bool
    {
        return !empty($this->real_name) &&
               !empty($this->gender) &&
               !empty($this->organization) &&
               !empty($this->job_title) &&
               !empty($this->email);
    }

    /**
     * 获取缺失的个人信息字段
     */
    public function getMissingProfileFields(): array
    {
        $missing = [];

        if (empty($this->real_name)) {
            $missing[] = '真实姓名';
        }
        if (empty($this->gender)) {
            $missing[] = '性别';
        }
        if (empty($this->organization)) {
            $missing[] = '工作单位';
        }
        if (empty($this->job_title)) {
            $missing[] = '职称';
        }
        if (empty($this->email)) {
            $missing[] = '邮箱';
        }

        return $missing;
    }

    /**
     * 获取用户统计数据
     */
    public function getStatistics(): array
    {
        return [
            'submissions_count' => $this->submissions()->count(),
            'speech_submissions_count' => $this->speechSubmissions()->count(),
            'total_papers' => $this->submissions()->count() + $this->speechSubmissions()->count(),
            'meeting_registrations_count' => $this->meetingRegistrations()->count(),
            'paid_registrations_count' => $this->meetingRegistrations()->where('payment_status', 'paid')->count(),
            'unpaid_registrations_count' => $this->meetingRegistrations()->where('payment_status', '!=', 'paid')->count(),
        ];
    }

    /**
     * 获取用户等级
     */
    public function getUserLevel(): string
    {
        if (!$this->is_paid_user) {
            return 'free';
        }

        $meetingsCount = $this->paid_meetings_count ?? 0;
        if ($meetingsCount >= 5) {
            return 'vip';
        }
        if ($meetingsCount >= 2) {
            return 'premium';
        }
        return 'paid';
    }

    /**
     * 获取用户等级标签
     */
    public function getUserLevelLabel(): string
    {
        $level = $this->getUserLevel();
        $labels = [
            'free' => '免费用户',
            'paid' => '付费用户',
            'premium' => '高级用户',
            'vip' => 'VIP用户'
        ];

        return $labels[$level] ?? '普通用户';
    }

    /**
     * 获取用户类型标签
     */
    public function getUserTypeLabel(): string
    {
        if ($this->is_paid_user) {
            return '付费用户';
        }
        return '免费用户';
    }


}
