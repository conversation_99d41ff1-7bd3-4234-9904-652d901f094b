# 会议发言投稿系统

## 🎯 功能概述

完整的会议发言投稿系统，支持主题演讲、特邀报告、一般发言等多种类型的发言投稿，包括发言人信息管理、演示文件上传、审核流程等功能。

## 🗄️ 数据库设计

### speech_submissions 表结构
```sql
CREATE TABLE speech_submissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,                    -- 投稿用户ID
    title VARCHAR(255) NOT NULL,                -- 发言标题
    abstract TEXT NOT NULL,                     -- 发言摘要
    speaker_name VARCHAR(100) NOT NULL,         -- 发言人姓名
    speaker_title VARCHAR(255) NOT NULL,        -- 发言人职务/头衔
    speaker_institution VARCHAR(255) NOT NULL,  -- 发言人所属机构
    speaker_email VARCHAR(255) NOT NULL,        -- 发言人邮箱
    speaker_phone VARCHAR(50) NOT NULL,         -- 发言人电话
    speaker_bio TEXT NOT NULL,                  -- 发言人简介
    speech_type ENUM('keynote', 'invited', 'regular') NOT NULL, -- 发言类型
    duration INT NOT NULL,                      -- 预计发言时长（分钟）
    topic_category VARCHAR(255) NOT NULL,       -- 主题分类
    keywords JSON NOT NULL,                     -- 关键词JSON数组
    outline TEXT NOT NULL,                      -- 发言大纲
    target_audience TEXT NULL,                  -- 目标听众
    special_requirements TEXT NULL,             -- 特殊要求
    presentation_file VARCHAR(255) NULL,        -- 演示文件路径
    original_filename VARCHAR(255) NULL,        -- 原始文件名
    file_size INT NULL,                         -- 文件大小(字节)
    status ENUM('draft', 'submitted', 'under_review', 'accepted', 'rejected', 'revision_required') DEFAULT 'draft',
    review_comments TEXT NULL,                  -- 审稿意见
    submitted_at TIMESTAMP NULL,                -- 提交时间
    reviewed_at TIMESTAMP NULL,                 -- 审稿时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_speech_type (speech_type),
    INDEX idx_submitted_at (submitted_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 字段说明
- **speech_type**: 发言类型（keynote-主题演讲，invited-特邀报告，regular-一般发言）
- **duration**: 预计发言时长，范围5-180分钟
- **keywords**: JSON格式存储关键词数组
- **outline**: 详细的发言大纲
- **target_audience**: 目标听众描述
- **special_requirements**: 设备、场地等特殊要求
- **presentation_file**: 演示文件（PPT、PDF等）

## 🔧 后端API设计

### 会议发言投稿管理接口

#### 1. 获取发言投稿列表
```
GET /api/speech-submissions
Response: {
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "人工智能在医疗领域的应用与挑战",
      "speaker_name": "张教授",
      "speech_type": "keynote",
      "speech_type_label": "主题演讲",
      "duration": 45,
      "status": "submitted",
      "status_label": "已提交",
      "submitted_at": "2025-07-30 10:00:00",
      "created_at": "2025-07-29 15:30:00",
      "can_edit": false
    }
  ]
}
```

#### 2. 创建发言投稿
```
POST /api/speech-submissions
Content-Type: multipart/form-data

Body:
- title: 发言标题
- abstract: 发言摘要
- speaker_name: 发言人姓名
- speaker_title: 发言人职务/头衔
- speaker_institution: 发言人所属机构
- speaker_email: 发言人邮箱
- speaker_phone: 发言人电话
- speaker_bio: 发言人简介
- speech_type: 发言类型（keynote/invited/regular）
- duration: 预计发言时长（分钟）
- topic_category: 主题分类
- keywords: JSON格式的关键词数组
- outline: 发言大纲
- target_audience: 目标听众（可选）
- special_requirements: 特殊要求（可选）
- file: 演示文件（PDF/PPT/PPTX/DOC/DOCX，最大20MB）

Response: {
  "success": true,
  "message": "会议发言投稿创建成功",
  "data": {
    "id": 1,
    "title": "发言标题",
    "status": "draft",
    "status_label": "草稿"
  }
}
```

#### 3. 获取发言投稿详情
```
GET /api/speech-submissions/{id}
Response: {
  "success": true,
  "data": {
    "id": 1,
    "title": "发言标题",
    "abstract": "发言摘要",
    "speaker_name": "张教授",
    "speaker_title": "主任医师",
    "speaker_institution": "某医科大学",
    "speaker_email": "<EMAIL>",
    "speaker_phone": "13800138000",
    "speaker_bio": "发言人简介",
    "speech_type": "keynote",
    "speech_type_label": "主题演讲",
    "duration": 45,
    "topic_category": "人工智能",
    "keywords": ["人工智能", "医疗", "机器学习"],
    "outline": "发言大纲内容",
    "target_audience": "医疗从业者",
    "special_requirements": "需要投影仪",
    "file_url": "http://localhost:8000/storage/speech-submissions/xxx.pptx",
    "original_filename": "presentation.pptx",
    "can_edit": true,
    // ... 其他字段
  }
}
```

#### 4. 更新发言投稿
```
PUT /api/speech-submissions/{id}
Content-Type: multipart/form-data
// 参数同创建发言投稿
```

#### 5. 删除发言投稿
```
DELETE /api/speech-submissions/{id}
Response: {
  "success": true,
  "message": "会议发言投稿删除成功"
}
```

#### 6. 提交发言投稿
```
POST /api/speech-submissions/{id}/submit
Response: {
  "success": true,
  "message": "会议发言投稿提交成功"
}
```

#### 7. 下载演示文件
```
GET /api/speech-submissions/{id}/download
Response: 文件流
```

## 🎨 前端界面设计

### 1. 发言投稿列表页面 (SpeechSubmissionList.vue)
- **功能**: 显示用户的所有发言投稿
- **特点**: 
  - 表格形式展示投稿信息
  - 显示发言人、发言类型、时长等关键信息
  - 状态标签区分不同投稿状态
  - 操作按钮（查看、编辑、提交、删除）
  - 未付费用户提示界面

### 2. 发言投稿表单页面 (SpeechSubmissionForm.vue)
- **功能**: 创建和编辑发言投稿
- **特点**:
  - 分区域表单设计（基本信息、发言人信息、发言内容、演示文件）
  - 发言类型选择（主题演讲、特邀报告、一般发言）
  - 时长控制（5-180分钟）
  - 关键词标签管理
  - 演示文件上传功能
  - 完整的表单验证

### 3. 发言投稿详情页面 (SpeechSubmissionDetail.vue)
- **功能**: 查看发言投稿详细信息
- **特点**:
  - 卡片式信息展示
  - 发言人详细信息展示
  - 发言内容和大纲显示
  - 演示文件下载功能
  - 审稿意见显示
  - 操作按钮（编辑、提交）

## 📊 发言投稿状态流程

```
草稿 (draft)
    ↓ [用户提交]
已提交 (submitted)
    ↓ [管理员审核]
审稿中 (under_review)
    ↓ [审核结果]
    ├─ 已接收 (accepted)
    ├─ 已拒绝 (rejected)
    └─ 需要修改 (revision_required)
           ↓ [用户修改后重新提交]
        已提交 (submitted)
```

### 发言类型说明
- **主题演讲 (keynote)**: 会议的重要主题演讲，通常时长较长
- **特邀报告 (invited)**: 特邀专家的专题报告
- **一般发言 (regular)**: 普通参会者的发言分享

## 🔒 付费用户权限控制

### 后端权限验证
```php
private function checkPaidUser($user)
{
    if (!$user->is_paid_user) {
        return response()->json([
            'success' => false,
            'message' => '会议发言投稿功能仅限付费用户使用，请先完成会议注册并支付费用',
            'error_code' => 'UNPAID_USER',
            'redirect_url' => '/meeting-registration'
        ], 403);
    }
    return null;
}
```

### 前端权限控制
```javascript
// 检查付费用户权限
const checkPaidUser = () => {
  if (!userStore.isPaidUser) {
    ElMessage.warning('会议发言投稿功能仅限付费用户使用，请先完成会议注册并支付费用')
    router.push('/meeting-registration')
    return false
  }
  return true
}
```

### 未付费用户界面
```vue
<div v-if="!userStore.isPaidUser" class="unpaid-notice">
  <el-alert
    title="会议发言投稿功能仅限付费用户使用"
    type="warning"
    :closable="false"
    show-icon
  >
    <template #default>
      <div class="unpaid-content">
        <p>您需要先完成会议注册并支付费用后才能使用会议发言投稿功能。</p>
        <div class="unpaid-actions">
          <el-button type="primary" @click="goToRegistration">
            立即注册会议
          </el-button>
          <el-button @click="goToProfile">
            查看我的注册状态
          </el-button>
        </div>
      </div>
    </template>
  </el-alert>
</div>
```

## 🧪 测试场景

### 1. 创建发言投稿测试
**步骤**:
1. 访问会议发言投稿列表页面
2. 点击"新建发言投稿"
3. 填写完整的发言信息
4. 上传演示文件
5. 保存投稿

**预期结果**:
- ✅ 发言投稿创建成功
- ✅ 返回投稿列表
- ✅ 新投稿显示在列表中

### 2. 发言类型选择测试
**步骤**:
1. 创建投稿时选择不同发言类型
2. 检查时长限制和表单验证

**预期结果**:
- ✅ 主题演讲、特邀报告、一般发言选择正常
- ✅ 时长范围5-180分钟验证正确

### 3. 演示文件上传测试
**步骤**:
1. 上传PPT、PDF等演示文件
2. 检查文件大小限制（20MB）
3. 在详情页面下载文件

**预期结果**:
- ✅ 文件上传成功
- ✅ 文件信息正确显示
- ✅ 文件下载正常

### 4. 付费用户权限测试
**步骤**:
1. 使用未付费用户访问功能
2. 使用已付费用户访问功能

**预期结果**:
- ✅ 未付费用户显示提示界面
- ✅ 已付费用户正常使用功能

## 🎯 核心特性

### 1. 完整的发言投稿流程
- 创建 → 编辑 → 提交 → 审核 → 结果
- 多种发言类型支持
- 灵活的时长设置

### 2. 详细的发言人信息管理
- 发言人基本信息
- 职务头衔管理
- 发言人简介
- 联系方式管理

### 3. 丰富的发言内容管理
- 发言摘要和大纲
- 主题分类和关键词
- 目标听众描述
- 特殊要求说明

### 4. 演示文件管理
- 支持多种文件格式（PDF、PPT、PPTX、DOC、DOCX）
- 文件大小限制（20MB）
- 安全的文件存储和下载

### 5. 付费用户权限控制
- 前后端双重验证
- 友好的未付费用户提示
- 引导用户完成付费注册

## 🚀 扩展功能建议

### 1. 发言安排管理
- 发言时间安排
- 会议室分配
- 发言顺序管理

### 2. 发言评价系统
- 听众评价功能
- 发言质量评分
- 反馈意见收集

### 3. 发言直播支持
- 在线直播功能
- 录制回放功能
- 互动问答功能

### 4. 发言统计分析
- 发言类型统计
- 主题分类分析
- 参与度统计

## 📝 使用指南

### 用户操作流程
1. **登录系统** → 访问"会议发言投稿"菜单
2. **创建投稿** → 点击"新建发言投稿"，填写详细信息
3. **选择类型** → 根据发言性质选择合适的发言类型
4. **上传文件** → 上传演示文件（可选）
5. **提交投稿** → 确认信息无误后提交审核
6. **查看结果** → 等待审核结果，查看审稿意见

### 注意事项
- 发言投稿提交后无法修改，请仔细检查信息
- 演示文件格式限制为PDF、PPT、PPTX、DOC、DOCX
- 演示文件大小不能超过20MB
- 发言时长范围为5-180分钟
- 至少需要添加一个关键词

## 📊 总结

会议发言投稿系统提供了完整的发言投稿管理功能：

1. **功能完备**: 支持多种发言类型和完整的投稿流程
2. **信息详细**: 包含发言人、发言内容、演示文件等全面信息
3. **权限控制**: 付费用户专享功能，保护商业价值
4. **用户友好**: 清晰的界面设计和操作流程
5. **扩展性强**: 易于扩展发言安排、评价等高级功能

系统为学术会议的发言征集和管理提供了专业的技术支持，有效提升了会议组织的效率和质量。
