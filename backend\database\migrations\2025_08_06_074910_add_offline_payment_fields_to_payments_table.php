<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->string('transfer_screenshot')->nullable()->after('wechat_callback_data')->comment('转账截图路径');
            $table->enum('review_status', ['pending_review', 'approved', 'rejected'])->nullable()->after('transfer_screenshot')->comment('审核状态');
            $table->text('review_notes')->nullable()->after('review_status')->comment('审核备注');
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->after('review_notes')->comment('审核人员ID');
            $table->timestamp('reviewed_at')->nullable()->after('reviewed_by')->comment('审核时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropForeign(['reviewed_by']);
            $table->dropColumn([
                'transfer_screenshot',
                'review_status',
                'review_notes',
                'reviewed_by',
                'reviewed_at'
            ]);
        });
    }
};
