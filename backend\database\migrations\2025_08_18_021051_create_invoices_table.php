<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade')->comment('用户ID');
            $table->foreignId('payment_id')->constrained()->onDelete('cascade')->comment('支付记录ID');
            $table->string('invoice_number')->unique()->nullable()->comment('发票号码');
            $table->string('invoice_title')->comment('发票抬头');
            $table->string('tax_number')->nullable()->comment('纳税人识别号');
            $table->decimal('amount', 10, 2)->comment('发票金额');
            $table->enum('status', ['pending', 'processing', 'issued', 'failed'])->default('pending')->comment('发票状态：pending-待开票，processing-开票中，issued-已开票，failed-开票失败');
            $table->date('issue_date')->nullable()->comment('开票日期');
            $table->string('file_path')->nullable()->comment('发票文件路径');
            $table->text('notes')->nullable()->comment('备注');
            $table->timestamps();

            // 添加索引
            $table->index('user_id');
            $table->index('payment_id');
            $table->index('status');
            $table->index('issue_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
