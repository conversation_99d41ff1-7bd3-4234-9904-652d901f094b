# 论文投稿系统

## 🎯 功能概述

完整的论文投稿系统，包括投稿创建、编辑、提交、审核等功能，支持文件上传和下载。

## 🗄️ 数据库设计

### submissions 表结构
```sql
CREATE TABLE submissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,                    -- 投稿用户ID
    title VARCHAR(255) NOT NULL,                -- 论文标题
    abstract TEXT NOT NULL,                     -- 论文摘要
    authors JSON NOT NULL,                      -- 作者信息JSON
    corresponding_author VARCHAR(255) NOT NULL, -- 通讯作者
    corresponding_email VARCHAR(255) NOT NULL,  -- 通讯作者邮箱
    corresponding_phone VARCHAR(255) NOT NULL,  -- 通讯作者电话
    institution VARCHAR(255) NOT NULL,          -- 所属机构
    department VARCHAR(255) NULL,               -- 所属科室/部门
    keywords JSON NOT NULL,                     -- 关键词JSON数组
    submission_type ENUM('oral', 'poster') NOT NULL, -- 投稿类型
    research_field VARCHAR(255) NOT NULL,       -- 研究领域
    funding_info TEXT NULL,                     -- 基金资助信息
    file_path VARCHAR(255) NULL,                -- 论文文件路径
    original_filename VARCHAR(255) NULL,        -- 原始文件名
    file_size INT NULL,                         -- 文件大小(字节)
    status ENUM('draft', 'submitted', 'under_review', 'accepted', 'rejected', 'revision_required') DEFAULT 'draft',
    review_comments TEXT NULL,                  -- 审稿意见
    submitted_at TIMESTAMP NULL,                -- 提交时间
    reviewed_at TIMESTAMP NULL,                 -- 审稿时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_submission_type (submission_type),
    INDEX idx_submitted_at (submitted_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 字段说明
- **authors**: JSON格式存储多个作者信息 `[{"name": "张三", "institution": "某大学", "email": "<EMAIL>"}]`
- **keywords**: JSON格式存储关键词数组 `["机器学习", "深度学习", "神经网络"]`
- **submission_type**: 投稿类型（oral-口头报告，poster-壁报展示）
- **status**: 投稿状态（draft-草稿，submitted-已提交，under_review-审稿中，accepted-已接收，rejected-已拒绝，revision_required-需要修改）

## 🔧 后端API设计

### 投稿管理接口

#### 1. 获取投稿列表
```
GET /api/submissions
Response: {
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "基于深度学习的医学图像分析",
      "submission_type": "oral",
      "submission_type_label": "口头报告",
      "status": "submitted",
      "status_label": "已提交",
      "submitted_at": "2025-07-30 10:00:00",
      "created_at": "2025-07-29 15:30:00",
      "can_edit": false
    }
  ]
}
```

#### 2. 创建投稿
```
POST /api/submissions
Content-Type: multipart/form-data

Body:
- title: 论文标题
- abstract: 论文摘要
- authors: JSON格式的作者信息
- corresponding_author: 通讯作者
- corresponding_email: 通讯邮箱
- corresponding_phone: 联系电话
- institution: 所属机构
- department: 科室/部门（可选）
- keywords: JSON格式的关键词数组
- submission_type: 投稿类型（oral/poster）
- research_field: 研究领域
- funding_info: 基金资助信息（可选）
- file: 论文文件（PDF/DOC/DOCX，最大10MB）

Response: {
  "success": true,
  "message": "投稿创建成功",
  "data": {
    "id": 1,
    "title": "论文标题",
    "status": "draft",
    "status_label": "草稿"
  }
}
```

#### 3. 获取投稿详情
```
GET /api/submissions/{id}
Response: {
  "success": true,
  "data": {
    "id": 1,
    "title": "论文标题",
    "abstract": "论文摘要",
    "authors": [
      {
        "name": "张三",
        "institution": "某大学",
        "email": "<EMAIL>"
      }
    ],
    "keywords": ["机器学习", "深度学习"],
    "file_url": "http://localhost:8000/storage/submissions/xxx.pdf",
    "original_filename": "paper.pdf",
    "can_edit": true,
    // ... 其他字段
  }
}
```

#### 4. 更新投稿
```
PUT /api/submissions/{id}
Content-Type: multipart/form-data
// 参数同创建投稿
```

#### 5. 删除投稿
```
DELETE /api/submissions/{id}
Response: {
  "success": true,
  "message": "投稿删除成功"
}
```

#### 6. 提交投稿
```
POST /api/submissions/{id}/submit
Response: {
  "success": true,
  "message": "投稿提交成功"
}
```

#### 7. 下载投稿文件
```
GET /api/submissions/{id}/download
Response: 文件流
```

## 🎨 前端界面设计

### 1. 投稿列表页面 (SubmissionList.vue)
- **功能**: 显示用户的所有投稿
- **特点**: 
  - 表格形式展示投稿信息
  - 状态标签区分不同投稿状态
  - 操作按钮（查看、编辑、提交、删除）
  - 空状态提示

### 2. 投稿表单页面 (SubmissionForm.vue)
- **功能**: 创建和编辑投稿
- **特点**:
  - 分区域表单设计（基本信息、作者信息、通讯作者信息、机构信息、其他信息）
  - 动态作者管理（添加/删除作者）
  - 关键词标签管理
  - 文件上传功能
  - 表单验证

### 3. 投稿详情页面 (SubmissionDetail.vue)
- **功能**: 查看投稿详细信息
- **特点**:
  - 卡片式信息展示
  - 作者信息表格
  - 文件下载功能
  - 审稿意见显示
  - 操作按钮（编辑、提交）

## 📊 投稿状态流程

```
草稿 (draft)
    ↓ [用户提交]
已提交 (submitted)
    ↓ [管理员审核]
审稿中 (under_review)
    ↓ [审核结果]
    ├─ 已接收 (accepted)
    ├─ 已拒绝 (rejected)
    └─ 需要修改 (revision_required)
           ↓ [用户修改后重新提交]
        已提交 (submitted)
```

### 状态权限说明
- **draft**: 可编辑、可删除、可提交
- **submitted**: 只读，等待审核
- **under_review**: 只读，审核中
- **accepted**: 只读，已接收
- **rejected**: 只读，已拒绝
- **revision_required**: 可编辑、可重新提交

## 🧪 测试场景

### 1. 创建投稿测试
**步骤**:
1. 访问投稿列表页面
2. 点击"新建投稿"
3. 填写完整的投稿信息
4. 上传论文文件
5. 保存投稿

**预期结果**:
- ✅ 投稿创建成功
- ✅ 返回投稿列表
- ✅ 新投稿显示在列表中

### 2. 编辑投稿测试
**步骤**:
1. 在投稿列表中点击"编辑"
2. 修改投稿信息
3. 更新文件
4. 保存修改

**预期结果**:
- ✅ 投稿更新成功
- ✅ 修改内容正确保存

### 3. 提交投稿测试
**步骤**:
1. 选择草稿状态的投稿
2. 点击"提交"按钮
3. 确认提交

**预期结果**:
- ✅ 投稿状态变为"已提交"
- ✅ 不能再编辑投稿

### 4. 文件上传下载测试
**步骤**:
1. 上传PDF文件
2. 保存投稿
3. 在详情页面下载文件

**预期结果**:
- ✅ 文件上传成功
- ✅ 文件信息正确显示
- ✅ 文件下载正常

## 🎯 核心特性

### 1. 完整的投稿流程
- 创建 → 编辑 → 提交 → 审核 → 结果
- 状态驱动的权限控制
- 完整的操作日志

### 2. 灵活的作者管理
- 支持多作者投稿
- 动态添加/删除作者
- 通讯作者单独管理

### 3. 文件管理
- 支持PDF、DOC、DOCX格式
- 文件大小限制（10MB）
- 安全的文件存储和下载

### 4. 用户友好的界面
- 响应式设计
- 清晰的状态标识
- 直观的操作流程

## 🚀 扩展功能建议

### 1. 管理员功能
- 投稿审核界面
- 批量操作功能
- 审稿意见管理

### 2. 通知系统
- 投稿状态变更通知
- 邮件提醒功能
- 系统消息推送

### 3. 统计分析
- 投稿统计报表
- 审核进度跟踪
- 数据导出功能

### 4. 高级搜索
- 按状态筛选
- 按时间范围查询
- 关键词搜索

## 📝 使用指南

### 用户操作流程
1. **登录系统** → 访问"论文投稿"菜单
2. **创建投稿** → 点击"新建投稿"，填写详细信息
3. **编辑投稿** → 在草稿状态下可以修改投稿内容
4. **提交投稿** → 确认信息无误后提交审核
5. **查看结果** → 等待审核结果，查看审稿意见

### 注意事项
- 投稿提交后无法修改，请仔细检查信息
- 文件格式限制为PDF、DOC、DOCX
- 文件大小不能超过10MB
- 至少需要一个作者和一个关键词

## 📊 总结

投稿系统提供了完整的论文投稿管理功能：

1. **数据完整**: 完善的投稿信息收集
2. **流程清晰**: 明确的投稿状态管理
3. **操作便捷**: 友好的用户界面
4. **功能完备**: 支持文件上传下载
5. **权限控制**: 基于状态的操作权限

系统为学术会议的论文征集和管理提供了强有力的技术支持。
