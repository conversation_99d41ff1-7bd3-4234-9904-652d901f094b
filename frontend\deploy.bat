@echo off
setlocal enabledelayedexpansion

REM 前端部署脚本 (Windows版本)
REM 使用方法: deploy.bat [环境]
REM 例如: deploy.bat production

set ENVIRONMENT=%1
if "%ENVIRONMENT%"=="" set ENVIRONMENT=production

echo 🚀 开始部署前端项目...
echo 环境: %ENVIRONMENT%

REM 1. 清理旧的构建文件
echo 📦 清理旧的构建文件...
if exist dist rmdir /s /q dist

REM 2. 安装依赖
echo 📦 安装依赖...
call npm ci --only=production
if errorlevel 1 (
    echo ❌ 依赖安装失败
    exit /b 1
)

REM 3. 构建项目
echo 🔨 构建项目...
if "%ENVIRONMENT%"=="production" (
    call npm run build:prod
) else (
    call npm run build
)

if errorlevel 1 (
    echo ❌ 构建失败
    exit /b 1
)

REM 4. 检查构建结果
if not exist dist (
    echo ❌ 构建失败，dist目录不存在
    exit /b 1
)

echo ✅ 构建完成
echo 📁 静态文件位于 dist\ 目录
echo 📤 可以将 dist\ 目录内容上传到你的web服务器

REM 5. 显示部署提示
echo.
echo 📋 部署提示:
echo 1. 将 dist\ 目录内容上传到web服务器根目录
echo 2. 确保web服务器支持Vue Router的history模式
echo 3. 配置正确的API地址在 .env.production 文件中
echo 4. 如果使用Nginx，参考 deploy\nginx.conf 配置文件
echo.

echo 🎉 部署流程完成!
pause
