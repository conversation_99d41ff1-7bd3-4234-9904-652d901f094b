<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 对公银行账户信息
    |--------------------------------------------------------------------------
    |
    | 用于线下转账支付的对公账户信息
    |
    */
    'bank_account' => [
        'account_name' => env('BANK_ACCOUNT_NAME', '示例科技有限公司'),
        'account_number' => env('BANK_ACCOUNT_NUMBER', '1234567890123456789'),
        'bank_name' => env('BANK_NAME', '中国工商银行'),
        'bank_branch' => env('BANK_BRANCH', '北京分行营业部'),
        'swift_code' => env('BANK_SWIFT_CODE', 'ICBKCNBJ'),
        'contact_phone' => env('BANK_CONTACT_PHONE', '************'),
        'contact_email' => env('BANK_CONTACT_EMAIL', '<EMAIL>'),
    ],

    /*
    |--------------------------------------------------------------------------
    | 支付方式配置
    |--------------------------------------------------------------------------
    */
    'methods' => [
        'wechat_native' => [
            'name' => '微信扫码支付',
            'description' => '使用微信扫码完成支付',
            'enabled' => true,
        ],
        'alipay_web' => [
            'name' => '支付宝支付',
            'description' => '使用支付宝完成支付',
            'enabled' => true,
        ],
        'offline_transfer' => [
            'name' => '线下对公转账',
            'description' => '通过银行转账到对公账户，上传转账凭证',
            'enabled' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 文件上传配置
    |--------------------------------------------------------------------------
    */
    'upload' => [
        'max_size' => 5120, // KB
        'allowed_types' => ['jpeg', 'png', 'jpg'],
        'storage_disk' => 'public',
        'storage_path' => 'transfer_screenshots',
    ],
];
