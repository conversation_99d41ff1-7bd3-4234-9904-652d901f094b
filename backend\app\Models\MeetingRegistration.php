<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class MeetingRegistration extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'meeting_id',
        'registration_type',
        'fee_amount',
        'payment_status',
        'registered_at',
        'notes',
    ];

    protected $casts = [
        'registered_at' => 'datetime',
        'fee_amount' => 'decimal:2',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联会议
     */
    public function meeting()
    {
        return $this->belongsTo(Meeting::class);
    }

    /**
     * 关联支付记录
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * 获取最新的支付记录
     */
    public function latestPayment()
    {
        return $this->hasOne(Payment::class)->latest();
    }
}
