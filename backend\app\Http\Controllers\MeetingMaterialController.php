<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\MeetingMaterial;
use App\Models\MaterialDownload;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;

class MeetingMaterialController extends Controller
{
    /**
     * 获取资料列表
     */
    public function index(Request $request)
    {
        $query = MeetingMaterial::with('meeting')
            ->active()
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc');

        // 按分类筛选
        if ($request->has('category') && $request->category) {
            $query->byCategory($request->category);
        }

        // 按会议筛选
        if ($request->has('meeting_id') && $request->meeting_id) {
            $query->byMeeting($request->meeting_id);
        }

        // 分页
        $perPage = $request->get('per_page', 15);
        $materials = $query->paginate($perPage);

        // 为每个资料添加用户权限信息
        $user = $request->user();
        $materials->getCollection()->transform(function ($material) use ($user) {
            $material->can_download = $material->canDownload($user);
            $material->formatted_file_size = $material->formatted_file_size;
            return $material;
        });

        return response()->json([
            'success' => true,
            'data' => $materials
        ]);
    }

    /**
     * 获取资料详情
     */
    public function show(Request $request, $id)
    {
        $material = MeetingMaterial::with('meeting')->find($id);

        if (!$material || !$material->is_active) {
            return response()->json([
                'success' => false,
                'message' => '资料不存在'
            ], 404);
        }

        $user = $request->user();
        $material->can_download = $material->canDownload($user);
        $material->formatted_file_size = $material->formatted_file_size;

        return response()->json([
            'success' => true,
            'data' => $material
        ]);
    }

    /**
     * 下载资料
     */
    public function download(Request $request, $id)
    {
        $material = MeetingMaterial::find($id);

        if (!$material || !$material->is_active) {
            return response()->json([
                'success' => false,
                'message' => '资料不存在'
            ], 404);
        }

        $user = $request->user();

        // 检查下载权限
        if (!$material->canDownload($user)) {
            return response()->json([
                'success' => false,
                'message' => '您需要成为付费用户才能下载此资料',
                'code' => 'PAYMENT_REQUIRED',
                'redirect_url' => '/meeting-registration'
            ], 403);
        }

        // 记录下载
        $material->recordDownload(
            $user,
            $request->ip(),
            $request->userAgent()
        );

        // 检查file_path是否为完整URL
        if (filter_var($material->file_path, FILTER_VALIDATE_URL)) {
            // 如果是完整URL，返回下载URL给前端
            return response()->json([
                'success' => true,
                'download_url' => $material->file_path,
                'filename' => $material->original_filename
            ]);
        } else {
            // 如果是相对路径，使用Storage系统下载
            try {
                return Storage::disk(config('filesystems.default'))->download(
                    $material->file_path,
                    $material->original_filename
                );
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => '文件下载失败，请稍后重试'
                ], 500);
            }
        }
    }

    /**
     * 获取资料分类列表
     */
    public function categories()
    {
        $categories = [
            'general' => '通用资料',
            'meeting_materials' => '会议资料',
            'presentations' => '演讲资料',
            'documents' => '文档资料',
            'videos' => '视频资料',
            'others' => '其他资料'
        ];

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * 获取用户下载历史
     */
    public function myDownloads(Request $request)
    {
        $user = $request->user();

        $downloads = MaterialDownload::with('material.meeting')
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $downloads
        ]);
    }
}
